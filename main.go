package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"resume-server/config"
	"resume-server/internal/pkg"
	"resume-server/internal/wire"

	// 导入生成的swagger文档
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"resume-server/docs"
)

// @title Resume Server API
// @version 1.0
// @description Resume Server API documentation
// @host localhost:8082

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name Apache 2.0
// @license.url http://www.apache.org/licenses/LICENSE-2.0.html

// @host localhost:8082
// @BasePath /
// @schemes http https

// 版本信息，会在编译时注入
var (
	BuildTime  string
	GitVersion string
)

func main() {
	// 设置时区为东八区（Asia/Shanghai）
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		fmt.Printf("加载时区失败: %v\n", err)
		os.Exit(1)
	}
	time.Local = loc

	// 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		fmt.Printf("加载配置失败: %v\n", err)
		os.Exit(1)
	}

	// 初始化日志
	if err := pkg.InitLogger(cfg); err != nil {
		fmt.Printf("初始化日志失败: %v\n", err)
		os.Exit(1)
	}
	defer pkg.Logger.Sync()

	// 初始化验证器
	if err := pkg.InitValidator(); err != nil {
		pkg.Fatal("初始化验证器失败", zap.Error(err))
		os.Exit(1)
	}
	pkg.Info("验证器初始化成功")

	// 初始化数据库（如果不是跳过数据库模式）
	skipDBInit := os.Getenv("SKIP_DB_INIT") == "true"
	if !skipDBInit {
		if err := pkg.InitDB(cfg); err != nil {
			pkg.Fatal("初始化数据库失败", zap.Error(err))
			os.Exit(1)
		}
		pkg.Info("数据库初始化成功")

		// 根据配置决定是否自动迁移数据库
		if cfg.Database.AutoMigrate {
			if err := pkg.AutoMigrateDB(); err != nil {
				pkg.Warn("自动迁移数据库失败", zap.Error(err))
			}
		} else if cfg.App.Env == "development" && cfg.App.Debug {
			// 如果是开发环境且开启了Debug模式，也可以自动迁移
			if err := pkg.AutoMigrateDB(); err != nil {
				pkg.Warn("自动迁移数据库失败", zap.Error(err))
			}
		}
	} else {
		pkg.Warn("跳过数据库初始化")
	}

	// 初始化Redis
	if err := pkg.InitRedis(cfg); err != nil {
		pkg.Fatal("初始化Redis失败", zap.Error(err))
		os.Exit(1)
	}
	pkg.Info("Redis初始化成功")
	defer pkg.CloseRedis()

	// 设置Gin模式
	if cfg.App.Debug {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	// 设置Swagger信息
	docs.SwaggerInfo.Title = "Resume Server API"
	docs.SwaggerInfo.Description = "Resume Server 的后端API文档"
	docs.SwaggerInfo.Version = "1.0"
	docs.SwaggerInfo.Host = fmt.Sprintf("localhost:%d", cfg.App.Port)
	docs.SwaggerInfo.BasePath = "/"
	docs.SwaggerInfo.Schemes = []string{"http", "https"}

	// 创建Gin引擎
	engine := gin.Default()

	// 构建包含定时任务的应用程序
	app, err := wire.BuildApplicationWithCron()
	if err != nil {
		log.Fatalf("构建应用程序失败: %v", err)
	}

	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 启动定时任务
	if err := app.CronService.Start(ctx); err != nil {
		pkg.Fatal("启动定时任务失败", zap.Error(err))
		os.Exit(1)
	}
	defer func() {
		if err := app.CronService.Stop(); err != nil {
			pkg.Error("停止定时任务失败", zap.Error(err))
		}
	}()

	// 初始化路由
	app.Router.Initialize(engine)

	// 注册 Swagger
	engine.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// 记录版本信息
	if GitVersion != "" {
		pkg.Info("服务版本信息",
			zap.String("git_version", GitVersion),
			zap.String("build_time", BuildTime))
	}

	// 启动服务器
	// 优先使用配置文件中的端口
	port := fmt.Sprintf("%d", cfg.App.Port)

	log.Printf("服务器启动在 http://localhost:%s", port)
	if err := engine.Run(":" + port); err != nil {
		log.Fatalf("启动服务器失败: %v", err)
	}

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	pkg.Info("正在关闭服务器...")
	pkg.Info("服务器已关闭")
}
