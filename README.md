# Speed Fox Server

基于 Gin 框架的 RESTful API 脚手架，采用 MVC 架构模式。

## 技术栈

- Gin: Web 框架
- GORM: ORM 数据库操作
- Google Wire: 依赖注入
- Zap: 日志处理
- Viper: 配置管理
- JWT: 身份认证

## 项目结构

```
.
├── cmd                 # 命令行工具
│   └── server          # 服务器入口
├── config              # 配置文件
├── docs                # API文档
├── internal            # 内部包
│   ├── controllers     # 控制器层
│   ├── middleware      # 中间件
│   ├── models          # 数据模型
│   ├── pkg             # 内部公共包
│   │   ├── database    # 数据库连接
│   │   ├── logger      # 日志工具
│   │   └── jwt         # JWT工具
│   ├── repository      # 数据访问层
│   └── services        # 业务逻辑层
├── scripts             # 脚本文件
└── tests               # 测试文件
```

## 快速开始

### 前置条件

- Go 1.20 或更高版本
- MySQL 5.7 或更高版本

### 配置

1. 复制 `.env.example` 为 `.env` 并根据实际情况修改配置

```bash
cp .env.example .env
```

2. 修改数据库连接配置

```
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=speed_fox
DB_USERNAME=root
DB_PASSWORD=password
```

### 安装依赖

```bash
go mod tidy
```

### 运行

```bash
go run main.go
```

或者构建后运行:

```bash
go build -o app
./app
```

## API 路由

### 用户模块

- `POST /api/users/register`: 用户注册
- `POST /api/users/login`: 用户登录
- `GET /api/users/:id`: 获取用户信息
- `PUT /api/users/:id`: 更新用户信息
- `DELETE /api/users/:id`: 删除用户
- `GET /api/users`: 获取用户列表

## 依赖注入

项目使用 Google Wire 进行依赖注入。在修改依赖关系后，需要重新生成 Wire 代码:

```bash
go install github.com/google/wire/cmd/wire@latest
wire
```

## 日志配置

日志使用 Zap 库，支持控制台和文件输出。可在 `.env` 中配置:

```
LOG_LEVEL=debug
LOG_CHANNEL=console  # 或 file
```

## 身份验证

使用 JWT 进行身份验证。在请求需要认证的 API 时，需要在 Header 中加入:

```
Authorization: Bearer <your_token>
```

## 数据库

项目使用 GORM 作为 ORM 框架，默认使用 MySQL 数据库。数据表会自动创建，表前缀为`tb_`。

## 开发建议

1. 按照 MVC 模式组织代码
2. 使用依赖注入管理组件依赖
3. 新增功能遵循以下步骤:
   - 创建模型
   - 实现仓储接口
   - 实现服务层
   - 实现控制器
   - 注册路由

## 许可证

MIT
