basePath: /
definitions:
  dto_admin.AdminChangePasswordRequest:
    properties:
      new_password:
        example: newpass123
        maxLength: 50
        minLength: 6
        type: string
      old_password:
        example: "123456"
        maxLength: 50
        minLength: 6
        type: string
    required:
    - new_password
    - old_password
    type: object
  dto_admin.AdminLoginRequest:
    properties:
      password:
        example: "123456"
        maxLength: 50
        minLength: 6
        type: string
      username:
        example: admin
        maxLength: 50
        minLength: 3
        type: string
    required:
    - password
    - username
    type: object
  dto_api.ApplyDraftRequest:
    properties:
      draft_id:
        example: 1
        minimum: 1
        type: integer
      resume_id:
        example: "1"
        minLength: 1
        type: string
    required:
    - draft_id
    - resume_id
    type: object
  dto_api.BatchValidatePrivilegeRequest:
    properties:
      privilege_types:
        description: 需要校验的权限类型列表
        items:
          $ref: '#/definitions/enum.PrivilegeType'
        minItems: 1
        type: array
    required:
    - privilege_types
    type: object
  dto_api.BindEmailRequest:
    properties:
      code:
        example: "123456"
        type: string
      email:
        example: <EMAIL>
        type: string
    required:
    - code
    - email
    type: object
  dto_api.BindPhoneRequest:
    properties:
      code:
        example: "123456"
        type: string
      phone:
        example: "13812345678"
        type: string
    required:
    - code
    - phone
    type: object
  dto_api.CheckBindWechatQrCodeStatusRequest:
    properties:
      scene_id:
        example: bind_1234567890_123456789
        type: string
    required:
    - scene_id
    type: object
  dto_api.CreateOrderRequest:
    properties:
      bd_vid:
        description: 百度投放ID
        example: bd123456
        type: string
      plan_id:
        description: 会员套餐ID
        example: 1
        type: integer
    required:
    - plan_id
    type: object
  dto_api.CreateTargetPositionRequest:
    properties:
      company_name:
        example: 阿里巴巴
        maxLength: 100
        type: string
      job_description:
        example: 负责前端页面开发...
        type: string
      job_source:
        example: Boss直聘
        maxLength: 50
        type: string
      position_name:
        example: 前端开发工程师
        maxLength: 100
        type: string
    required:
    - company_name
    - job_description
    - job_source
    - position_name
    type: object
  dto_api.GenerateResumeRequest:
    properties:
      prompt:
        description: 话术内容
        example: 我是一名软件工程师，有3年Java开发经验，熟悉Spring框架，参与过电商项目开发
        maxLength: 5000
        type: string
      template_id:
        description: 模板ID（可选）
        example: 1
        type: integer
    required:
    - prompt
    type: object
  dto_api.GenerateResumeResponse:
    properties:
      resume_id:
        description: 新创建的简历ID
        example: 123
        type: integer
    type: object
  dto_api.LoginCodeRequest:
    properties:
      code:
        example: "123456"
        type: string
      phone:
        example: "13812345678"
        type: string
    required:
    - code
    - phone
    type: object
  dto_api.LoginEmailCodeRequest:
    properties:
      code:
        example: "123456"
        type: string
      email:
        example: <EMAIL>
        type: string
    required:
    - code
    - email
    type: object
  dto_api.OptimizeResumeRequest:
    properties:
      resume_id:
        description: 简历ID
        example: 1
        type: integer
    required:
    - resume_id
    type: object
  dto_api.OptimizeResumeResponse:
    properties:
      draft_id:
        description: 简历草稿ID
        example: 456
        type: integer
    type: object
  dto_api.PromptRequest:
    properties:
      desc:
        description: 描述字段
        example: 我是一名软件工程师，有3年开发经验
        maxLength: 2000
        type: string
      module:
        allOf:
        - $ref: '#/definitions/enum.ResumeModule'
        description: 模块名称（枚举）
        example: basic_info
      prompt_type:
        allOf:
        - $ref: '#/definitions/enum.PromptType'
        description: 提示词类型（枚举）
        example: generate
      resume_id:
        description: 简历ID
        example: 1
        type: integer
    required:
    - desc
    - module
    - prompt_type
    - resume_id
    type: object
  dto_api.QrCodeStatusRequest:
    properties:
      scene_id:
        example: login_1234567890_123456
        type: string
    required:
    - scene_id
    type: object
  dto_api.QueryOrderStatusRequest:
    properties:
      order_no:
        description: 订单号
        example: AL00011234567890
        type: string
    required:
    - order_no
    type: object
  dto_api.SaveResumeRequest:
    properties:
      basic_info:
        $ref: '#/definitions/models.BasicInfo'
      completion_rate:
        example: 75%
        type: string
      custom_modules:
        items:
          $ref: '#/definitions/models.CustomModule'
        type: array
      education:
        $ref: '#/definitions/models.Education'
      honors:
        $ref: '#/definitions/models.Honors'
      other:
        $ref: '#/definitions/models.Other'
      personal_summary:
        $ref: '#/definitions/models.PersonalSummary'
      portfolio:
        $ref: '#/definitions/models.Portfolio'
      project:
        $ref: '#/definitions/models.Project'
      research:
        $ref: '#/definitions/models.Research'
      resume_id:
        example: "1"
        minLength: 1
        type: string
      resume_style:
        $ref: '#/definitions/models.ResumeStyle'
      skills:
        $ref: '#/definitions/models.Skills'
      slogan:
        $ref: '#/definitions/models.Slogan'
      team:
        $ref: '#/definitions/models.Team'
      work:
        $ref: '#/definitions/models.Work'
    required:
    - basic_info
    - completion_rate
    - custom_modules
    - education
    - honors
    - other
    - personal_summary
    - portfolio
    - project
    - research
    - resume_id
    - resume_style
    - skills
    - slogan
    - team
    - work
    type: object
  dto_api.ScoreResumeRequest:
    properties:
      position_id:
        description: 目标岗位ID（来自target_position表）
        example: 1
        type: integer
      resume_id:
        description: 简历ID
        example: 1
        type: integer
    required:
    - position_id
    - resume_id
    type: object
  dto_api.SendEmailCodeRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
    required:
    - email
    type: object
  dto_api.SendSMSCodeRequest:
    properties:
      phone:
        example: "13812345678"
        type: string
    required:
    - phone
    type: object
  dto_api.ShareResumeByEmailRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
      file_name:
        example: 张三的简历
        maxLength: 100
        minLength: 1
        type: string
      resume_id:
        example: "1"
        minLength: 1
        type: string
    required:
    - email
    - file_name
    - resume_id
    type: object
  dto_api.UpdateResumeNameRequest:
    properties:
      resume_id:
        example: "1"
        minLength: 1
        type: string
      resume_name:
        example: 我的新简历
        maxLength: 100
        minLength: 1
        type: string
    required:
    - resume_id
    - resume_name
    type: object
  dto_api.UpdateTargetPositionRequest:
    properties:
      company_name:
        example: 阿里巴巴
        maxLength: 100
        type: string
      id:
        example: 1
        type: integer
      job_description:
        example: 负责前端页面开发...
        type: string
      job_source:
        example: Boss直聘
        maxLength: 50
        type: string
      position_name:
        example: 前端开发工程师
        maxLength: 100
        type: string
    required:
    - company_name
    - id
    - job_description
    - job_source
    - position_name
    type: object
  dto_api.UpdateUsernameRequest:
    properties:
      username:
        example: 新用户名
        maxLength: 20
        minLength: 2
        type: string
    required:
    - username
    type: object
  dto_api.UseExampleRequest:
    properties:
      example_id:
        example: 1
        minimum: 1
        type: integer
    required:
    - example_id
    type: object
  dto_api.UseTemplateRequest:
    properties:
      resume_id:
        description: 简历ID
        example: 1
        minimum: 1
        type: integer
      template_id:
        description: 模板ID
        example: 1
        minimum: 1
        type: integer
    required:
    - resume_id
    - template_id
    type: object
  enum.ModalType:
    enum:
    - 1
    - 2
    type: integer
    x-enum-varnames:
    - ModalTypeLogin
    - ModalTypeMembership
  enum.PaymentMethod:
    enum:
    - 1
    - 2
    type: integer
    x-enum-varnames:
    - PaymentMethodWechat
    - PaymentMethodAlipay
  enum.PaymentStatus:
    enum:
    - 1
    - 2
    - 3
    - 4
    - 5
    type: integer
    x-enum-varnames:
    - PaymentStatusPending
    - PaymentStatusProcessing
    - PaymentStatusSuccess
    - PaymentStatusFailed
    - PaymentStatusTimeout
  enum.PrivilegeType:
    enum:
    - 1
    - 2
    - 3
    - 4
    - 5
    - 6
    - 7
    type: integer
    x-enum-varnames:
    - PrivilegeResumeDownload
    - PrivilegeResumeCreate
    - PrivilegeAIGenerate
    - PrivilegeAIRewrite
    - PrivilegeAIOptimize
    - PrivilegeAIDiagnose
    - PrivilegeAIOneClick
  enum.PromptType:
    enum:
    - generate
    - continue
    - professional
    - concise
    - detailed
    - generate_resume
    - optimize
    - score
    type: string
    x-enum-varnames:
    - PromptTypeGenerate
    - PromptTypeContinue
    - PromptTypeProfessional
    - PromptTypeConcise
    - PromptTypeDetailed
    - PromptTypeGenerateResume
    - PromptTypeOptimize
    - PromptTypeScore
  enum.ResumeModule:
    enum:
    - basic_info
    - education
    - work
    - project
    - research
    - team
    - portfolio
    - other
    - personal_summary
    - honors
    - skills
    - custom_modules
    - slogan
    - resume_style
    type: string
    x-enum-varnames:
    - ResumeModuleBasicInfo
    - ResumeModuleEducation
    - ResumeModuleWork
    - ResumeModuleProject
    - ResumeModuleResearch
    - ResumeModuleTeam
    - ResumeModulePortfolio
    - ResumeModuleOther
    - ResumeModulePersonalSummary
    - ResumeModuleHonors
    - ResumeModuleSkills
    - ResumeModuleCustomModules
    - ResumeModuleSlogan
    - ResumeModuleResumeStyle
  enum.UserStatus:
    enum:
    - 0
    - 1
    type: integer
    x-enum-varnames:
    - UserStatusDisabled
    - UserStatusEnabled
  enum.UserType:
    enum:
    - 1
    - 2
    - 3
    type: integer
    x-enum-varnames:
    - UserTypeGuest
    - UserTypeRegular
    - UserTypeMember
  models.BasicInfo:
    properties:
      id:
        type: string
      index:
        type: integer
      is_required:
        type: boolean
      is_visible:
        type: boolean
      item:
        $ref: '#/definitions/models.BasicInfoItem'
      name:
        type: string
      support_ai:
        type: boolean
      type:
        type: string
    type: object
  models.BasicInfoField:
    properties:
      label:
        type: string
      value: {}
    type: object
  models.BasicInfoItem:
    properties:
      avatar:
        $ref: '#/definitions/models.BasicInfoField'
      avatar_filter:
        $ref: '#/definitions/models.BasicInfoField'
      birth:
        $ref: '#/definitions/models.BasicInfoField'
      birth_type:
        $ref: '#/definitions/models.BasicInfoField'
      city:
        $ref: '#/definitions/models.BasicInfoField'
      created_at:
        $ref: '#/definitions/models.BasicInfoField'
      customize_fields:
        $ref: '#/definitions/models.BasicInfoField'
      email:
        $ref: '#/definitions/models.BasicInfoField'
      ethnicity:
        $ref: '#/definitions/models.BasicInfoField'
      gender:
        $ref: '#/definitions/models.BasicInfoField'
      gitee:
        $ref: '#/definitions/models.BasicInfoField'
      github:
        $ref: '#/definitions/models.BasicInfoField'
      height:
        $ref: '#/definitions/models.BasicInfoField'
      id:
        $ref: '#/definitions/models.BasicInfoField'
      intended_city:
        $ref: '#/definitions/models.BasicInfoField'
      job:
        $ref: '#/definitions/models.BasicInfoField'
      job_status:
        $ref: '#/definitions/models.BasicInfoField'
      marital:
        $ref: '#/definitions/models.BasicInfoField'
      max_salary:
        $ref: '#/definitions/models.BasicInfoField'
      name:
        $ref: '#/definitions/models.BasicInfoField'
      origin:
        $ref: '#/definitions/models.BasicInfoField'
      phone:
        $ref: '#/definitions/models.BasicInfoField'
      political_affiliation:
        $ref: '#/definitions/models.BasicInfoField'
      site:
        $ref: '#/definitions/models.BasicInfoField'
      updated_at:
        $ref: '#/definitions/models.BasicInfoField'
      wechat:
        $ref: '#/definitions/models.BasicInfoField'
      weight:
        $ref: '#/definitions/models.BasicInfoField'
    type: object
  models.CustomModule:
    properties:
      id:
        type: string
      index:
        type: integer
      items:
        items:
          $ref: '#/definitions/models.CustomModuleItem'
        type: array
      name:
        type: string
    type: object
  models.CustomModuleItem:
    properties:
      desc:
        $ref: '#/definitions/models.BasicInfoField'
      end_month:
        $ref: '#/definitions/models.BasicInfoField'
      id:
        type: string
      index:
        type: integer
      name:
        $ref: '#/definitions/models.BasicInfoField'
      role:
        $ref: '#/definitions/models.BasicInfoField'
      start_month:
        $ref: '#/definitions/models.BasicInfoField'
    type: object
  models.Education:
    properties:
      id:
        type: string
      index:
        type: integer
      is_visible:
        type: boolean
      item:
        items:
          $ref: '#/definitions/models.EducationItem'
        type: array
      name:
        type: string
      support_ai:
        type: boolean
      type:
        type: string
    type: object
  models.EducationItem:
    properties:
      city:
        $ref: '#/definitions/models.BasicInfoField'
      college_name:
        $ref: '#/definitions/models.BasicInfoField'
      degree:
        $ref: '#/definitions/models.BasicInfoField'
      description:
        $ref: '#/definitions/models.BasicInfoField'
      end_date:
        $ref: '#/definitions/models.BasicInfoField'
      id:
        type: string
      index:
        type: integer
      major:
        $ref: '#/definitions/models.BasicInfoField'
      school_name:
        $ref: '#/definitions/models.BasicInfoField'
      school_tags:
        $ref: '#/definitions/models.BasicInfoField'
      start_date:
        $ref: '#/definitions/models.BasicInfoField'
    type: object
  models.Honors:
    properties:
      id:
        type: string
      index:
        type: integer
      is_visible:
        type: boolean
      item:
        $ref: '#/definitions/models.HonorsItem'
      name:
        type: string
      support_ai:
        type: boolean
      type:
        type: string
    type: object
  models.HonorsItem:
    properties:
      honorWallLayout:
        $ref: '#/definitions/models.BasicInfoField'
      honorWallStyle:
        $ref: '#/definitions/models.BasicInfoField'
      values:
        $ref: '#/definitions/models.BasicInfoField'
    type: object
  models.Other:
    properties:
      id:
        type: string
      index:
        type: integer
      is_visible:
        type: boolean
      item:
        items:
          $ref: '#/definitions/models.OtherItem'
        type: array
      name:
        type: string
      support_ai:
        type: boolean
      type:
        type: string
    type: object
  models.OtherItem:
    properties:
      desc:
        $ref: '#/definitions/models.BasicInfoField'
      id:
        type: string
      index:
        type: integer
      name:
        $ref: '#/definitions/models.BasicInfoField'
    type: object
  models.PersonalSummary:
    properties:
      id:
        type: string
      index:
        type: integer
      is_visible:
        type: boolean
      item:
        $ref: '#/definitions/models.PersonalSummaryItem'
      name:
        type: string
      support_ai:
        type: boolean
      type:
        type: string
    type: object
  models.PersonalSummaryItem:
    properties:
      summary:
        $ref: '#/definitions/models.BasicInfoField'
    type: object
  models.Portfolio:
    properties:
      id:
        type: string
      index:
        type: integer
      is_visible:
        type: boolean
      item:
        items:
          $ref: '#/definitions/models.PortfolioItem'
        type: array
      name:
        type: string
      support_ai:
        type: boolean
      type:
        type: string
    type: object
  models.PortfolioItem:
    properties:
      id:
        type: string
      index:
        type: integer
      name:
        $ref: '#/definitions/models.BasicInfoField'
      url:
        $ref: '#/definitions/models.BasicInfoField'
    type: object
  models.Project:
    properties:
      id:
        type: string
      index:
        type: integer
      is_visible:
        type: boolean
      item:
        items:
          $ref: '#/definitions/models.ProjectItem'
        type: array
      name:
        type: string
      support_ai:
        type: boolean
      type:
        type: string
    type: object
  models.ProjectItem:
    properties:
      company:
        $ref: '#/definitions/models.BasicInfoField'
      desc:
        $ref: '#/definitions/models.BasicInfoField'
      end_month:
        $ref: '#/definitions/models.BasicInfoField'
      id:
        type: string
      index:
        type: integer
      name:
        $ref: '#/definitions/models.BasicInfoField'
      role:
        $ref: '#/definitions/models.BasicInfoField'
      start_month:
        $ref: '#/definitions/models.BasicInfoField'
    type: object
  models.Research:
    properties:
      id:
        type: string
      index:
        type: integer
      is_visible:
        type: boolean
      item:
        items:
          $ref: '#/definitions/models.ResearchItem'
        type: array
      name:
        type: string
      support_ai:
        type: boolean
      type:
        type: string
    type: object
  models.ResearchItem:
    properties:
      city:
        $ref: '#/definitions/models.BasicInfoField'
      department:
        $ref: '#/definitions/models.BasicInfoField'
      desc:
        $ref: '#/definitions/models.BasicInfoField'
      end_month:
        $ref: '#/definitions/models.BasicInfoField'
      id:
        type: string
      index:
        type: integer
      name:
        $ref: '#/definitions/models.BasicInfoField'
      role:
        $ref: '#/definitions/models.BasicInfoField'
      start_month:
        $ref: '#/definitions/models.BasicInfoField'
    type: object
  models.ResumeStyle:
    properties:
      avatar_layout:
        description: '''left'' | ''center'' | ''right'''
        type: string
      badge_layout:
        description: '''left'' | ''right'''
        type: string
      base_info:
        description: '''text'' | ''icon'' | ''simple'''
        type: string
      can_change_avatar_layout:
        type: boolean
      can_change_background_style:
        type: boolean
      can_change_base_info:
        type: boolean
      can_change_color:
        type: boolean
      can_change_date_align:
        type: boolean
      can_change_date_format:
        type: boolean
      can_change_font_family:
        type: boolean
      can_change_font_gray:
        type: boolean
      can_change_font_size:
        type: boolean
      can_change_header_layout:
        description: 功能开关
        type: boolean
      can_change_line_spacing:
        type: boolean
      can_change_module_spacing:
        type: boolean
      can_change_page_margin:
        type: boolean
      can_change_skills_three_columns:
        type: boolean
      can_change_title_align:
        type: boolean
      can_change_title_style:
        type: boolean
      color:
        type: string
      color_count:
        type: integer
      date_align:
        description: '''left'' | ''right'''
        type: string
      date_format:
        type: string
      font_family:
        description: 字体相关
        type: string
      font_gray:
        type: string
      font_size:
        type: string
      header_layout:
        description: '''left'' | ''center'' | ''right'''
        type: string
      layout_mode:
        type: string
      left_box_width:
        type: string
      line_spacing:
        type: string
      module_spacing:
        description: 布局相关
        type: string
      page_margin:
        type: string
      paper_style:
        type: string
      preset_colors_dual:
        items:
          items:
            type: string
          type: array
        type: array
      preset_colors_single:
        items:
          type: string
        type: array
      resume_color:
        description: 主题色相关
        type: string
      resume_color2:
        type: string
      separator:
        type: string
      title_align:
        description: '''left'' | ''center'' | ''right'' | ''justify'''
        type: string
      title_color:
        type: string
      title_row:
        type: string
      title_style:
        type: string
    type: object
  models.Skills:
    properties:
      id:
        type: string
      index:
        type: integer
      is_visible:
        type: boolean
      item:
        $ref: '#/definitions/models.SkillsItem'
      name:
        type: string
      support_ai:
        type: boolean
      type:
        type: string
    type: object
  models.SkillsItem:
    properties:
      skillLayout:
        $ref: '#/definitions/models.BasicInfoField'
      skillStyle:
        $ref: '#/definitions/models.BasicInfoField'
      values:
        $ref: '#/definitions/models.BasicInfoField'
    type: object
  models.Slogan:
    properties:
      slogan:
        $ref: '#/definitions/models.BasicInfoField'
      title:
        $ref: '#/definitions/models.BasicInfoField'
    type: object
  models.Team:
    properties:
      id:
        type: string
      index:
        type: integer
      is_visible:
        type: boolean
      item:
        items:
          $ref: '#/definitions/models.TeamItem'
        type: array
      name:
        type: string
      support_ai:
        type: boolean
      type:
        type: string
    type: object
  models.TeamItem:
    properties:
      city:
        $ref: '#/definitions/models.BasicInfoField'
      department:
        $ref: '#/definitions/models.BasicInfoField'
      desc:
        $ref: '#/definitions/models.BasicInfoField'
      end_month:
        $ref: '#/definitions/models.BasicInfoField'
      id:
        type: string
      index:
        type: integer
      name:
        $ref: '#/definitions/models.BasicInfoField'
      role:
        $ref: '#/definitions/models.BasicInfoField'
      start_month:
        $ref: '#/definitions/models.BasicInfoField'
    type: object
  models.Work:
    properties:
      id:
        type: string
      index:
        type: integer
      is_visible:
        type: boolean
      item:
        items:
          $ref: '#/definitions/models.WorkItem'
        type: array
      name:
        type: string
      support_ai:
        type: boolean
      type:
        type: string
    type: object
  models.WorkItem:
    properties:
      city:
        $ref: '#/definitions/models.BasicInfoField'
      company:
        $ref: '#/definitions/models.BasicInfoField'
      company_tags:
        $ref: '#/definitions/models.BasicInfoField'
      department:
        $ref: '#/definitions/models.BasicInfoField'
      desc:
        $ref: '#/definitions/models.BasicInfoField'
      end_month:
        $ref: '#/definitions/models.BasicInfoField'
      id:
        type: string
      index:
        type: integer
      job:
        $ref: '#/definitions/models.BasicInfoField'
      job_tags:
        $ref: '#/definitions/models.BasicInfoField'
      start_month:
        $ref: '#/definitions/models.BasicInfoField'
    type: object
  response.Response-any:
    properties:
      code:
        example: 0
        type: integer
      data: {}
      message:
        example: 操作成功
        type: string
    type: object
  response.Response-dto_api_GenerateResumeResponse:
    properties:
      code:
        example: 0
        type: integer
      data:
        $ref: '#/definitions/dto_api.GenerateResumeResponse'
      message:
        example: 操作成功
        type: string
    type: object
  response.Response-dto_api_OptimizeResumeResponse:
    properties:
      code:
        example: 0
        type: integer
      data:
        $ref: '#/definitions/dto_api.OptimizeResumeResponse'
      message:
        example: 操作成功
        type: string
    type: object
  response.Response-vo_api_BatchValidatePrivilegeResponse:
    properties:
      code:
        example: 0
        type: integer
      data:
        $ref: '#/definitions/vo_api.BatchValidatePrivilegeResponse'
      message:
        example: 操作成功
        type: string
    type: object
  response.Response-vo_api_ParseFileResponse:
    properties:
      code:
        example: 0
        type: integer
      data:
        $ref: '#/definitions/vo_api.ParseFileResponse'
      message:
        example: 操作成功
        type: string
    type: object
  response.Response-vo_api_ResumeScoreDetailResponse:
    properties:
      code:
        example: 0
        type: integer
      data:
        $ref: '#/definitions/vo_api.ResumeScoreDetailResponse'
      message:
        example: 操作成功
        type: string
    type: object
  response.Response-vo_api_ResumeScoreListResponse:
    properties:
      code:
        example: 0
        type: integer
      data:
        $ref: '#/definitions/vo_api.ResumeScoreListResponse'
      message:
        example: 操作成功
        type: string
    type: object
  response.Response-vo_api_ScoreResumeResponse:
    properties:
      code:
        example: 0
        type: integer
      data:
        $ref: '#/definitions/vo_api.ScoreResumeResponse'
      message:
        example: 操作成功
        type: string
    type: object
  response.Response-vo_api_SimpleEnumsResponse:
    properties:
      code:
        example: 0
        type: integer
      data:
        $ref: '#/definitions/vo_api.SimpleEnumsResponse'
      message:
        example: 操作成功
        type: string
    type: object
  vo.ErrorAPIResponse:
    properties:
      code:
        description: 业务状态码
        example: 400
        type: integer
      data:
        description: 响应数据，通常为null
      message:
        description: 错误消息
        example: 请求参数错误
        type: string
    type: object
  vo.PaginatedList-vo_api_ExampleListItemResponse:
    properties:
      list:
        description: 数据列表
        items:
          $ref: '#/definitions/vo_api.ExampleListItemResponse'
        type: array
      page:
        description: 当前页码
        example: 1
        type: integer
      page_size:
        description: 每页条数
        example: 10
        type: integer
      total:
        description: 总记录数
        example: 100
        type: integer
    type: object
  vo.SuccessAPIResponse:
    properties:
      code:
        description: 业务状态码
        example: 200
        type: integer
      data:
        description: 响应数据
      message:
        description: 响应消息
        example: 操作成功
        type: string
    type: object
  vo_admin.AdminInfoResponse:
    properties:
      channels:
        example:
        - '[''web'''
        - '''mobile'']'
        items:
          type: string
        type: array
      created_at:
        example: "2024-01-01T12:00:00Z"
        type: string
      id:
        example: 1
        type: integer
      is_super_admin:
        example: true
        type: boolean
      username:
        example: admin
        type: string
    type: object
  vo_admin.AdminTokenResponse:
    properties:
      token:
        example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        type: string
    type: object
  vo_admin.ChannelPaymentResponse:
    properties:
      amount:
        example: 99
        type: number
      channel:
        example: web
        type: string
      id:
        example: 1
        type: integer
      order_created_at:
        example: "2024-01-01T12:00:00Z"
        type: string
      order_no:
        example: ORD20240101001
        type: string
      payment_method:
        allOf:
        - $ref: '#/definitions/enum.PaymentMethod'
        example: 1
      payment_status:
        allOf:
        - $ref: '#/definitions/enum.PaymentStatus'
        example: 3
      user_created_at:
        example: "2024-01-01T10:00:00Z"
        type: string
      user_id:
        example: 123
        type: integer
      username:
        example: user123
        type: string
    type: object
  vo_admin.ChannelPaymentsListResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/vo_admin.ChannelPaymentResponse'
        type: array
      page:
        example: 1
        type: integer
      page_size:
        example: 10
        type: integer
      total:
        example: 100
        type: integer
      total_amount:
        example: 9900
        type: number
    type: object
  vo_admin.ChannelUserResponse:
    properties:
      channel:
        example: web
        type: string
      created_at:
        example: "2024-01-01T12:00:00Z"
        type: string
      email:
        example: <EMAIL>
        type: string
      id:
        example: 1
        type: integer
      phone:
        example: "13812345678"
        type: string
      status:
        allOf:
        - $ref: '#/definitions/enum.UserStatus'
        example: 1
      user_type:
        allOf:
        - $ref: '#/definitions/enum.UserType'
        example: 2
      username:
        example: user123
        type: string
    type: object
  vo_admin.ChannelUsersListResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/vo_admin.ChannelUserResponse'
        type: array
      page:
        example: 1
        type: integer
      page_size:
        example: 10
        type: integer
      total:
        example: 100
        type: integer
    type: object
  vo_api.ApplyDraftResponse:
    properties:
      resume_id:
        example: 1
        type: integer
    type: object
  vo_api.AvailableCouponsCountResponse:
    properties:
      count:
        description: 可用下载券数量
        example: 5
        type: integer
    type: object
  vo_api.BatchValidatePrivilegeResponse:
    properties:
      all_allowed:
        description: 是否所有权限都通过校验
        example: false
        type: boolean
      modal_description:
        description: 弹窗描述（当AllAllowed为false时）
        example: 请先登录后使用此功能
        type: string
      modal_title:
        description: 弹窗标题（当AllAllowed为false时）
        example: 登录提示
        type: string
      modal_type:
        allOf:
        - $ref: '#/definitions/enum.ModalType'
        description: 弹窗类型（当AllAllowed为false时）
        example: 1
    type: object
  vo_api.CategoryDetailResponse:
    properties:
      category_type:
        type: integer
      description:
        description: 描述
        type: string
      examples:
        allOf:
        - $ref: '#/definitions/vo.PaginatedList-vo_api_ExampleListItemResponse'
        description: 示例数据（仅职位分类时有值）
      has_data:
        description: 是否查到数据
        type: boolean
      more_recommendations:
        description: 更多推荐
        items:
          $ref: '#/definitions/vo_api.ExampleListItemResponse'
        type: array
      select_data:
        $ref: '#/definitions/vo_api.CategoryItemWithChildrenResponse'
      tags:
        description: 分类标签
        items:
          $ref: '#/definitions/vo_api.CategoryTagResponse'
        type: array
    type: object
  vo_api.CategoryGroupResponse:
    properties:
      category_type:
        type: integer
      children:
        items:
          $ref: '#/definitions/vo_api.CategoryItemResponse'
        type: array
      name:
        type: string
    type: object
  vo_api.CategoryItemResponse:
    properties:
      id:
        type: integer
      name:
        type: string
      slug_cn:
        type: string
      slug_en:
        type: string
    type: object
  vo_api.CategoryItemWithChildrenResponse:
    properties:
      children:
        items:
          $ref: '#/definitions/vo_api.CategoryItemWithChildrenResponse'
        type: array
      id:
        type: integer
      name:
        type: string
      slug_cn:
        type: string
      slug_en:
        type: string
    type: object
  vo_api.CategoryTagResponse:
    properties:
      name:
        type: string
      url:
        type: string
    type: object
  vo_api.CategoryTdkResponse:
    properties:
      description:
        example: 专业的软件工程师简历模板，适合后端开发岗位
        type: string
      id:
        example: 1
        type: integer
      keywords: {}
      title:
        example: 软件工程师简历模板
        type: string
    type: object
  vo_api.CreateTargetPositionResponse:
    properties:
      id:
        example: 1
        type: integer
    type: object
  vo_api.DeletedResumeItem:
    properties:
      completion_rate:
        example: 75%
        type: string
      created_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      deleted_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      id:
        example: 1
        type: integer
      preview_image_url:
        example: https://cdn.avrilko.com/preview/123.jpg
        type: string
      resume_name:
        example: 我的简历
        type: string
      template_id:
        example: 1
        type: integer
      updated_at:
        example: "2023-01-01T12:00:00Z"
        type: string
    type: object
  vo_api.DownloadCouponPlanListResponse:
    properties:
      list:
        description: 列表数据
        items:
          $ref: '#/definitions/vo_api.DownloadCouponPlanResponse'
        type: array
    type: object
  vo_api.DownloadCouponPlanResponse:
    properties:
      actual_price:
        description: 实际价格(单位:元)
        example: 9.9
        type: number
      id:
        description: 套餐ID
        example: 1
        type: integer
      name:
        description: 套餐名称
        example: 10次下载券
        type: string
      original_price:
        description: 原价(单位:元)
        example: 19.9
        type: number
      resume_limit:
        description: 简历数量限制 (0表示无限制)
        example: 10
        type: integer
    type: object
  vo_api.EnumItem:
    properties:
      code:
        description: 枚举值（支持int和string）
      name:
        description: 枚举名称
        type: string
    type: object
  vo_api.ExampleDetailResponse:
    properties:
      basic_info:
        allOf:
        - $ref: '#/definitions/models.BasicInfo'
        description: 简历内容字段
      component_name:
        example: ResumeTemplate1
        type: string
      created_at:
        description: 时间字段
        example: "2023-01-01T12:00:00Z"
        type: string
      custom_modules:
        items:
          $ref: '#/definitions/models.CustomModule'
        type: array
      desc:
        description: 描述
        example: 专业的软件工程师简历模板，适合后端开发岗位
        type: string
      education:
        $ref: '#/definitions/models.Education'
      honors:
        $ref: '#/definitions/models.Honors'
      hot_resume_recommendations:
        description: 热门简历推荐
        items:
          $ref: '#/definitions/vo_api.HotResumeRecommendation'
        type: array
      id:
        example: 1
        type: integer
      more_recommendations:
        description: 更多推荐
        items:
          $ref: '#/definitions/vo_api.ExampleListItemResponse'
        type: array
      other:
        $ref: '#/definitions/models.Other'
      personal_summary:
        $ref: '#/definitions/models.PersonalSummary'
      portfolio:
        $ref: '#/definitions/models.Portfolio'
      project:
        $ref: '#/definitions/models.Project'
      research:
        $ref: '#/definitions/models.Research'
      resume_style:
        $ref: '#/definitions/models.ResumeStyle'
      skills:
        $ref: '#/definitions/models.Skills'
      slogan:
        $ref: '#/definitions/models.Slogan'
      tags:
        description: 分类标签和推荐字段
        items:
          $ref: '#/definitions/vo_api.CategoryTagResponse'
        type: array
      team:
        $ref: '#/definitions/models.Team'
      template_id:
        example: 1
        type: integer
      updated_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      usage_count:
        description: 使用人数
        example: 1250
        type: integer
      work:
        $ref: '#/definitions/models.Work'
    type: object
  vo_api.ExampleListItemResponse:
    properties:
      id:
        description: 示例ID
        example: 1
        type: integer
      name:
        description: 模板名称
        example: 软件工程师简历模板
        type: string
      preview_image_url:
        description: 预览图链接
        example: https://example.com/preview/1.jpg
        type: string
      tags:
        description: 模板标签
        example:
        - '[''简洁'''
        - '''专业'''
        - '''技术'']'
        items:
          type: string
        type: array
      template_id:
        description: 模板ID
        example: 1
        type: integer
      usage_count:
        description: 使用人数
        example: 1250
        type: integer
    type: object
  vo_api.ExampleListSwaggerResponse:
    properties:
      list:
        description: 示例列表
        items:
          $ref: '#/definitions/vo_api.ExampleListItemResponse'
        type: array
      page:
        description: 当前页码
        example: 1
        type: integer
      page_size:
        description: 每页条数
        example: 40
        type: integer
      total:
        description: 总记录数
        example: 100
        type: integer
    type: object
  vo_api.ExampleTdkResponse:
    properties:
      description:
        example: 专业的软件工程师简历模板，适合后端开发岗位
        type: string
      id:
        example: 1
        type: integer
      keywords:
        example:
        - '["软件工程师"'
        - '"简历模板"'
        - '"后端开发"]'
        items:
          type: string
        type: array
      title:
        example: 软件工程师简历模板
        type: string
    type: object
  vo_api.GetAllMyResumesResponse:
    properties:
      resumes:
        items:
          $ref: '#/definitions/vo_api.ResumeBasicItem'
        type: array
    type: object
  vo_api.GetDeletedResumesResponse:
    properties:
      resumes:
        items:
          $ref: '#/definitions/vo_api.DeletedResumeItem'
        type: array
    type: object
  vo_api.GetResumeBasicInfoResponse:
    properties:
      completion_rate:
        example: 75%
        type: string
      created_at:
        example: "2023-01-01T00:00:00Z"
        type: string
      id:
        example: 1
        type: integer
      preview_image_url:
        example: https://cdn.avrilko.com/resume/preview/123.png
        type: string
      resume_name:
        example: 张三的简历
        type: string
    type: object
  vo_api.HotResumeRecommendation:
    properties:
      link:
        description: 简历链接
        example: /jianli/123
        type: string
      name:
        description: 简历名称
        example: 软件工程师简历模板
        type: string
      usage_count:
        description: 使用人数
        example: 1250
        type: integer
    type: object
  vo_api.MembershipPlanInfo:
    properties:
      id:
        description: 套餐ID
        example: 1
        type: integer
      name:
        description: 套餐名称
        example: 月度会员
        type: string
    type: object
  vo_api.MembershipPlanListResponse:
    properties:
      list:
        description: 列表数据
        items:
          $ref: '#/definitions/vo_api.MembershipPlanResponse'
        type: array
    type: object
  vo_api.MembershipPlanResponse:
    properties:
      actual_price:
        description: 实际价格(单位:元)
        example: 29.9
        type: number
      ai_diagnose_limit:
        description: AI简历打分次数限制(0表示无限制)
        example: 0
        type: integer
      ai_generate_limit:
        description: AI生成次数限制(0表示无限制)
        example: 0
        type: integer
      ai_one_click_limit:
        description: AI一键生成简历次数限制(0表示无限制)
        example: 0
        type: integer
      ai_optimize_limit:
        description: AI简历优化次数限制(0表示无限制)
        example: 0
        type: integer
      ai_rewrite_limit:
        description: AI改写次数限制(0表示无限制)
        example: 0
        type: integer
      corner_image_url:
        description: 右上角图片地址
        example: https://example.com/images/corner.png
        type: string
      description:
        description: 描述信息
        example:
        - 无限简历创建
        - 高级模板使用权
        - AI优化建议
        items:
          type: string
        type: array
      discount_tip:
        description: 优惠提示
        example: 限时优惠
        type: string
      id:
        description: 套餐ID
        example: 1
        type: integer
      is_default:
        description: 是否默认 1:非默认 2:默认
        example: 2
        type: integer
      name:
        description: 套餐名称
        example: 月度会员
        type: string
      original_price:
        description: 原价(单位:元)
        example: 39.9
        type: number
      resume_limit:
        description: 能创建简历的个数(0表示无限制)
        example: 0
        type: integer
    type: object
  vo_api.OrderListItem:
    properties:
      amount:
        description: 订单金额
        example: 29.9
        type: number
      created_at:
        description: 创建时间
        example: "2023-01-01T12:00:00Z"
        type: string
      fail_reason:
        description: 支付失败原因
        example: ""
        type: string
      id:
        description: 订单ID
        example: 1
        type: integer
      membership_plan:
        allOf:
        - $ref: '#/definitions/vo_api.MembershipPlanInfo'
        description: 套餐信息
      order_no:
        description: 订单号
        example: AL00011234567890
        type: string
      payment_method:
        allOf:
        - $ref: '#/definitions/enum.PaymentMethod'
        description: 支付方式：1微信支付 2支付宝
        example: 2
      payment_status:
        allOf:
        - $ref: '#/definitions/enum.PaymentStatus'
        description: 支付状态：1待支付 2支付处理中 3支付成功 4支付失败 5支付超时
        example: 1
      payment_status_str:
        description: 支付状态文字描述
        example: 待支付
        type: string
      title:
        description: 订单标题
        example: 【熊猫简历】购买会员套餐-月度会员
        type: string
    type: object
  vo_api.OrderListResponse:
    properties:
      list:
        description: 订单列表
        items:
          $ref: '#/definitions/vo_api.OrderListItem'
        type: array
      page:
        description: 当前页码
        example: 1
        type: integer
      page_size:
        description: 每页条数
        example: 10
        type: integer
      total:
        description: 总记录数
        example: 100
        type: integer
    type: object
  vo_api.OrderResponse:
    properties:
      amount:
        description: 订单金额
        example: 29.9
        type: number
      code_url:
        description: 支付二维码链接
        example: https://qr.alipay.com/xxx
        type: string
      order_no:
        description: 订单号
        example: RS00011234567890
        type: string
      package_id:
        description: 套餐ID
        example: 1
        type: integer
      title:
        description: 订单标题
        example: 【熊猫简历】购买会员套餐-月度会员
        type: string
    type: object
  vo_api.OrderStatusResponse:
    properties:
      amount:
        description: 订单金额
        example: 29.9
        type: number
      channel_name:
        description: 用户渠道名称
        example: 百度推广
        type: string
      fail_reason:
        description: 支付失败原因，仅当支付状态为失败或超时时有值
        example: 支付超时
        type: string
      order_no:
        description: 订单号
        example: AL00011234567890
        type: string
      payment_status:
        description: 支付状态(1:待支付 2:支付处理中 3:支付成功 4:支付失败 5:支付超时)
        example: 1
        type: integer
    type: object
  vo_api.ParseFileResponse:
    properties:
      content:
        description: 解析后的文件内容
        example: 这是解析后的文件内容...
        type: string
      file_size:
        description: 文件大小（字节）
        example: 1024000
        type: integer
      file_type:
        description: 文件类型
        example: pdf
        type: string
      filename:
        description: 原始文件名
        example: document.pdf
        type: string
    type: object
  vo_api.PositionItemResponse:
    properties:
      children:
        items:
          $ref: '#/definitions/vo_api.PositionItemResponse'
        type: array
      id:
        type: integer
      name:
        type: string
      slug_cn:
        type: string
      slug_en:
        type: string
    type: object
  vo_api.QrCodeLoginResponse:
    properties:
      qr_code_url:
        description: 二维码图片URL
        example: https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=xxxxx
        type: string
      scene_id:
        description: 场景值ID，前端需要保存此ID用于后续查询扫码状态
        example: login_123456789
        type: string
    type: object
  vo_api.QrCodeStatusResponse:
    properties:
      status:
        description: 二维码状态：PENDING(待扫描)、SCANNED(已扫描)、EXPIRED(已过期)
        example: SCANNED
        type: string
      token:
        description: 令牌，仅当状态为SCANNED时返回
        example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        type: string
    type: object
  vo_api.ResumeBasicItem:
    properties:
      completion_rate:
        example: 75%
        type: string
      created_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      id:
        example: 1
        type: integer
      preview_image_url:
        example: https://cdn.avrilko.com/preview/123.jpg
        type: string
      resume_name:
        example: 我的简历
        type: string
      template_id:
        example: 1
        type: integer
      updated_at:
        example: "2023-01-01T12:00:00Z"
        type: string
    type: object
  vo_api.ResumeDetailResponse:
    properties:
      basic_info:
        allOf:
        - $ref: '#/definitions/models.BasicInfo'
        description: 简历详情内容
      completion_rate:
        example: 75%
        type: string
      component_name:
        example: ResumeTemplate1
        type: string
      created_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      custom_modules:
        items:
          $ref: '#/definitions/models.CustomModule'
        type: array
      diff:
        example: 优化了工作经历描述，增强了项目经历的技术细节
        type: string
      education:
        $ref: '#/definitions/models.Education'
      honors:
        $ref: '#/definitions/models.Honors'
      id:
        example: 1
        type: integer
      other:
        $ref: '#/definitions/models.Other'
      personal_summary:
        $ref: '#/definitions/models.PersonalSummary'
      portfolio:
        $ref: '#/definitions/models.Portfolio'
      preview_image_url:
        example: https://cdn.avrilko.com/preview/123.jpg
        type: string
      project:
        $ref: '#/definitions/models.Project'
      research:
        $ref: '#/definitions/models.Research'
      resume_name:
        example: 我的简历
        type: string
      resume_style:
        $ref: '#/definitions/models.ResumeStyle'
      skills:
        $ref: '#/definitions/models.Skills'
      slogan:
        $ref: '#/definitions/models.Slogan'
      team:
        $ref: '#/definitions/models.Team'
      template_id:
        example: 1
        type: integer
      updated_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      user_id:
        example: 1
        type: integer
      work:
        $ref: '#/definitions/models.Work'
    type: object
  vo_api.ResumeScoreDetailResponse:
    properties:
      content_relevance_details:
        items:
          $ref: '#/definitions/vo_api.ScoreDetailItem'
        type: array
      content_relevance_score:
        example: 78
        type: number
      created_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      id:
        example: 1
        type: integer
      information_completeness_details:
        items:
          $ref: '#/definitions/vo_api.ScoreDetailItem'
        type: array
      information_completeness_score:
        example: 82
        type: number
      language_expression_details:
        items:
          $ref: '#/definitions/vo_api.ScoreDetailItem'
        type: array
      language_expression_score:
        example: 85.5
        type: number
      overall_comment:
        example: 简历整体质量较高...
        type: string
      overall_score:
        example: 83.4
        type: number
      professionalism_details:
        items:
          $ref: '#/definitions/vo_api.ScoreDetailItem'
        type: array
      professionalism_score:
        example: 88
        type: number
      resume_id:
        example: 1
        type: integer
      resume_name:
        example: 我的简历
        type: string
      target_position_id:
        example: 1
        type: integer
      target_position_name:
        example: 前端开发工程师
        type: string
      updated_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      user_id:
        example: 1
        type: integer
    type: object
  vo_api.ResumeScoreListItem:
    properties:
      created_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      id:
        example: 1
        type: integer
      overall_score:
        example: 83.4
        type: number
      resume_id:
        example: 1
        type: integer
      resume_name:
        example: 我的简历
        type: string
    type: object
  vo_api.ResumeScoreListResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/vo_api.ResumeScoreListItem'
        type: array
      total:
        example: 10
        type: integer
    type: object
  vo_api.SaveResumeResponse:
    properties:
      updated_at:
        example: "2023-01-01T12:00:00Z"
        type: string
    type: object
  vo_api.ScoreDetailItem:
    properties:
      comment:
        description: 评价
        example: 语言表达清晰，逻辑性强，但部分描述可以更加简洁
        type: string
      title:
        description: 小标题
        example: 语言表达清晰度
        type: string
    type: object
  vo_api.ScoreDimension:
    properties:
      details:
        description: 详细评价项目
        items:
          $ref: '#/definitions/vo_api.ScoreDetailItem'
        type: array
      score:
        description: 评分 (0-100)
        example: 85.5
        type: number
    type: object
  vo_api.ScoreResumeResponse:
    properties:
      content_relevance:
        allOf:
        - $ref: '#/definitions/vo_api.ScoreDimension'
        description: 内容相关性评分
      information_completeness:
        allOf:
        - $ref: '#/definitions/vo_api.ScoreDimension'
        description: 信息完整性评分
      language_expression:
        allOf:
        - $ref: '#/definitions/vo_api.ScoreDimension'
        description: 语言与表达评分
      overall_comment:
        description: 总体评价
        example: 简历整体质量较高，建议在项目经历部分增加更多量化数据
        type: string
      overall_score:
        description: 总体评分
        example: 85.5
        type: number
      professionalism:
        allOf:
        - $ref: '#/definitions/vo_api.ScoreDimension'
        description: 简历专业性评分
    type: object
  vo_api.SearchResponse:
    properties:
      estimatedTotalHits:
        description: 预估总数
        type: integer
      hits:
        description: 搜索结果列表
        items:
          $ref: '#/definitions/vo_api.SearchResultItem'
        type: array
      limit:
        description: 返回条数限制
        type: integer
      offset:
        description: 偏移量
        type: integer
      processingTimeMs:
        description: 处理时间（毫秒）
        type: integer
      query:
        description: 搜索关键词
        type: string
    type: object
  vo_api.SearchResultItem:
    properties:
      id:
        description: 文档ID，格式：position_123 或 example_456
        type: string
      name:
        description: position的名称或者example表name
        type: string
      slug_cn:
        description: slug_cn字段
        type: string
      type:
        description: 1为position 2为example
        type: integer
      url:
        description: position为/jianli/{slug_cn} example为/jianli/{id}.html
        type: string
    type: object
  vo_api.ShareResumeByEmailResponse:
    properties:
      message:
        example: 简历邮件发送成功
        type: string
    type: object
  vo_api.SimpleEnumsResponse:
    additionalProperties:
      items:
        $ref: '#/definitions/vo_api.EnumItem'
      type: array
    type: object
  vo_api.TargetPositionListResponse:
    properties:
      list:
        items:
          $ref: '#/definitions/vo_api.TargetPositionResponse'
        type: array
      total:
        example: 10
        type: integer
    type: object
  vo_api.TargetPositionResponse:
    properties:
      company_name:
        example: 阿里巴巴
        type: string
      created_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      id:
        example: 1
        type: integer
      job_description:
        example: 负责前端页面开发...
        type: string
      job_source:
        example: Boss直聘
        type: string
      position_name:
        example: 前端开发工程师
        type: string
      updated_at:
        example: "2023-01-01T12:00:00Z"
        type: string
      user_id:
        example: 1
        type: integer
    type: object
  vo_api.TemplateListItemResponse:
    properties:
      id:
        description: 模板ID
        example: 1
        type: integer
      preview_image_url:
        description: 预览图链接
        example: https://example.com/preview/1.jpg
        type: string
      tags:
        description: 模板标签
        example:
        - '[''简洁'''
        - '''专业'''
        - '''技术'']'
        items:
          type: string
        type: array
      template_name:
        description: 模板名称
        example: 简洁风格简历模板
        type: string
      usage_count:
        description: 使用人数
        example: 1250
        type: integer
    type: object
  vo_api.TemplateListResponse:
    properties:
      list:
        description: 模板列表
        items:
          $ref: '#/definitions/vo_api.TemplateListItemResponse'
        type: array
      page:
        description: 当前页码
        example: 1
        type: integer
      page_size:
        description: 每页条数
        example: 20
        type: integer
      total:
        description: 总记录数
        example: 100
        type: integer
    type: object
  vo_api.TokenResponse:
    properties:
      token:
        example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
        type: string
    type: object
  vo_api.UpdateResumeNameResponse:
    properties:
      resume_name:
        example: 我的新简历
        type: string
      updated_at:
        example: "2023-01-01T12:00:00Z"
        type: string
    type: object
  vo_api.UploadResponse:
    properties:
      filename:
        description: 文件名
        example: 123456789.jpg
        type: string
      url:
        description: 文件URL
        example: https://cdn.avrilko.com/speed-fox/avatar/123456789.jpg
        type: string
    type: object
  vo_api.UseExampleResponse:
    properties:
      resume_id:
        example: 123
        type: integer
    type: object
  vo_api.UserResponse:
    properties:
      avatar:
        example: http://example.com/avatar.jpg
        type: string
      created_at:
        description: 创建时间
        example: "2023-01-01T12:00:00Z"
        type: string
      email:
        example: <EMAIL>
        type: string
      id:
        example: 1
        type: integer
      is_logged_in:
        description: 是否登录，JWT认证成功为true，指纹认证为false
        example: true
        type: boolean
      open_id:
        example: oNHwxjgrzgL9H_A2pGLSMuME-X-Q
        type: string
      phone:
        example: "13812345678"
        type: string
      user_type:
        allOf:
        - $ref: '#/definitions/enum.UserType'
        description: 用户类型 1:游客 2:普通用户 3:会员
        example: 2
      username:
        example: johndoe
        type: string
    type: object
host: localhost:8082
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: Resume Server API documentation
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  title: Resume Server API
  version: "1.0"
paths:
  /admin/admin/change-password:
    post:
      consumes:
      - application/json
      description: 管理员修改自己的密码，需要提供旧密码和新密码
      parameters:
      - description: 修改密码信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto_admin.AdminChangePasswordRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 修改成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  additionalProperties:
                    type: string
                  type: object
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 旧密码错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "404":
          description: 管理员不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - AdminAuth: []
      summary: 修改管理员密码
      tags:
      - Admin/管理员管理
  /admin/admin/info:
    get:
      consumes:
      - application/json
      description: 获取当前登录管理员的详细信息
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_admin.AdminInfoResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "404":
          description: 管理员不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - AdminAuth: []
      summary: 获取当前管理员信息
      tags:
      - Admin/管理员管理
  /admin/admin/logout:
    post:
      consumes:
      - application/json
      description: 管理员退出登录，清除服务端token缓存
      produces:
      - application/json
      responses:
        "200":
          description: 退出成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  additionalProperties:
                    type: string
                  type: object
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - AdminAuth: []
      summary: 管理员退出登录
      tags:
      - Admin/管理员管理
  /admin/auth/login:
    post:
      consumes:
      - application/json
      description: 管理员使用用户名和密码登录并获取令牌
      parameters:
      - description: 登录信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto_admin.AdminLoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 登录成功，返回token
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_admin.AdminTokenResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 用户名或密码错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "403":
          description: 账号已被禁用
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 管理员登录
      tags:
      - Admin/认证管理
  /admin/channel/payments:
    get:
      consumes:
      - application/json
      description: 获取渠道付费列表，支持分页、时间范围查询和渠道号查询，返回合计付费金额，非超级管理员只能查看有权限的渠道
      parameters:
      - description: 页码
        example: 1
        in: query
        name: page
        type: integer
      - description: 每页条数
        example: 10
        in: query
        name: page_size
        type: integer
      - description: 渠道号
        example: '"web"'
        in: query
        name: channel
        type: string
      - description: 开始时间
        example: '"2024-01-01T00:00:00Z"'
        in: query
        name: start_time
        type: string
      - description: 结束时间
        example: '"2024-12-31T23:59:59Z"'
        in: query
        name: end_time
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_admin.ChannelPaymentsListResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - AdminAuth: []
      summary: 获取渠道付费列表
      tags:
      - Admin/渠道管理
  /admin/channel/users:
    get:
      consumes:
      - application/json
      description: 获取渠道用户列表，支持分页、时间范围查询和渠道号查询，非超级管理员只能查看有权限的渠道
      parameters:
      - description: 页码
        example: 1
        in: query
        name: page
        type: integer
      - description: 每页条数
        example: 10
        in: query
        name: page_size
        type: integer
      - description: 渠道号
        example: '"web"'
        in: query
        name: channel
        type: string
      - description: 开始时间
        example: '"2024-01-01T00:00:00Z"'
        in: query
        name: start_time
        type: string
      - description: 结束时间
        example: '"2024-12-31T23:59:59Z"'
        in: query
        name: end_time
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_admin.ChannelUsersListResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "403":
          description: 权限不足
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - AdminAuth: []
      summary: 获取渠道用户列表
      tags:
      - Admin/渠道管理
  /api/ai/batch-validate-privilege:
    post:
      consumes:
      - application/json
      description: 批量校验用户是否可以使用多项权益，返回权限校验结果、弹窗类型、弹窗标题和描述文案
      parameters:
      - description: 批量权限校验请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto_api.BatchValidatePrivilegeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 校验成功
          schema:
            $ref: '#/definitions/response.Response-vo_api_BatchValidatePrivilegeResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/response.Response-any'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/response.Response-any'
      summary: 批量权限校验
      tags:
      - API/AI助手
  /api/ai/generate-resume:
    post:
      consumes:
      - application/json
      description: 根据用户提供的话术，使用AI生成完整的简历数据，包括基本信息、教育经历、工作经历、项目经历和个人总结
      parameters:
      - description: AI生成简历请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto_api.GenerateResumeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 生成成功
          schema:
            $ref: '#/definitions/response.Response-dto_api_GenerateResumeResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/response.Response-any'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/response.Response-any'
      summary: AI生成简历
      tags:
      - API/AI助手
  /api/ai/optimize-resume:
    post:
      consumes:
      - application/json
      description: 根据简历ID，使用AI优化简历内容，将优化后的内容保存到简历草稿表
      parameters:
      - description: AI优化简历请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto_api.OptimizeResumeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 优化成功
          schema:
            $ref: '#/definitions/response.Response-dto_api_OptimizeResumeResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/response.Response-any'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/response.Response-any'
      summary: AI优化简历
      tags:
      - API/AI助手
  /api/ai/parse-file:
    post:
      consumes:
      - multipart/form-data
      description: 上传文件并解析内容，支持pdf、txt、csv、docx、doc、xlsx、xls、pptx、ppt、md、mobi、epub格式，最大10MB
      parameters:
      - description: 要解析的文件
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: 解析成功
          schema:
            $ref: '#/definitions/response.Response-vo_api_ParseFileResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/response.Response-any'
        "413":
          description: 文件过大
          schema:
            $ref: '#/definitions/response.Response-any'
        "415":
          description: 不支持的文件类型
          schema:
            $ref: '#/definitions/response.Response-any'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/response.Response-any'
      summary: 文件解析
      tags:
      - API/AI助手
  /api/ai/prompt:
    post:
      consumes:
      - application/json
      description: 根据简历模块和提示词类型，使用AI生成相应的内容，支持流式传输
      parameters:
      - description: AI提示词请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto_api.PromptRequest'
      produces:
      - text/plain
      responses:
        "200":
          description: AI生成的内容（流式传输）
          schema:
            type: string
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/response.Response-any'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/response.Response-any'
      summary: AI提示词处理
      tags:
      - API/AI助手
  /api/ai/records:
    get:
      consumes:
      - application/json
      description: 根据简历ID分页获取AI调用记录列表，返回枚举的字符串值
      parameters:
      - description: 简历ID
        in: query
        name: resume_id
        required: true
        type: integer
      - default: 1
        description: 页码
        in: query
        name: page
        type: integer
      - default: 20
        description: 每页数量
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: AI调用记录列表
          schema:
            $ref: '#/definitions/response.Response-any'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/response.Response-any'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/response.Response-any'
      summary: 获取AI调用记录列表
      tags:
      - API/AI助手
  /api/ai/score-resume:
    post:
      consumes:
      - application/json
      description: 根据简历ID和目标岗位ID，使用AI对简历进行四维度打分评估：语言与表达、信息完整性、内容相关性、简历专业性。每个维度包含总分和详细评价项。
      parameters:
      - description: AI简历打分请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto_api.ScoreResumeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 打分成功，返回四维度评分结果
          schema:
            $ref: '#/definitions/response.Response-vo_api_ScoreResumeResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/response.Response-any'
        "403":
          description: 权益不足
          schema:
            $ref: '#/definitions/response.Response-any'
        "404":
          description: 简历或目标岗位不存在
          schema:
            $ref: '#/definitions/response.Response-any'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/response.Response-any'
      summary: AI简历打分
      tags:
      - API/AI助手
  /api/auth/email/bind-email-code:
    post:
      consumes:
      - application/json
      description: 向指定邮箱发送绑定邮箱验证码，验证码有效期5分钟，同一邮箱1分钟内只能发送一次
      parameters:
      - description: 发送验证码请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto_api.SendEmailCodeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 发送成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "429":
          description: 发送频率限制
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 发送绑定邮箱验证码
      tags:
      - API/认证管理
  /api/auth/email/code:
    post:
      consumes:
      - application/json
      description: 向指定邮箱发送验证码，验证码有效期5分钟，同一邮箱号码1分钟内只能发送一次
      parameters:
      - description: 发送验证码请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto_api.SendEmailCodeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 发送成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "429":
          description: 发送频率限制
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 发送邮件验证码
      tags:
      - API/认证管理
  /api/auth/email/login:
    post:
      consumes:
      - application/json
      description: 用户使用邮箱和验证码登录并获取令牌，如果用户不存在则自动注册
      parameters:
      - description: 邮箱验证码登录请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto_api.LoginEmailCodeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 登录成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.TokenResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 验证码错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 邮箱验证码登录
      tags:
      - API/认证管理
  /api/auth/login-code:
    post:
      consumes:
      - application/json
      description: 用户使用手机号和验证码登录并获取令牌，如果用户不存在则自动注册
      parameters:
      - description: 登录信息
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto_api.LoginCodeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 登录成功，返回token
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.TokenResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 验证码错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 验证码登录
      tags:
      - API/认证管理
  /api/auth/login/qrcode:
    get:
      consumes:
      - application/json
      description: 获取微信扫码登录的二维码，返回二维码图片URL和场景值
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.QrCodeLoginResponse'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 获取登录二维码
      tags:
      - API/认证管理
  /api/auth/login/qrcode/status:
    post:
      consumes:
      - application/json
      description: 检查微信扫码登录的二维码状态，如果已扫码并且用户存在则返回token
      parameters:
      - description: 二维码状态请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto_api.QrCodeStatusRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.QrCodeStatusResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 检查二维码状态
      tags:
      - API/认证管理
  /api/auth/sms/bind-phone-code:
    post:
      consumes:
      - application/json
      description: 向指定手机号发送绑定手机号验证码，验证码有效期5分钟，同一手机号1分钟内只能发送一次
      parameters:
      - description: 发送验证码请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto_api.SendSMSCodeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 发送成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "429":
          description: 发送频率限制
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 发送绑定手机号验证码
      tags:
      - API/认证管理
  /api/auth/sms/code:
    post:
      consumes:
      - application/json
      description: 向指定手机号发送短信验证码，验证码有效期5分钟，同一手机号1分钟内只能发送一次
      parameters:
      - description: 发送验证码请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto_api.SendSMSCodeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 发送成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "429":
          description: 发送频率限制
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 发送短信验证码
      tags:
      - API/认证管理
  /api/categories:
    get:
      consumes:
      - application/json
      description: 获取所有分类数据，按分类类型分组返回
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/vo_api.CategoryGroupResponse'
                  type: array
              type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 获取所有分类数据
      tags:
      - API/分类管理
  /api/categories/{slug}:
    get:
      consumes:
      - application/json
      description: 先查询分类表slug_cn，存在则返回分类数据，不存在则查询职位表并返回完整层级结构
      parameters:
      - description: 分类或职位的slug
        in: path
        name: slug
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.CategoryDetailResponse'
              type: object
        "404":
          description: 分类不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 根据slug获取分类详情
      tags:
      - API/分类管理
  /api/categories/{slug}/examples:
    get:
      consumes:
      - application/json
      description: 根据分类或职位的slug获取对应的示例列表，支持分页
      parameters:
      - description: 分类或职位的slug
        in: path
        name: slug
        required: true
        type: string
      - default: 1
        description: 页码，默认为1
        in: query
        name: page
        type: integer
      - default: 40
        description: 每页条数，默认为40
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.ExampleListSwaggerResponse'
              type: object
        "404":
          description: 分类不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 根据slug获取示例列表
      tags:
      - API/分类管理
  /api/categories/tdk/{slug}:
    get:
      consumes:
      - application/json
      description: 先查询分类表slug_cn，存在则返回分类TDK数据，不存在则查询职位表TDK数据
      parameters:
      - description: 分类或职位的slug
        in: path
        name: slug
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.CategoryTdkResponse'
              type: object
        "404":
          description: 分类不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 根据slug获取分类TDK信息
      tags:
      - API/分类管理
  /api/common/enums:
    get:
      consumes:
      - application/json
      description: 获取系统中定义的枚举列表，返回枚举的code和name，方便前端下拉组件使用
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功" {"code":0,"message":"获取枚举列表成功","data":{"account_status":[{"code":0,"name":"正常"},{"code":1,"name":"异常"}],"notice_status":[{"code":0,"name":"待处理"},{"code":1,"name":"已接受"},{"code":2,"name":"已拒绝"}]}}
          schema:
            $ref: '#/definitions/response.Response-vo_api_SimpleEnumsResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/response.Response-any'
      summary: 获取枚举列表
      tags:
      - API/通用接口
  /api/download-coupon-plans:
    get:
      consumes:
      - application/json
      description: 获取所有可见的下载券套餐，只返回基本信息（ID、名称、价格），按照排序字段降序排列（值越大越靠前）
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.DownloadCouponPlanListResponse'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 获取所有可见的下载券套餐
      tags:
      - API/会员套餐
  /api/examples/homepage:
    get:
      consumes:
      - application/json
      description: 随机获取16个示例用于首页展示，包含预览图、标签、名称等信息
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/vo_api.ExampleListItemResponse'
                  type: array
              type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 获取首页示例列表
      tags:
      - API/示例管理
  /api/examples/id/{id}:
    get:
      consumes:
      - application/json
      description: 根据示例ID获取示例的详细信息，包括简历内容和模板样式
      parameters:
      - description: 示例ID
        example: 1
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.ExampleDetailResponse'
              type: object
        "404":
          description: 示例不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 根据ID获取示例详情
      tags:
      - API/示例管理
  /api/examples/recommendations:
    get:
      consumes:
      - application/json
      description: 随机获取20个示例作为模板推荐，包含预览图、标签、名称等信息，支持永久缓存
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/vo_api.ExampleListItemResponse'
                  type: array
              type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 获取模板推荐列表（带永久缓存）
      tags:
      - API/示例管理
  /api/examples/recommendations/fresh:
    get:
      consumes:
      - application/json
      description: 随机获取20个示例作为模板推荐，包含预览图、标签、名称等信息，实时查询不使用缓存
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/vo_api.ExampleListItemResponse'
                  type: array
              type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 获取模板推荐列表（不带缓存）
      tags:
      - API/示例管理
  /api/examples/tdk/{id}:
    get:
      consumes:
      - application/json
      description: 根据示例ID获取示例的TDK（标题、描述、关键词）信息
      parameters:
      - description: 示例ID
        example: 1
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.ExampleTdkResponse'
              type: object
        "404":
          description: 示例不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 根据ID获取示例TDK信息
      tags:
      - API/示例管理
  /api/examples/use:
    post:
      consumes:
      - application/json
      description: 根据例子ID复制例子的内容创建新的简历
      parameters:
      - description: 使用例子请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto_api.UseExampleRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.UseExampleResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "404":
          description: 示例不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 使用例子创建简历
      tags:
      - API/示例管理
  /api/membership-plans:
    get:
      consumes:
      - application/json
      description: 获取所有可见的会员套餐，按照排序字段降序排列（值越大越靠前）
      produces:
      - application/json
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.MembershipPlanListResponse'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 获取所有可见的会员套餐
      tags:
      - API/会员套餐
  /api/openapi/alipay:
    post:
      consumes:
      - application/x-www-form-urlencoded
      description: 处理支付宝支付回调通知，验证签名并处理订单状态
      produces:
      - text/plain
      responses:
        "200":
          description: success
          schema:
            type: string
        "400":
          description: fail
          schema:
            type: string
      summary: 支付宝支付回调
      tags:
      - API/OpenAPI
  /api/openapi/wechatpay:
    post:
      consumes:
      - application/json
      description: 处理微信支付回调通知，验证签名并处理订单状态
      produces:
      - text/plain
      responses:
        "200":
          description: success
          schema:
            type: string
        "400":
          description: fail
          schema:
            type: string
      summary: 微信支付回调
      tags:
      - API/OpenAPI
  /api/orders:
    get:
      consumes:
      - application/json
      description: 分页获取当前用户的订单列表，支持按支付状态筛选，按创建时间倒序排列
      parameters:
      - description: 页码，默认为1
        example: 1
        in: query
        minimum: 1
        name: page
        type: integer
      - description: 每页条数，默认为10，最大100
        example: 10
        in: query
        maximum: 100
        minimum: 1
        name: page_size
        type: integer
      - description: 支付状态筛选：1待支付 2支付处理中 3支付成功 4支付失败 5支付超时，支持多个状态组合查询(用逗号分隔)，不传则查询所有状态
        example: '"1,3"'
        in: query
        name: payment_status
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.OrderListResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 获取用户订单列表
      tags:
      - API/订单管理
  /api/orders/alipay:
    post:
      consumes:
      - application/json
      description: 创建支付宝订单并返回支付二维码链接
      parameters:
      - description: 创建订单请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto_api.CreateOrderRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.OrderResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "404":
          description: 会员套餐不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 创建支付宝订单
      tags:
      - API/订单管理
  /api/orders/status:
    post:
      consumes:
      - application/json
      description: 根据订单号查询订单状态，返回订单号、支付状态(整数值:1待支付 2支付处理中 3支付成功 4支付失败 5支付超时)和失败原因(如果有)
      parameters:
      - description: 查询订单状态请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto_api.QueryOrderStatusRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 查询成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.OrderStatusResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "404":
          description: 订单不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 查询订单状态
      tags:
      - API/订单管理
  /api/orders/wechat:
    post:
      consumes:
      - application/json
      description: 创建微信支付订单并返回支付二维码链接
      parameters:
      - description: 创建订单请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto_api.CreateOrderRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.OrderResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "404":
          description: 会员套餐不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 创建微信支付订单
      tags:
      - API/订单管理
  /api/positions:
    get:
      consumes:
      - application/json
      description: 获取所有职位信息，按层级结构返回，支持无限层级嵌套
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/vo_api.PositionItemResponse'
                  type: array
              type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 获取所有职位信息
      tags:
      - API/职位管理
  /api/positions/{parent_id}/children:
    get:
      consumes:
      - application/json
      description: 根据一级分类ID获取下面的二级三级分类，按层级结构返回
      parameters:
      - description: 一级分类ID
        in: path
        name: parent_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/vo_api.PositionItemResponse'
                  type: array
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 根据一级分类ID获取子分类
      tags:
      - API/职位管理
  /api/positions/search/{keyword}:
    get:
      consumes:
      - application/json
      description: 根据关键字搜索职位和示例信息，支持按名称和slug_cn搜索，返回包含type字段区分职位(1)和示例(2)的结果，最多返回10条结果
      parameters:
      - description: 搜索关键字
        in: path
        name: keyword
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 搜索成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.SearchResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 搜索职位和示例
      tags:
      - API/职位管理
  /api/resume-drafts/{draft_id}:
    get:
      consumes:
      - application/json
      description: 根据简历草稿ID获取简历草稿的详细信息，包括基本信息、教育经历、工作经历等所有模块
      parameters:
      - description: 简历草稿ID
        in: path
        name: draft_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.ResumeDetailResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "403":
          description: 无权访问该简历草稿
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "404":
          description: 简历草稿不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - BearerAuth: []
      summary: 获取简历草稿详情
      tags:
      - API/简历管理
  /api/resume-scores:
    get:
      consumes:
      - application/json
      description: 获取当前用户的所有简历评分记录列表，包含简历名称、总体评分、创建时间等信息
      parameters:
      - default: 1
        description: 页码
        example: 1
        in: query
        name: page
        type: integer
      - default: 10
        description: 每页数量
        example: 10
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            $ref: '#/definitions/response.Response-vo_api_ResumeScoreListResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/response.Response-any'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/response.Response-any'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/response.Response-any'
      security:
      - BearerAuth: []
      summary: 获取我的所有简历评分记录
      tags:
      - API/简历评分管理
  /api/resume-scores/{id}:
    delete:
      consumes:
      - application/json
      description: 根据评分记录ID删除简历评分记录，只有记录的所有者才能删除
      parameters:
      - description: 评分记录ID
        example: 1
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/response.Response-any'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/response.Response-any'
        "403":
          description: 无权限访问
          schema:
            $ref: '#/definitions/response.Response-any'
        "404":
          description: 评分记录不存在
          schema:
            $ref: '#/definitions/response.Response-any'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/response.Response-any'
      security:
      - BearerAuth: []
      summary: 删除简历评分记录
      tags:
      - API/简历评分管理
    get:
      consumes:
      - application/json
      description: 根据评分记录ID获取简历评分的详细信息，包含四个维度的评分和详细评价
      parameters:
      - description: 评分记录ID
        example: 1
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            $ref: '#/definitions/response.Response-vo_api_ResumeScoreDetailResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/response.Response-any'
        "403":
          description: 无权限访问
          schema:
            $ref: '#/definitions/response.Response-any'
        "404":
          description: 评分记录不存在
          schema:
            $ref: '#/definitions/response.Response-any'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/response.Response-any'
      security:
      - BearerAuth: []
      summary: 根据ID获取简历评分详情
      tags:
      - API/简历评分管理
  /api/resumes:
    get:
      consumes:
      - application/json
      description: 获取当前用户的所有简历列表，只返回简历表的基本信息，按更新时间排序
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.GetAllMyResumesResponse'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - BearerAuth: []
      summary: 获取所有自己的简历
      tags:
      - API/简历管理
  /api/resumes/{resume_id}:
    delete:
      consumes:
      - application/json
      description: 根据简历ID删除简历及其所有相关数据
      parameters:
      - description: 简历ID
        in: path
        name: resume_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "403":
          description: 无权访问该简历
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "404":
          description: 简历不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - BearerAuth: []
      summary: 删除简历
      tags:
      - API/简历管理
    get:
      consumes:
      - application/json
      description: 根据简历ID获取简历的详细信息，包括基本信息、教育经历、工作经历等所有模块
      parameters:
      - description: 简历ID
        in: path
        name: resume_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.ResumeDetailResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "403":
          description: 无权访问该简历
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "404":
          description: 简历不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - BearerAuth: []
      summary: 获取简历详情
      tags:
      - API/简历管理
    put:
      consumes:
      - application/json
      description: 保存简历的所有模块内容，包括基本信息、教育经历、工作经历等
      parameters:
      - description: 简历ID
        in: path
        name: resume_id
        required: true
        type: string
      - description: 简历详情数据
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto_api.SaveResumeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 保存成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.SaveResumeResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "403":
          description: 无权访问该简历
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "404":
          description: 简历不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - BearerAuth: []
      summary: 保存简历详情
      tags:
      - API/简历管理
  /api/resumes/{resume_id}/apply-draft:
    post:
      consumes:
      - application/json
      description: 将草稿数据覆盖到指定简历，用草稿内容更新简历的所有模块数据
      parameters:
      - description: 简历ID
        in: path
        name: resume_id
        required: true
        type: string
      - description: 应用草稿数据
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto_api.ApplyDraftRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 应用成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.ApplyDraftResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "403":
          description: 无权访问该简历或草稿
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "404":
          description: 简历或草稿不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - BearerAuth: []
      summary: 应用草稿到简历
      tags:
      - API/简历管理
  /api/resumes/{resume_id}/basic:
    get:
      consumes:
      - application/json
      description: 根据简历ID获取简历的基本信息（ID、名称、预览图、创建时间）
      parameters:
      - description: 简历ID
        in: path
        name: resume_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.GetResumeBasicInfoResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "403":
          description: 无权访问该简历
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "404":
          description: 简历不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - BearerAuth: []
      summary: 获取简历基本信息
      tags:
      - API/简历管理
  /api/resumes/{resume_id}/copy:
    post:
      consumes:
      - application/json
      description: 根据简历ID复制简历及其所有相关数据，新简历名称会在原名称后加上"-复制"
      parameters:
      - description: 简历ID
        in: path
        name: resume_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 复制成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "403":
          description: 无权访问该简历
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "404":
          description: 简历不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - BearerAuth: []
      summary: 复制简历
      tags:
      - API/简历管理
  /api/resumes/{resume_id}/download:
    get:
      description: 根据简历ID生成并下载PDF文件
      parameters:
      - description: 简历ID
        in: path
        name: resume_id
        required: true
        type: string
      produces:
      - application/pdf
      responses:
        "200":
          description: PDF文件
          schema:
            type: file
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "403":
          description: 无权访问该简历
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "404":
          description: 简历不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - BearerAuth: []
      summary: 下载简历PDF
      tags:
      - API/简历管理
  /api/resumes/{resume_id}/name:
    put:
      consumes:
      - application/json
      description: 修改指定简历的名称
      parameters:
      - description: 简历ID
        in: path
        name: resume_id
        required: true
        type: string
      - description: 简历名称数据
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto_api.UpdateResumeNameRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 修改成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.UpdateResumeNameResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "403":
          description: 无权访问该简历
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "404":
          description: 简历不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - BearerAuth: []
      summary: 修改简历名称
      tags:
      - API/简历管理
  /api/resumes/{resume_id}/permanently:
    delete:
      consumes:
      - application/json
      description: 根据简历ID彻底删除简历及其所有相关数据，只能删除回收站中的简历
      parameters:
      - description: 简历ID
        in: path
        name: resume_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 彻底删除成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "403":
          description: 无权访问该简历或简历不在回收站
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "404":
          description: 简历不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - BearerAuth: []
      summary: 物理删除简历
      tags:
      - API/简历管理
  /api/resumes/{resume_id}/restore:
    put:
      consumes:
      - application/json
      description: 根据简历ID从回收站恢复简历，只能恢复回收站中的简历
      parameters:
      - description: 简历ID
        in: path
        name: resume_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 恢复成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "403":
          description: 无权访问该简历或简历不在回收站
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "404":
          description: 简历不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - BearerAuth: []
      summary: 恢复简历
      tags:
      - API/简历管理
  /api/resumes/{resume_id}/share/email:
    post:
      consumes:
      - application/json
      description: 生成简历PDF并通过邮件发送给指定邮箱地址
      parameters:
      - description: 简历ID
        in: path
        name: resume_id
        required: true
        type: string
      - description: 邮件分享数据
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto_api.ShareResumeByEmailRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 分享成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.ShareResumeByEmailResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "403":
          description: 无权访问该简历
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "404":
          description: 简历不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - BearerAuth: []
      summary: 邮件分享简历
      tags:
      - API/简历管理
  /api/resumes/online:
    post:
      consumes:
      - application/json
      description: 记录用户在线状态并返回当前在线人数（5分钟内活跃用户）
      produces:
      - application/json
      responses:
        "200":
          description: 记录成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  additionalProperties:
                    type: integer
                  type: object
              type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - BearerAuth: []
      summary: 记录在线用户
      tags:
      - API/简历管理
  /api/resumes/trash:
    get:
      consumes:
      - application/json
      description: 获取当前用户回收站中的所有简历列表，只返回已删除的简历基本信息，按删除时间排序
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.GetDeletedResumesResponse'
              type: object
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - BearerAuth: []
      summary: 获取回收站简历
      tags:
      - API/简历管理
  /api/target-positions:
    get:
      consumes:
      - application/json
      description: 获取当前用户的所有目标岗位列表
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.TargetPositionListResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - BearerAuth: []
      summary: 获取用户所有目标岗位
      tags:
      - API/目标岗位管理
    post:
      consumes:
      - application/json
      description: 用户创建新的目标岗位信息
      parameters:
      - description: 创建目标岗位请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto_api.CreateTargetPositionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.CreateTargetPositionResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - BearerAuth: []
      summary: 创建目标岗位
      tags:
      - API/目标岗位管理
  /api/target-positions/{id}:
    delete:
      consumes:
      - application/json
      description: 用户删除已有的目标岗位信息
      parameters:
      - description: 岗位ID
        example: 1
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "404":
          description: 目标岗位不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - BearerAuth: []
      summary: 删除目标岗位
      tags:
      - API/目标岗位管理
    get:
      consumes:
      - application/json
      description: 根据岗位ID获取目标岗位的详细信息
      parameters:
      - description: 岗位ID
        example: 1
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.TargetPositionResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "404":
          description: 目标岗位不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - BearerAuth: []
      summary: 根据ID获取目标岗位详情
      tags:
      - API/目标岗位管理
    put:
      consumes:
      - application/json
      description: 用户更新已有的目标岗位信息
      parameters:
      - description: 岗位ID
        example: 1
        in: path
        name: id
        required: true
        type: integer
      - description: 更新目标岗位请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto_api.UpdateTargetPositionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "404":
          description: 目标岗位不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - BearerAuth: []
      summary: 更新目标岗位
      tags:
      - API/目标岗位管理
  /api/templates:
    get:
      consumes:
      - application/json
      description: 分页获取模板列表，返回模板ID、名称、标签、预览图和使用人数
      parameters:
      - description: 页码，默认为1
        example: 1
        in: query
        name: page
        type: integer
      - description: 每页条数，默认为20
        example: 20
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.TemplateListResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 获取模板列表
      tags:
      - API/模板管理
  /api/templates/use:
    post:
      consumes:
      - application/json
      description: 将指定模板应用到简历，更新简历的模板ID和样式配置
      parameters:
      - description: 使用模板请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto_api.UseTemplateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 应用成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "403":
          description: 无权限修改此简历
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "404":
          description: 模板不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 使用模板
      tags:
      - API/模板管理
  /api/upload/attachment:
    post:
      consumes:
      - multipart/form-data
      description: 上传附件，支持PDF、DOC、DOCX、JPG、PNG、ZIP、RAR等格式，最大20MB
      parameters:
      - description: 附件文件
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: 上传成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.UploadResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "413":
          description: 文件过大
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "415":
          description: 不支持的文件类型
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 上传附件
      tags:
      - API/文件上传
  /api/upload/avatar:
    post:
      consumes:
      - multipart/form-data
      description: 上传用户头像，支持jpg、png、webp格式，最大7MB
      parameters:
      - description: 头像文件
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: 上传成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.UploadResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "413":
          description: 文件过大
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "415":
          description: 不支持的文件类型
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 上传头像
      tags:
      - API/文件上传
  /api/users/avatar:
    put:
      consumes:
      - multipart/form-data
      description: 修改当前登录用户的头像，支持jpg、png、webp格式，最大7MB
      parameters:
      - description: 头像文件
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: 修改成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "413":
          description: 文件过大
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "415":
          description: 不支持的文件类型
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 修改用户头像
      tags:
      - API/用户管理
  /api/users/bind-email:
    post:
      consumes:
      - application/json
      description: 为当前登录用户绑定邮箱，需要先发送验证码
      parameters:
      - description: 绑定邮箱请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto_api.BindEmailRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 绑定成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 绑定邮箱
      tags:
      - API/用户管理
  /api/users/bind-phone:
    post:
      consumes:
      - application/json
      description: 为当前登录用户绑定手机号，需要先发送验证码
      parameters:
      - description: 绑定手机号请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto_api.BindPhoneRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 绑定成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 绑定手机号
      tags:
      - API/用户管理
  /api/users/bind-wechat/qrcode:
    get:
      consumes:
      - application/json
      description: 获取当前登录用户绑定微信的二维码，返回二维码图片URL和场景值
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.QrCodeLoginResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 获取绑定微信二维码
      tags:
      - API/用户管理
  /api/users/bind-wechat/qrcode/status:
    post:
      consumes:
      - application/json
      description: 检查绑定微信二维码的扫描状态，如果已扫描则执行绑定操作
      parameters:
      - description: 检查绑定微信二维码状态请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto_api.CheckBindWechatQrCodeStatusRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 检查成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.QrCodeStatusResponse'
              type: object
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 检查绑定微信二维码状态
      tags:
      - API/用户管理
  /api/users/download-coupons/count:
    get:
      consumes:
      - application/json
      description: 获取当前登录用户可用的下载券数量
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.AvailableCouponsCountResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - BearerAuth: []
      summary: 获取用户可用的下载券数量
      tags:
      - API/用户管理
  /api/users/guest/info:
    get:
      consumes:
      - application/json
      description: 获取当前游客用户的基本信息，需要指纹认证
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.UserResponse'
              type: object
        "400":
          description: 缺少浏览器指纹
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      summary: 获取游客用户基本信息
      tags:
      - API/用户管理
  /api/users/info:
    get:
      consumes:
      - application/json
      description: 获取当前登录用户的详细信息
      produces:
      - application/json
      responses:
        "200":
          description: 获取成功
          schema:
            allOf:
            - $ref: '#/definitions/vo.SuccessAPIResponse'
            - properties:
                data:
                  $ref: '#/definitions/vo_api.UserResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 获取当前登录用户信息
      tags:
      - API/用户管理
  /api/users/logout:
    post:
      consumes:
      - application/json
      description: 用户退出登录，清除登录状态
      produces:
      - application/json
      responses:
        "200":
          description: 退出成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 退出登录
      tags:
      - API/用户管理
  /api/users/username:
    put:
      consumes:
      - application/json
      description: 修改当前登录用户的用户名
      parameters:
      - description: 修改用户名请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto_api.UpdateUsernameRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 修改成功
          schema:
            $ref: '#/definitions/vo.SuccessAPIResponse'
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
        "500":
          description: 服务器内部错误
          schema:
            $ref: '#/definitions/vo.ErrorAPIResponse'
      security:
      - ApiKeyAuth: []
      summary: 修改用户名
      tags:
      - API/用户管理
  /api/wechat/serve:
    get:
      description: 处理微信公众号服务器配置时的验证请求
      produces:
      - text/plain
      responses:
        "200":
          description: 成功
          schema:
            type: string
      summary: 处理微信服务器验证请求
      tags:
      - API/微信管理
    post:
      consumes:
      - text/xml
      description: 接收微信公众号的消息推送，包括普通消息、事件推送等
      produces:
      - text/xml
      responses:
        "200":
          description: 成功
          schema:
            type: string
      summary: 接收微信服务器消息推送
      tags:
      - API/微信管理
schemes:
- http
- https
swagger: "2.0"
