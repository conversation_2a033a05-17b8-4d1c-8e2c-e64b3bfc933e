package config

import (
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/spf13/viper"
)

// Config 应用配置结构体
type Config struct {
	App            AppConfig
	Database       DatabaseConfig
	JWT            JWTConfig
	AdminJWT       AdminJWTConfig
	Log            LogConfig
	Swagger        SwaggerConfig
	Redis          RedisConfig
	SMS            SMSConfig
	WeChat         WeChatConfig
	S3             S3Config
	Email          EmailConfig
	AliPay         AliPayConfig
	MeiliSearch    MeiliSearchConfig
	AI             AIConfig
	WebParser      WebParserConfig
	FrontendDomain string // 前端域名
}

// AppConfig 应用基本配置
type AppConfig struct {
	Name  string
	Env   string
	Debug bool
	Port  int
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Connection                string
	Host                      string
	Port                      int
	Database                  string
	Username                  string
	Password                  string
	Charset                   string        // 字符集
	ParseTime                 bool          // 是否解析时间
	TimeZone                  string        // 时区
	DefaultStringSize         uint          // 字符串默认长度
	DisableDatetimePrecision  bool          // 禁用datetime精度
	SkipInitializeWithVersion bool          // 是否根据MySQL版本自动配置
	AutoMigrate               bool          // 是否自动迁移表结构
	SlowSQL                   time.Duration // 慢SQL阈值
	LogLevel                  string        // 日志级别
	IgnoreRecordNotFoundError bool          // 是否忽略记录未找到错误
	MaxIdleConn               int           // 最大空闲连接数
	MaxOpenConn               int           // 最大连接数
	ConnMaxLifetime           time.Duration // 连接最大生命周期
	ConnMaxIdleTime           time.Duration // 最大空闲时间
	// GORM配置
	GormSkipDefaultTx   bool   // 是否跳过默认事务
	GormTablePrefix     string // 表前缀
	GormSingularTable   bool   // 是否使用单数表名
	GormCoverLogger     bool   // 是否覆盖默认logger
	GormPrepareStmt     bool   // 是否使用预处理语句
	GormCloseForeignKey bool   // 是否关闭外键约束
}

// JWTConfig JWT配置
type JWTConfig struct {
	Secret string
	TTL    int
}

// AdminJWTConfig 管理员JWT配置
type AdminJWTConfig struct {
	Secret string
	TTL    int
}

// LogConfig 日志配置
type LogConfig struct {
	Level   string
	Channel string
}

// SwaggerConfig Swagger文档配置
type SwaggerConfig struct {
	Enable bool
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string
	Port     int
	Password string
	DB       int
}

// SMSConfig 短信配置
type SMSConfig struct {
	AccessKeyID       string
	AccessKeySecret   string
	LoginTemplateCode string
	SignName          string
	Endpoint          string
}

// WeChatConfig 微信配置
type WeChatConfig struct {
	AppID     string // 公众号/小程序AppID
	AppSecret string // 公众号/小程序AppSecret
	Token     string // 公众号接口配置Token

	// 微信支付配置
	PayAppID         string // 微信支付AppID
	PayMchID         string // 微信支付商户号
	PayPlatformCerts string // 微信支付平台证书路径
	PayPrivateKey    string // 微信支付私钥路径
	PaySecretKey     string // 微信支付API密钥
	PayCertificate   string // 微信支付证书路径
	PayNotifyURL     string // 微信支付回调通知地址
}

// S3Config S3配置
type S3Config struct {
	AccessKeyID     string
	AccessKeySecret string
	Endpoint        string
	BucketName      string
	SelfUrl         string
	Region          string
}

// EmailConfig 阿里企业邮箱配置
type EmailConfig struct {
	SMTPHost string // SMTP服务器地址
	SMTPPort int    // SMTP服务器端口
	Username string // 邮箱用户名
	Password string // 邮箱密码
	FromName string // 发件人名称
}

// AliPayConfig 支付宝支付配置
type AliPayConfig struct {
	AppID             string // 支付宝应用ID
	AppSecretCertPath string // 支付宝应用私钥文件路径
	AppPublicCertPath string // 应用公钥证书路径
	PublicCertPath    string // 支付宝公钥证书路径
	RootCertPath      string // 支付宝根证书路径
	NotifyURL         string // 支付宝回调通知地址
	IsProduction      bool   // 是否是生产环境
}

// MeiliSearchConfig MeiliSearch配置
type MeiliSearchConfig struct {
	Host   string // MeiliSearch服务地址
	APIKey string // MeiliSearch API密钥
}

// AIConfig AI服务配置
type AIConfig struct {
	APIKey  string // 火山引擎豆包大模型API Key
	ModelID string // 模型ID
}

// WebParserConfig 网页解析服务配置
type WebParserConfig struct {
	BaseURL string        // 网页解析API基础URL
	Timeout time.Duration // HTTP请求超时时间
}

// LoadConfig 从.env文件和环境变量加载配置
func LoadConfig() (*Config, error) {
	// 使用Viper读取配置
	v := viper.New()

	// 设置环境变量前缀和自动读取环境变量
	v.AutomaticEnv()
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// 尝试读取.env文件（如果存在）
	envFile, err := os.ReadFile(".env")
	if err == nil {
		// 转换.env为YAML格式
		yamlContent := parseEnvToYaml(string(envFile))

		// 创建临时YAML文件
		tmpFile, err := os.CreateTemp("", "config-*.yaml")
		if err != nil {
			return nil, fmt.Errorf("无法创建临时YAML文件: %w", err)
		}
		defer os.Remove(tmpFile.Name())

		if _, err := tmpFile.WriteString(yamlContent); err != nil {
			return nil, fmt.Errorf("无法写入临时YAML文件: %w", err)
		}
		tmpFile.Close()

		// 读取.env文件内容
		v.SetConfigFile(tmpFile.Name())
		v.SetConfigType("yaml")
		if err := v.ReadInConfig(); err != nil {
			return nil, fmt.Errorf("无法读取配置文件: %w", err)
		}
	}

	// 设置默认值
	setDefaults(v)

	config := &Config{}

	// 应用配置
	config.App.Name = v.GetString("app.name")
	config.App.Env = v.GetString("app.env")
	config.App.Debug = v.GetBool("app.debug")
	config.App.Port = v.GetInt("app.port")

	// 数据库配置
	config.Database.Connection = v.GetString("db.connection")
	config.Database.Host = v.GetString("db.host")
	config.Database.Port = v.GetInt("db.port")
	config.Database.Database = v.GetString("db.database")
	config.Database.Username = v.GetString("db.username")
	config.Database.Password = v.GetString("db.password")

	// 数据库高级配置
	config.Database.Charset = v.GetString("db.charset")
	config.Database.ParseTime = v.GetBool("db.parse_time")
	config.Database.TimeZone = v.GetString("db.time_zone")
	config.Database.DefaultStringSize = uint(v.GetInt("db.default_string_size"))
	config.Database.DisableDatetimePrecision = v.GetBool("db.disable_datetime_precision")
	config.Database.SkipInitializeWithVersion = v.GetBool("db.skip_initialize_with_version")
	config.Database.AutoMigrate = v.GetBool("db.auto_migrate")
	config.Database.SlowSQL = time.Duration(v.GetInt("db.slow_sql")) * time.Millisecond
	config.Database.LogLevel = v.GetString("db.log_level")
	config.Database.IgnoreRecordNotFoundError = v.GetBool("db.ignore_record_not_found_error")
	config.Database.MaxIdleConn = v.GetInt("db.max_idle_conn")
	config.Database.MaxOpenConn = v.GetInt("db.max_open_conn")
	config.Database.ConnMaxLifetime = time.Duration(v.GetInt("db.conn_max_lifetime")) * time.Hour
	config.Database.ConnMaxIdleTime = time.Duration(v.GetInt("db.conn_max_idle_time")) * time.Hour

	// GORM配置
	config.Database.GormSkipDefaultTx = v.GetBool("db.gorm.skip_default_tx")
	config.Database.GormTablePrefix = v.GetString("db.gorm.table_prefix")
	config.Database.GormSingularTable = v.GetBool("db.gorm.singular_table")
	config.Database.GormCoverLogger = v.GetBool("db.gorm.cover_logger")
	config.Database.GormPrepareStmt = v.GetBool("db.gorm.prepare_stmt")
	config.Database.GormCloseForeignKey = v.GetBool("db.gorm.close_foreign_key")

	// JWT配置
	config.JWT.Secret = v.GetString("jwt.secret")
	config.JWT.TTL = v.GetInt("jwt.ttl")

	// 管理员JWT配置
	config.AdminJWT.Secret = v.GetString("admin_jwt.secret")
	config.AdminJWT.TTL = v.GetInt("admin_jwt.ttl")

	// 日志配置
	config.Log.Level = v.GetString("log.level")
	config.Log.Channel = v.GetString("log.channel")

	// Swagger配置
	config.Swagger.Enable = v.GetBool("swagger.enable")

	// Redis配置
	config.Redis.Host = v.GetString("redis.host")
	config.Redis.Port = v.GetInt("redis.port")
	config.Redis.Password = v.GetString("redis.password")
	config.Redis.DB = v.GetInt("redis.db")

	// 短信配置
	config.SMS.AccessKeyID = v.GetString("sms.access_key_id")
	config.SMS.AccessKeySecret = v.GetString("sms.access_key_secret")
	config.SMS.LoginTemplateCode = v.GetString("sms.login_template_code")
	config.SMS.SignName = v.GetString("sms.sign_name")
	config.SMS.Endpoint = v.GetString("sms.endpoint")

	// 微信配置
	config.WeChat.AppID = v.GetString("wechat.app_id")
	config.WeChat.AppSecret = v.GetString("wechat.app_secret")
	config.WeChat.Token = v.GetString("wechat.token")

	// 微信支付配置
	config.WeChat.PayAppID = v.GetString("wechat.pay_app_id")
	config.WeChat.PayMchID = v.GetString("wechat.pay_mch_id")
	config.WeChat.PayPlatformCerts = v.GetString("wechat.pay_platform_certs")
	config.WeChat.PayPrivateKey = v.GetString("wechat.pay_private_key")
	config.WeChat.PaySecretKey = v.GetString("wechat.pay_secret_key")
	config.WeChat.PayCertificate = v.GetString("wechat.pay_certificate")
	config.WeChat.PayNotifyURL = v.GetString("wechat.pay_notify_url")

	// S3配置
	config.S3 = S3Config{
		AccessKeyID:     v.GetString("s3.access_key_id"),
		AccessKeySecret: v.GetString("s3.access_key_secret"),
		Endpoint:        v.GetString("s3.endpoint"),
		BucketName:      v.GetString("s3.bucket_name"),
		SelfUrl:         v.GetString("s3.self_url"),
		Region:          v.GetString("s3.region"),
	}

	// 邮箱配置
	config.Email = EmailConfig{
		SMTPHost: v.GetString("email.smtp_host"),
		SMTPPort: v.GetInt("email.smtp_port"),
		Username: v.GetString("email.username"),
		Password: v.GetString("email.password"),
		FromName: v.GetString("email.from_name"),
	}

	// 支付宝配置
	config.AliPay = AliPayConfig{
		AppID:             v.GetString("alipay.app_id"),
		AppSecretCertPath: v.GetString("alipay.app_secret_cert"),
		AppPublicCertPath: v.GetString("alipay.app_public_cert_path"),
		PublicCertPath:    v.GetString("alipay.public_cert_path"),
		RootCertPath:      v.GetString("alipay.root_cert_path"),
		NotifyURL:         v.GetString("alipay.notify_url"),
		IsProduction:      v.GetBool("alipay.is_production"),
	}

	// MeiliSearch配置
	config.MeiliSearch = MeiliSearchConfig{
		Host:   v.GetString("meilisearch.host"),
		APIKey: v.GetString("meilisearch.api_key"),
	}

	// AI配置
	config.AI = AIConfig{
		APIKey:  v.GetString("ai.api_key"),
		ModelID: v.GetString("ai.model_id"),
	}

	// WebParser配置
	config.WebParser = WebParserConfig{
		BaseURL: v.GetString("webparser.base_url"),
		Timeout: time.Duration(v.GetInt("webparser.timeout")) * time.Second,
	}

	// 前端域名配置
	config.FrontendDomain = v.GetString("frontend.domain")

	return config, nil
}

// setDefaults 设置配置默认值
func setDefaults(v *viper.Viper) {
	// 应用默认配置
	v.SetDefault("app.name", "Resume Server")
	v.SetDefault("app.env", "development")
	v.SetDefault("app.debug", false)
	v.SetDefault("app.port", 8082)

	// 数据库默认配置
	v.SetDefault("db.connection", "mysql")
	v.SetDefault("db.host", "localhost")
	v.SetDefault("db.port", 3306)
	v.SetDefault("db.charset", "utf8mb4")
	v.SetDefault("db.parse_time", true)
	v.SetDefault("db.time_zone", "Asia%2FShanghai")
	v.SetDefault("db.default_string_size", 255)
	v.SetDefault("db.disable_datetime_precision", true)
	v.SetDefault("db.skip_initialize_with_version", false)
	v.SetDefault("db.auto_migrate", false)
	v.SetDefault("db.slow_sql", 500)
	v.SetDefault("db.log_level", "debug")
	v.SetDefault("db.ignore_record_not_found_error", true)
	v.SetDefault("db.max_idle_conn", 10)
	v.SetDefault("db.max_open_conn", 100)
	v.SetDefault("db.conn_max_lifetime", 1)
	v.SetDefault("db.conn_max_idle_time", 1)

	// GORM默认配置
	v.SetDefault("db.gorm.skip_default_tx", false)
	v.SetDefault("db.gorm.table_prefix", "")
	v.SetDefault("db.gorm.singular_table", true)
	v.SetDefault("db.gorm.cover_logger", true)
	v.SetDefault("db.gorm.prepare_stmt", false)
	v.SetDefault("db.gorm.close_foreign_key", true)

	// JWT默认配置
	v.SetDefault("jwt.ttl", 24)

	// 管理员JWT默认配置
	v.SetDefault("admin_jwt.ttl", 8)

	// 日志默认配置
	v.SetDefault("log.level", "debug")
	v.SetDefault("log.channel", "console")

	// Swagger默认配置
	v.SetDefault("swagger.enable", false)

	// Redis默认配置
	v.SetDefault("redis.host", "localhost")
	v.SetDefault("redis.port", 6379)
	v.SetDefault("redis.db", 0)

	// S3默认配置
	v.SetDefault("s3.region", "us-east-1")

	// 邮箱默认配置
	v.SetDefault("email.smtp_port", 587)

	// WebParser默认配置
	v.SetDefault("webparser.base_url", "http://localhost:8000")
	v.SetDefault("webparser.timeout", 30)

	// 前端域名默认配置
	v.SetDefault("frontend.domain", "https://resume.avrilko.com")
}

// 将.env格式解析为YAML格式
func parseEnvToYaml(envContent string) string {
	var yamlParts []string
	lines := strings.Split(envContent, "\n")

	var currentSection string
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) != 2 {
			continue
		}

		key := strings.TrimSpace(parts[0])
		value := strings.TrimSpace(parts[1])

		// 根据命名约定确定配置段
		var section, subKey string
		if strings.HasPrefix(key, "APP_") {
			section = "app"
			subKey = strings.ToLower(strings.TrimPrefix(key, "APP_"))
		} else if strings.HasPrefix(key, "DB_GORM_") {
			section = "db.gorm"
			subKey = strings.ToLower(strings.TrimPrefix(key, "DB_GORM_"))
		} else if strings.HasPrefix(key, "DB_") {
			section = "db"
			subKey = strings.ToLower(strings.TrimPrefix(key, "DB_"))
		} else if strings.HasPrefix(key, "ADMIN_JWT_") {
			section = "admin_jwt"
			subKey = strings.ToLower(strings.TrimPrefix(key, "ADMIN_JWT_"))
		} else if strings.HasPrefix(key, "JWT_") {
			section = "jwt"
			subKey = strings.ToLower(strings.TrimPrefix(key, "JWT_"))
		} else if strings.HasPrefix(key, "LOG_") {
			section = "log"
			subKey = strings.ToLower(strings.TrimPrefix(key, "LOG_"))
		} else if strings.HasPrefix(key, "SWAGGER_") {
			section = "swagger"
			subKey = strings.ToLower(strings.TrimPrefix(key, "SWAGGER_"))
		} else if strings.HasPrefix(key, "REDIS_") {
			section = "redis"
			subKey = strings.ToLower(strings.TrimPrefix(key, "REDIS_"))
		} else if strings.HasPrefix(key, "SMS_") {
			section = "sms"
			subKey = strings.ToLower(strings.TrimPrefix(key, "SMS_"))
		} else if strings.HasPrefix(key, "WECHAT_") {
			section = "wechat"
			subKey = strings.ToLower(strings.TrimPrefix(key, "WECHAT_"))
		} else if strings.HasPrefix(key, "S3_") {
			section = "s3"
			subKey = strings.ToLower(strings.TrimPrefix(key, "S3_"))
		} else if strings.HasPrefix(key, "EMAIL_") {
			section = "email"
			subKey = strings.ToLower(strings.TrimPrefix(key, "EMAIL_"))
		} else if strings.HasPrefix(key, "ALI_PAY_") {
			section = "alipay"
			subKey = strings.ToLower(strings.TrimPrefix(key, "ALI_PAY_"))
		} else if strings.HasPrefix(key, "MEILISEARCH_") {
			section = "meilisearch"
			subKey = strings.ToLower(strings.TrimPrefix(key, "MEILISEARCH_"))
		} else if strings.HasPrefix(key, "AI_") {
			section = "ai"
			subKey = strings.ToLower(strings.TrimPrefix(key, "AI_"))
		} else if strings.HasPrefix(key, "WEBPARSER_") {
			section = "webparser"
			subKey = strings.ToLower(strings.TrimPrefix(key, "WEBPARSER_"))
		} else if strings.HasPrefix(key, "FRONTEND_") {
			section = "frontend"
			subKey = strings.ToLower(strings.TrimPrefix(key, "FRONTEND_"))
		} else {
			section = "misc"
			subKey = strings.ToLower(key)
		}

		if section != currentSection {
			currentSection = section
			yamlParts = append(yamlParts, fmt.Sprintf("%s:", section))
		}

		// 将值格式化为YAML格式
		if value == "true" || value == "false" || isNumeric(value) {
			yamlParts = append(yamlParts, fmt.Sprintf("  %s: %s", subKey, value))
		} else {
			yamlParts = append(yamlParts, fmt.Sprintf("  %s: \"%s\"", subKey, escapeYamlString(value)))
		}
	}

	return strings.Join(yamlParts, "\n")
}

// 检查字符串是否是数字
func isNumeric(s string) bool {
	_, err := fmt.Sscanf(s, "%f", new(float64))
	return err == nil
}

// 转义YAML字符串中的特殊字符
func escapeYamlString(s string) string {
	s = strings.ReplaceAll(s, "\\", "\\\\")
	s = strings.ReplaceAll(s, "\"", "\\\"")
	return s
}
