APP_NAME=熊猫简历
APP_ENV=production
APP_DEBUG=false
APP_PORT=8082

# 数据库配置
DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=resume_server
DB_USERNAME=root
DB_PASSWORD=UJqjSm7aKsLA8oNa9MOGbAz2

# 数据库高级配置
DB_CHARSET=utf8mb4
DB_PARSE_TIME=true
DB_TIME_ZONE=Local
DB_DEFAULT_STRING_SIZE=255
DB_DISABLE_DATETIME_PRECISION=true
DB_SKIP_INITIALIZE_WITH_VERSION=false
DB_AUTO_MIGRATE=false
DB_SLOW_SQL=500
DB_LOG_LEVEL=debug
DB_IGNORE_RECORD_NOT_FOUND_ERROR=true
DB_MAX_IDLE_CONN=10
DB_MAX_OPEN_CONN=100
DB_CONN_MAX_LIFETIME=1
DB_CONN_MAX_IDLE_TIME=1

# GORM配置
DB_GORM_SKIP_DEFAULT_TX=false
DB_GORM_TABLE_PREFIX=
DB_GORM_SINGULAR_TABLE=true
DB_GORM_COVER_LOGGER=true
DB_GORM_PREPARE_STMT=false
DB_GORM_CLOSE_FOREIGN_KEY=true

# JWT配置
JWT_SECRET=SGcCvhV6lStn65xfRWN1qjJ2F2dx95uVufQeahwk2fM=
JWT_TTL=864000

# 管理员JWT配置
ADMIN_JWT_SECRET=AdminSecretKey2024PandaResumeManagementSystem
ADMIN_JWT_TTL=864000

# 日志配置
LOG_LEVEL=info
LOG_CHANNEL=console

# Swagger配置
SWAGGER_ENABLE=false

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=UJqjSm7aKsLA8oNa9MOGbAz2
REDIS_DB=0

# MeiliSearch配置
MEILISEARCH_HOST=http://meilisearch:7700
MEILISEARCH_API_KEY=CQx4eUF6-IUFHVJzJuh4koCP54K8ZqOIJYl3Du31J4E

# 阿里云短信配置
SMS_ACCESS_KEY_ID=LTAI5tFNNbBx6RmSQT9BS3E7
SMS_ACCESS_KEY_SECRET=******************************
SMS_LOGIN_TEMPLATE_CODE=SMS_319096226
SMS_SIGN_NAME=自品网络
SMS_ENDPOINT=dysmsapi.aliyuncs.com

# 微信配置
WECHAT_APP_ID=wxe9872dc9df660e7b
WECHAT_APP_SECRET=80b98ae127ae337f1e1e03156098228b
WECHAT_TOKEN=2e29b36df34df5cdacf2d5e206721748

# 微信支付配置
WECHAT_PAY_APP_ID=wxe9872dc9df660e7b
WECHAT_PAY_MCH_ID=1718394860
WECHAT_PAY_PLATFORM_CERTS=/app/cert/cert.pem
WECHAT_PAY_PRIVATE_KEY=/app/cert/apiclient_key.pem
WECHAT_PAY_SECRET_KEY=61236027705e48ef3ff7f71f156a7137
WECHAT_PAY_CERTIFICATE=/app/cert/apiclient_cert.pem
WECHAT_PAY_NOTIFY_URL=https://www.pandaresume.cn/api/openapi/wechatpay

S3_ACCESS_KEY_ID=AKIDVt9IE0ay98wZ6dZQ7gEcqywtkOp22lOp
S3_ACCESS_KEY_SECRET=NWaVGtKAyafopdGiy2oSt672ua2LRO91
S3_ENDPOINT=https://resume-1323698408.cos.ap-shanghai.myqcloud.com
S3_BUCKET_NAME=resume-1323698408
S3_SELF_URL=https://cdn.pandaresume.com
S3_REGION=ap-shanghai

# 阿里企业邮箱配置
EMAIL_SMTP_HOST=smtp.qiye.aliyun.com
EMAIL_SMTP_PORT=465
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=oZKXW21!uho(nP5R3&pEfChkRlThMSg#
EMAIL_FROM_NAME=熊猫简历

# 支付宝配置
ALI_PAY_APP_ID=2021005106664115
ALI_PAY_APP_SECRET_CERT=/app/cert/alicert/alipay_app_private_key.pem
ALI_PAY_APP_PUBLIC_CERT_PATH=/app/cert/alicert/appCertPublicKey_2021005106664115.crt
ALI_PAY_PUBLIC_CERT_PATH=/app/cert/alicert/alipayCertPublicKey_RSA2.crt
ALI_PAY_ROOT_CERT_PATH=/app/cert/alicert/alipayRootCert.crt
ALI_PAY_NOTIFY_URL=https://www.pandaresume.cn/api/openapi/alipay
ALI_PAY_IS_PRODUCTION=true

# AI配置 - 火山引擎豆包大模型
AI_API_KEY=855bcd70-a1e5-4857-aa51-2e4bb7bd3f04
AI_MODEL_ID=deepseek-v3-250324

# 网页解析服务配置
WEBPARSER_BASE_URL=http://link-reader-api:8000
WEBPARSER_TIMEOUT=30

# 前端域名配置
FRONTEND_DOMAIN=https://www.pandaresume.cn
