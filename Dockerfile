# 第一阶段：构建阶段
FROM golang:1.23-alpine AS builder

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV GOPROXY=https://goproxy.cn,direct
ENV CGO_ENABLED=1

# 安装构建依赖
RUN apk add --no-cache gcc musl-dev

# 复制 go.mod 和 go.sum 文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -o resume-server .

# 构建MeiliSearch初始化脚本
RUN CGO_ENABLED=0 GOOS=linux go build -o init-meilisearch scripts/run_init_meilisearch.go

# 第二阶段：运行阶段
FROM fedora:39

# 设置工作目录
WORKDIR /app

# 安装系统依赖和 Chromium（无头模式最小依赖）
RUN dnf update -y && \
    dnf install -y \
        # Chromium 浏览器
        chromium \
        # 字体相关（PDF生成必需）
        fontconfig \
        dejavu-sans-fonts \
        dejavu-serif-fonts \
        dejavu-sans-mono-fonts \
        # 中文字体支持
        google-noto-cjk-fonts \
        wqy-microhei-fonts \
        wqy-zenhei-fonts \
        # 无头Chrome最小必需库
        nss \
        # 清理缓存
    && dnf clean all \
    && rm -rf /var/cache/dnf

# 创建字体目录并设置权限
RUN mkdir -p /usr/share/fonts/custom && \
    fc-cache -fv

# 复制项目字体文件
COPY fonts/ /usr/share/fonts/custom/

# 更新字体缓存
RUN fc-cache -fv

# 从构建阶段复制编译好的二进制文件
COPY --from=builder /app/resume-server .
COPY --from=builder /app/init-meilisearch .

# 复制.env-docker文件作为.env（如果存在）
COPY .env-docker .env

# 创建非 root 用户
RUN useradd -m -s /bin/bash appuser && \
    chown -R appuser:appuser /app

# 切换到非 root 用户
USER appuser

# 设置 Chromium 环境变量
ENV CHROME_BIN=/usr/bin/chromium-browser
ENV CHROME_PATH=/usr/bin/chromium-browser

# 暴露端口
EXPOSE 8082

# 启动应用
CMD ["./resume-server"]
