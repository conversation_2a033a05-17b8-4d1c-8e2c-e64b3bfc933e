package main

import (
	"fmt"
	"log"
	"os"

	"go.uber.org/zap"
)

// 简单示例脚本：展示如何使用脚本依赖注入系统
func main() {
	// 初始化脚本基础
	scriptBase, err := NewScriptBase()
	if err != nil {
		log.Fatalf("初始化脚本基础失败: %v", err)
	}
	defer scriptBase.Close()

	scriptBase.Info("简单脚本开始执行")

	// 获取脚本服务
	scriptService := scriptBase.GetScriptService()

	// 示例1: 获取配置信息
	config := scriptService.GetConfig()
	scriptBase.Info("应用配置",
		zap.String("app_name", config.App.Name),
		zap.String("app_env", config.App.Env),
		zap.Int("app_port", config.App.Port))

	// 示例2: 获取数据库连接
	db := scriptService.GetDB()
	scriptBase.Info("数据库连接已获取", zap.String("db_type", fmt.Sprintf("%T", db)))

	// 示例3: 获取Redis客户端
	redis := scriptService.GetRedis()
	scriptBase.Info("Redis客户端已获取", zap.String("redis_type", fmt.Sprintf("%T", redis)))

	// 示例4: 获取用户服务
	userService := scriptService.GetUserService()
	scriptBase.Info("用户服务已获取", zap.String("service_type", fmt.Sprintf("%T", userService)))

	// 示例5: 获取简历服务
	resumeService := scriptService.GetResumeService()
	scriptBase.Info("简历服务已获取", zap.String("service_type", fmt.Sprintf("%T", resumeService)))

	// 示例6: 获取职位服务
	positionService := scriptService.GetPositionService()
	scriptBase.Info("职位服务已获取", zap.String("service_type", fmt.Sprintf("%T", positionService)))

	// 示例7: 获取AI服务
	aiService := scriptService.GetAIService()
	scriptBase.Info("AI服务已获取", zap.String("service_type", fmt.Sprintf("%T", aiService)))

	// 示例8: 获取其他基础设施服务
	ossService := scriptService.GetOSSService()
	smsService := scriptService.GetSMSService()
	emailService := scriptService.GetEmailService()

	scriptBase.Info("基础设施服务已获取",
		zap.String("oss_service", fmt.Sprintf("%T", ossService)),
		zap.String("sms_service", fmt.Sprintf("%T", smsService)),
		zap.String("email_service", fmt.Sprintf("%T", emailService)))

	scriptBase.Info("简单脚本执行完成")

	// 检查命令行参数，如果是 init-meilisearch，则执行MeiliSearch初始化
	if len(os.Args) > 1 && os.Args[1] == "init-meilisearch" {
		scriptBase.Info("检测到init-meilisearch参数，开始执行MeiliSearch数据初始化")
		InitializeMeiliSearchData()
		return
	}

	// 在这里可以调用具体的业务方法
	// 例如：
	// user, err := userService.GetUserByID(1)
	// if err != nil {
	//     scriptBase.Error("获取用户失败", zap.Error(err))
	//     return
	// }
	// scriptBase.Info("获取用户成功", zap.String("nickname", user.Nickname))
}
