package main

import (
	"encoding/xml"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"go.uber.org/zap"
	"resume-server/internal/models"
)

// 生成sitemap.xml脚本：获取position、category、example表数据生成百度格式的sitemap
//
// 使用方法：
// 1. 确保 .env 文件已正确配置（数据库等）
// 2. 在项目根目录运行：go run scripts/generate_urls.go scripts/script_base.go
// 3. 生成的sitemap.xml文件将保存在项目根目录
//
// 脚本功能：
// - 获取position表中level为1和3的数据，生成 https://www.pandaresume.cn/jianli/{slug_cn}/
// - 获取category表数据，生成 https://www.pandaresume.cn/jianli/{slug_cn}/
// - 获取example表数据，生成 https://www.pandaresume.cn/jianli/{id}.html

// URL 结构体，用于生成 sitemap XML
type URL struct {
	XMLName    xml.Name `xml:"url"`
	Loc        string   `xml:"loc"`
	LastMod    string   `xml:"lastmod"`
	ChangeFreq string   `xml:"changefreq"`
	Priority   string   `xml:"priority"`
}

// URLSet 结构体，用于生成 sitemap XML
type URLSet struct {
	XMLName xml.Name `xml:"urlset"`
	URLs    []URL    `xml:"url"`
}

func main() {
	// 创建脚本基础实例
	scriptBase, err := NewScriptBase()
	if err != nil {
		fmt.Printf("初始化脚本失败: %v\n", err)
		os.Exit(1)
	}
	defer scriptBase.Close()

	scriptBase.Info("开始生成sitemap.xml文件")

	// 获取数据库连接
	db := scriptBase.ScriptService.GetDB()
	if db == nil {
		scriptBase.Fatal("获取数据库连接失败")
		return
	}

	// 创建 URLSet 结构体
	urlSet := URLSet{}
	var totalCount int

	// 0. 添加基础页面 URLs
	scriptBase.Info("添加基础页面URLs")
	baseURLs := []struct {
		url        string
		priority   string
		changefreq string
	}{
		{"https://www.pandaresume.cn/", "1.0", "weekly"},
		{"https://www.pandaresume.cn/jianli/", "1.0", "weekly"},
	}

	for _, baseURL := range baseURLs {
		urlSet.URLs = append(urlSet.URLs, URL{
			Loc:        baseURL.url,
			LastMod:    time.Now().Format("2006-01-02"),
			ChangeFreq: baseURL.changefreq,
			Priority:   baseURL.priority,
		})
		totalCount++
	}

	// 1. 获取position表数据 (level in (1,3))
	scriptBase.Info("开始获取position表数据")
	var positions []models.Position
	if err := db.Where("level IN (?)", []int{1, 3}).Find(&positions).Error; err != nil {
		scriptBase.Fatal("查询position表失败", zap.Error(err))
		return
	}

	scriptBase.Info("获取position数据完成", zap.Int("count", len(positions)))

	// 添加position URLs到sitemap
	for _, position := range positions {
		if position.SlugCn != "" {
			url := fmt.Sprintf("https://www.pandaresume.cn/jianli/%s/", position.SlugCn)

			// 获取最后修改时间，如果没有则使用当前时间
			lastMod := time.Now().Format("2006-01-02")
			if !position.UpdatedAt.IsZero() {
				lastMod = position.UpdatedAt.Format("2006-01-02")
			}

			urlSet.URLs = append(urlSet.URLs, URL{
				Loc:        url,
				LastMod:    lastMod,
				ChangeFreq: "weekly",
				Priority:   "1.0",
			})
			totalCount++
		}
	}

	// 2. 获取category表数据
	scriptBase.Info("开始获取category表数据")
	var categories []models.Category
	if err := db.Find(&categories).Error; err != nil {
		scriptBase.Fatal("查询category表失败", zap.Error(err))
		return
	}

	scriptBase.Info("获取category数据完成", zap.Int("count", len(categories)))

	// 添加category URLs到sitemap
	for _, category := range categories {
		if category.SlugCn != "" {
			url := fmt.Sprintf("https://www.pandaresume.cn/jianli/%s/", category.SlugCn)

			// 获取最后修改时间，如果没有则使用当前时间
			lastMod := time.Now().Format("2006-01-02")
			if !category.UpdatedAt.IsZero() {
				lastMod = category.UpdatedAt.Format("2006-01-02")
			}

			urlSet.URLs = append(urlSet.URLs, URL{
				Loc:        url,
				LastMod:    lastMod,
				ChangeFreq: "weekly",
				Priority:   "1.0",
			})
			totalCount++
		}
	}

	// 3. 获取example表数据
	scriptBase.Info("开始获取example表数据")
	var examples []models.Example
	if err := db.Find(&examples).Error; err != nil {
		scriptBase.Fatal("查询example表失败", zap.Error(err))
		return
	}

	scriptBase.Info("获取example数据完成", zap.Int("count", len(examples)))

	// 添加example URLs到sitemap
	for _, example := range examples {
		url := fmt.Sprintf("https://www.pandaresume.cn/jianli/%d.html", example.ID)

		// 获取最后修改时间，如果没有则使用当前时间
		lastMod := time.Now().Format("2006-01-02")
		if !example.UpdatedAt.IsZero() {
			lastMod = example.UpdatedAt.Format("2006-01-02")
		}

		urlSet.URLs = append(urlSet.URLs, URL{
			Loc:        url,
			LastMod:    lastMod,
			ChangeFreq: "monthly",
			Priority:   "1.0",
		})
		totalCount++
	}

	// 生成sitemap.xml文件
	outputFile := "sitemap.xml"
	file, err := os.Create(outputFile)
	if err != nil {
		scriptBase.Fatal("创建sitemap.xml文件失败", zap.Error(err))
		return
	}
	defer file.Close()

	// 写入XML头部
	if _, err := file.WriteString(`<?xml version="1.0" encoding="UTF-8"?>` + "\n"); err != nil {
		scriptBase.Fatal("写入XML头部失败", zap.Error(err))
		return
	}

	// 编码XML内容
	encoder := xml.NewEncoder(file)
	encoder.Indent("", "\t")

	if err := encoder.Encode(urlSet); err != nil {
		scriptBase.Fatal("编码XML失败", zap.Error(err))
		return
	}

	// 获取文件绝对路径
	absPath, err := filepath.Abs(outputFile)
	if err != nil {
		absPath = outputFile
	}

	scriptBase.Info("sitemap.xml文件生成完成",
		zap.String("file_path", absPath),
		zap.Int("total_urls", totalCount),
		zap.Int("base_urls", len(baseURLs)),
		zap.Int("position_count", len(positions)),
		zap.Int("category_count", len(categories)),
		zap.Int("example_count", len(examples)))

	fmt.Printf("✅ sitemap.xml文件生成完成！\n")
	fmt.Printf("📁 文件路径: %s\n", absPath)
	fmt.Printf("📊 总URL数量: %d\n", totalCount)
	fmt.Printf("   - Base URLs: %d (首页和简历页)\n", len(baseURLs))
	fmt.Printf("   - Position URLs: %d (level 1和3)\n", len(positions))
	fmt.Printf("   - Category URLs: %d\n", len(categories))
	fmt.Printf("   - Example URLs: %d\n", len(examples))
}
