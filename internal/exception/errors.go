package exception

import "net/http"

// 预定义错误 - 通用错误 (100-999)
var (
	// 参数错误
	ErrInvalidParam = New(http.StatusBadRequest, 100, "参数错误")

	// 认证错误
	ErrUnauthorized = New(http.StatusUnauthorized, 101, "未授权")
	ErrTokenInvalid = New(http.StatusUnauthorized, 102, "无效的令牌")
	ErrTokenExpired = New(http.StatusUnauthorized, 103, "令牌已过期")

	// 权限错误
	ErrForbidden = New(http.StatusForbidden, 200, "权限不足")

	// 资源不存在
	ErrNotFound = New(http.StatusNotFound, 300, "资源不存在")

	// 服务器错误
	ErrInternalServer = New(http.StatusInternalServerError, 500, "服务器内部错误")

	// 验证相关错误
	ErrValidationFailed = New(http.StatusBadRequest, 600, "参数验证失败")

	// 频率限制错误
	ErrRateLimit = New(http.StatusTooManyRequests, 700, "请求过于频繁")
)

// 预定义错误 - 用户相关错误 (1000-1099)
var (
	// 用户基本错误
	ErrUserNotFound = New(http.StatusNotFound, 1000, "用户不存在")

	// 用户登录相关错误
	ErrUserLoginFailed    = New(http.StatusInternalServerError, 1010, "用户登录失败")
	ErrTokenGenFailed     = New(http.StatusInternalServerError, 1011, "生成用户令牌失败")
	ErrUserCreateFailed   = New(http.StatusInternalServerError, 1012, "创建用户失败")
	ErrUserUpdateFailed   = New(http.StatusInternalServerError, 1013, "更新用户信息失败")
	ErrUserStatusDisabled = New(http.StatusForbidden, 1015, "用户已禁用")
	ErrPhoneAlreadyBound  = New(http.StatusBadRequest, 1016, "手机号已被其他用户绑定")
	ErrUserNameGenFailed  = New(http.StatusInternalServerError, 1017, "生成用户名失败")

	// 用户名修改相关错误
	ErrUsernameInvalid      = New(http.StatusBadRequest, 1020, "用户名格式不正确")
	ErrUsernameTooShort     = New(http.StatusBadRequest, 1021, "用户名长度不能少于2个字符")
	ErrUsernameTooLong      = New(http.StatusBadRequest, 1022, "用户名长度不能超过20个字符")
	ErrUsernameUpdateFailed = New(http.StatusInternalServerError, 1023, "用户名修改失败")

	// 头像修改相关错误
	ErrAvatarInvalid      = New(http.StatusBadRequest, 1024, "头像URL格式不正确")
	ErrAvatarUpdateFailed = New(http.StatusInternalServerError, 1025, "头像修改失败")
	ErrAvatarUploadFailed = New(http.StatusInternalServerError, 1026, "头像上传失败")
)

// 预定义错误 - 上传相关错误 (1100-1199)
var (
	ErrFileTooLarge         = New(http.StatusRequestEntityTooLarge, 1100, "文件过大")
	ErrFileTypeNotSupported = New(http.StatusUnsupportedMediaType, 1101, "不支持的文件类型")
	ErrFileUploadFailed     = New(http.StatusInternalServerError, 1102, "文件上传失败")
	ErrFileOpenFailed       = New(http.StatusInternalServerError, 1103, "打开文件失败")
	ErrFileReadFailed       = New(http.StatusInternalServerError, 1104, "读取文件内容失败")
)

// 预定义错误 - 验证码相关错误 (1200-1299)
var (
	// 短信验证码相关错误
	ErrSMSCodeInvalid    = New(http.StatusBadRequest, 1200, "验证码无效或已过期")
	ErrSMSCodeSendFailed = New(http.StatusInternalServerError, 1201, "发送验证码失败")
	ErrSMSCodeRateLimit  = New(http.StatusTooManyRequests, 1202, "发送验证码过于频繁")
	ErrPhoneInvalid      = New(http.StatusBadRequest, 1203, "手机号码格式无效")
)

// 预定义错误 - 二维码相关错误 (1300-1399)
var (
	// 二维码基本错误
	ErrQrCodeNotFound    = New(http.StatusNotFound, 1300, "二维码不存在或已过期")
	ErrQrCodeParseFailed = New(http.StatusInternalServerError, 1301, "解析二维码信息失败")
	ErrQrCodeGenFailed   = New(http.StatusInternalServerError, 1303, "生成二维码失败")

	// 二维码扫码登录错误
	ErrQrCodeBindFailed = New(http.StatusInternalServerError, 1311, "绑定二维码失败")
	ErrQrCodeExpired    = New(http.StatusBadRequest, 1312, "二维码已过期")
)

// 预定义错误 - 邮箱相关错误 (1400-1499)
var (
	// 邮箱基本错误
	ErrEmailInvalid  = New(http.StatusBadRequest, 1400, "邮箱格式无效")
	ErrEmailNotFound = New(http.StatusNotFound, 1401, "邮箱不存在")

	// 邮件发送错误
	ErrEmailSendFailed = New(http.StatusInternalServerError, 1410, "发送邮件失败")
	ErrEmailRateLimit  = New(http.StatusTooManyRequests, 1411, "发送邮件过于频繁")

	// 邮件验证码错误
	ErrEmailCodeInvalid      = New(http.StatusBadRequest, 1420, "邮件验证码无效或已过期")
	ErrEmailCodeSendFailed   = New(http.StatusInternalServerError, 1421, "发送邮件验证码失败")
	ErrEmailCodeVerifyFailed = New(http.StatusInternalServerError, 1422, "验证邮件验证码失败")
)

// 预定义错误 - 会员套餐相关错误 (1500-1599)
var (
	// 会员套餐基本错误
	ErrMembershipPlanNotFound = New(http.StatusNotFound, 1500, "会员套餐不存在")
	ErrMembershipPlanInactive = New(http.StatusBadRequest, 1501, "会员套餐已下架")

	// 会员套餐查询错误
	ErrMembershipPlanQueryFailed = New(http.StatusInternalServerError, 1510, "获取会员套餐失败")

	// 会员购买相关错误
	ErrMembershipPurchaseFailed = New(http.StatusInternalServerError, 1520, "购买会员套餐失败")
	ErrMembershipActivateFailed = New(http.StatusInternalServerError, 1521, "激活会员套餐失败")
)

// 预定义错误 - 订单相关错误 (1600-1699)
var (
	// 订单基本错误
	ErrOrderNotFound = New(http.StatusNotFound, 1600, "订单不存在")
	ErrOrderExpired  = New(http.StatusBadRequest, 1601, "订单已过期")

	// 订单创建相关错误
	ErrOrderCreateFailed = New(http.StatusInternalServerError, 1610, "创建订单失败")
	ErrOrderUpdateFailed = New(http.StatusInternalServerError, 1611, "更新订单失败")

	// 支付相关错误
	ErrPaymentFailed          = New(http.StatusInternalServerError, 1620, "支付失败")
	ErrPaymentInterfaceFailed = New(http.StatusInternalServerError, 1621, "调用支付接口失败")
	ErrPaymentTimeout         = New(http.StatusRequestTimeout, 1622, "支付超时")
	ErrPaymentCancelled       = New(http.StatusBadRequest, 1623, "支付已取消")

	// 订单查询相关错误
	ErrOrderQueryFailed = New(http.StatusInternalServerError, 1630, "查询订单失败")
)

// 预定义错误 - 职位相关错误 (1700-1799)
var (
	// 职位基本错误
	ErrPositionNotFound = New(http.StatusNotFound, 1700, "职位不存在")

	// 职位参数错误
	ErrPositionParentIDInvalid = New(http.StatusBadRequest, 1710, "父级职位ID无效")
	ErrPositionParentIDEmpty   = New(http.StatusBadRequest, 1711, "父级职位ID不能为空")

	// 职位查询相关错误
	ErrPositionQueryFailed           = New(http.StatusInternalServerError, 1720, "获取职位数据失败")
	ErrPositionHierarchyFailed       = New(http.StatusInternalServerError, 1721, "构建职位层级结构失败")
	ErrPositionChildrenQueryFailed   = New(http.StatusInternalServerError, 1722, "获取子分类数据失败")
	ErrPositionParentIDConvertFailed = New(http.StatusInternalServerError, 1723, "父级ID参数转换失败")

	// 职位搜索相关错误
	ErrPositionSearchFailed      = New(http.StatusInternalServerError, 1730, "搜索职位失败")
	ErrPositionSearchUnavailable = New(http.StatusServiceUnavailable, 1731, "搜索服务暂时不可用")
)

// 预定义错误 - 简历相关错误 (1800-1899)
var (
	// 简历基本错误
	ErrResumeNotFound = New(http.StatusNotFound, 1800, "简历不存在")
	ErrResumeAccess   = New(http.StatusForbidden, 1801, "无权访问该简历")

	// 简历参数错误
	ErrResumeIDInvalid = New(http.StatusBadRequest, 1810, "简历ID无效")
	ErrResumeIDEmpty   = New(http.StatusBadRequest, 1811, "简历ID不能为空")

	// 简历查询相关错误
	ErrResumeQueryFailed       = New(http.StatusInternalServerError, 1820, "获取简历数据失败")
	ErrResumeDetailQueryFailed = New(http.StatusInternalServerError, 1821, "获取简历详情失败")
	ErrResumeOwnershipFailed   = New(http.StatusInternalServerError, 1822, "检查简历权限失败")

	// 简历操作相关错误
	ErrResumeDeleteFailed = New(http.StatusInternalServerError, 1825, "删除简历失败")

	// 简历会员权限相关错误
	ErrResumeMembershipRequired     = New(http.StatusForbidden, 1830, "此功能仅限会员使用，请升级为会员")
	ErrResumeDownloadMemberRequired = New(http.StatusForbidden, 1831, "下载简历PDF功能仅限会员使用")
	ErrResumeShareMemberRequired    = New(http.StatusForbidden, 1832, "邮件分享简历功能仅限会员使用")
)

// 预定义错误 - 示例相关错误 (1900-1999)
var (
	// 示例基本错误
	ErrExampleNotFound = New(http.StatusNotFound, 1900, "示例不存在")

	// 示例参数错误
	ErrExampleIDInvalid = New(http.StatusBadRequest, 1910, "示例ID无效")
	ErrExampleIDEmpty   = New(http.StatusBadRequest, 1911, "示例ID不能为空")

	// 示例查询相关错误
	ErrExampleQueryFailed       = New(http.StatusInternalServerError, 1920, "获取示例数据失败")
	ErrExampleDetailQueryFailed = New(http.StatusInternalServerError, 1921, "获取示例详情失败")
	ErrExampleTdkQueryFailed    = New(http.StatusInternalServerError, 1922, "获取示例TDK信息失败")

	// 示例使用相关错误
	ErrExampleUseFailed = New(http.StatusInternalServerError, 1930, "使用示例创建简历失败")
)

// 预定义错误 - 分类相关错误 (2000-2099)
var (
	// 分类基本错误
	ErrCategoryNotFound = New(http.StatusNotFound, 2000, "分类不存在")

	// 分类参数错误
	ErrCategorySlugInvalid = New(http.StatusBadRequest, 2010, "分类slug无效")
	ErrCategorySlugEmpty   = New(http.StatusBadRequest, 2011, "分类slug不能为空")

	// 分类查询相关错误
	ErrCategoryQueryFailed       = New(http.StatusInternalServerError, 2020, "获取分类数据失败")
	ErrCategoryDetailQueryFailed = New(http.StatusInternalServerError, 2021, "获取分类详情失败")
	ErrCategoryTdkQueryFailed    = New(http.StatusInternalServerError, 2022, "获取分类TDK信息失败")
)

// 预定义错误 - 模板相关错误 (2100-2199)
var (
	// 模板基本错误
	ErrTemplateNotFound = New(http.StatusNotFound, 2100, "模板不存在")

	// 模板参数错误
	ErrTemplateIDInvalid = New(http.StatusBadRequest, 2110, "模板ID无效")
	ErrTemplateIDEmpty   = New(http.StatusBadRequest, 2111, "模板ID不能为空")

	// 模板查询相关错误
	ErrTemplateQueryFailed     = New(http.StatusInternalServerError, 2120, "获取模板数据失败")
	ErrTemplateListQueryFailed = New(http.StatusInternalServerError, 2121, "获取模板列表失败")

	// 模板使用相关错误
	ErrTemplateUseFailed           = New(http.StatusInternalServerError, 2130, "应用模板失败")
	ErrTemplateResumeOwnershipFail = New(http.StatusForbidden, 2131, "无权限修改此简历")
	ErrTemplateUpdateResumeIDFail  = New(http.StatusInternalServerError, 2132, "更新简历模板ID失败")
	ErrTemplateUpdateStyleFail     = New(http.StatusInternalServerError, 2133, "更新简历样式失败")
)

// 预定义错误 - 目标岗位相关错误 (2200-2299)
var (
	// 目标岗位基本错误
	ErrTargetPositionNotFound = New(http.StatusNotFound, 2200, "目标岗位不存在")

	// 目标岗位参数错误
	ErrTargetPositionIDInvalid = New(http.StatusBadRequest, 2210, "目标岗位ID无效")
	ErrTargetPositionIDEmpty   = New(http.StatusBadRequest, 2211, "目标岗位ID不能为空")

	// 目标岗位查询相关错误
	ErrTargetPositionQueryFailed = New(http.StatusInternalServerError, 2220, "获取目标岗位数据失败")
	ErrTargetPositionListFailed  = New(http.StatusInternalServerError, 2221, "获取目标岗位列表失败")

	// 目标岗位操作相关错误
	ErrTargetPositionCreateFailed = New(http.StatusInternalServerError, 2230, "创建目标岗位失败")
	ErrTargetPositionUpdateFailed = New(http.StatusInternalServerError, 2231, "更新目标岗位失败")
	ErrTargetPositionDeleteFailed = New(http.StatusInternalServerError, 2232, "删除目标岗位失败")
)

// 预定义错误 - 管理员相关错误 (2300-2399)
var (
	// 管理员基本错误
	ErrAdminNotFound = New(http.StatusNotFound, 2300, "管理员不存在")
	ErrAdminDisabled = New(http.StatusForbidden, 2301, "管理员账号已被禁用")

	// 管理员登录相关错误
	ErrAdminLoginFailed        = New(http.StatusUnauthorized, 2310, "用户名或密码错误")
	ErrAdminPasswordIncorrect  = New(http.StatusUnauthorized, 2311, "密码错误")
	ErrAdminTokenGenFailed     = New(http.StatusInternalServerError, 2312, "生成管理员令牌失败")
	ErrAdminLoginDisabled      = New(http.StatusForbidden, 2313, "管理员账号已被禁用")

	// 管理员操作相关错误
	ErrAdminCreateFailed = New(http.StatusInternalServerError, 2320, "创建管理员失败")
	ErrAdminUpdateFailed = New(http.StatusInternalServerError, 2321, "更新管理员信息失败")
	ErrAdminDeleteFailed = New(http.StatusInternalServerError, 2322, "删除管理员失败")
	ErrAdminQueryFailed  = New(http.StatusInternalServerError, 2323, "查询管理员信息失败")

	// 管理员权限相关错误
	ErrAdminPermissionDenied = New(http.StatusForbidden, 2330, "管理员权限不足")
	ErrAdminNotSuperAdmin    = New(http.StatusForbidden, 2331, "需要超级管理员权限")
)
