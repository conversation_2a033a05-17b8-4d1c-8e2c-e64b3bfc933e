package middleware

import (
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"resume-server/internal/enum"
	"resume-server/internal/exception"
	"resume-server/internal/pkg"
	"resume-server/internal/response"
	"resume-server/internal/services"
)

// AdminAuthMiddleware 管理员JWT认证中间件
func AdminAuthMiddleware(adminJWTService pkg.AdminJWTService, adminService services.AdminService, redisClient *redis.Client) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			response.ThrowError(c, exception.ErrUnauthorized.WithMessage("请先登录管理后台"))
			return
		}

		// 检查Bearer前缀
		parts := strings.SplitN(authHeader, " ", 2)
		if !(len(parts) == 2 && parts[0] == "Bearer") {
			response.ThrowError(c, exception.ErrTokenInvalid.WithMessage("无效的认证头"))
			return
		}

		// 验证令牌
		token := parts[1]

		// 检查token是否在黑名单中
		isBlacklisted, err := adminService.IsTokenBlacklisted(token)
		if err != nil {
			pkg.Error("检查管理员token黑名单失败", zap.Error(err))
			response.ThrowError(c, exception.ErrInternalServer)
			return
		}
		if isBlacklisted {
			pkg.Warn("管理员token已被加入黑名单")
			response.ThrowError(c, exception.ErrUnauthorized.WithMessage("登录已失效，请重新登录"))
			return
		}

		claims, err := adminJWTService.ParseToken(token)
		if err != nil {
			pkg.Warn("管理员Token验证失败", zap.Error(err))
			response.ThrowError(c, exception.ErrTokenInvalid.WithMessage("无效的Token"))
			return
		}

		// 检查令牌是否过期
		if adminJWTService.IsExpired(claims) {
			pkg.Warn("管理员Token已过期")
			response.ThrowError(c, exception.ErrTokenExpired)
			return
		}

		// 获取管理员ID
		adminID := adminJWTService.GetAdminID(claims)

		// 查询数据库获取管理员信息
		admin, err := adminService.GetAdminByID(adminID)
		if err != nil {
			pkg.Error("获取管理员信息失败", zap.Uint("adminId", adminID), zap.Error(err))
			response.ThrowError(c, exception.ErrUnauthorized.WithMessage("管理员不存在或已被禁用"))
			return
		}

		// 检查管理员状态
		if admin.Status != enum.AdminStatusEnabled {
			pkg.Warn("管理员账号已被禁用", zap.Uint("adminId", adminID))
			response.ThrowError(c, exception.ErrUnauthorized.WithMessage("管理员账号已被禁用"))
			return
		}

		// 将管理员ID和管理员模型保存到上下文中
		c.Set("adminId", adminID)
		c.Set("admin", admin)
		c.Set("isAdminLoggedIn", true)
		c.Next()
	}
}
