package dto_api

// SendEmailCodeRequest 发送邮件验证码请求DTO
type SendEmailCodeRequest struct {
	Email string `json:"email" binding:"required,email" example:"<EMAIL>" label:"邮箱地址"`
}

// LoginEmailCodeRequest 邮箱验证码登录请求DTO
type LoginEmailCodeRequest struct {
	Email string `json:"email" binding:"required,email" example:"<EMAIL>" label:"邮箱地址"`
	Code  string `json:"code" binding:"required" example:"123456" label:"验证码"`
}
