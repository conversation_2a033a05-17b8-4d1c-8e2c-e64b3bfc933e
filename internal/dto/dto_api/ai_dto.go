package dto_api

import (
	"time"

	"resume-server/internal/enum"
)

// PromptRequest AI提示词请求
type PromptRequest struct {
	// 简历ID
	ResumeID uint `json:"resume_id" binding:"required" example:"1" label:"简历ID"`

	// 模块名称（枚举）
	Module enum.ResumeModule `json:"module" binding:"required" example:"basic_info" label:"模块名称"`

	// 提示词类型（枚举）
	PromptType enum.PromptType `json:"prompt_type" binding:"required" example:"generate" label:"提示词类型"`

	// 描述字段
	Desc string `json:"desc" binding:"required,max=2000" example:"我是一名软件工程师，有3年开发经验" label:"描述内容"`
}

// AICallRecordListRequest AI调用记录列表请求
type AICallRecordListRequest struct {
	// 简历ID
	ResumeID uint `form:"resume_id" binding:"required" example:"1" label:"简历ID"`

	// 页码
	Page int `form:"page" binding:"min=1" example:"1" label:"页码"`

	// 每页数量
	PageSize int `form:"page_size" binding:"min=1,max=100" example:"20" label:"每页数量"`
}

// AICallRecordResponse AI调用记录响应
type AICallRecordResponse struct {
	// 记录ID
	ID uint `json:"id" example:"1"`

	// 用户ID
	UserID uint `json:"user_id" example:"1"`

	// 简历ID
	ResumeID uint `json:"resume_id" example:"1"`

	// 提示词类型（字符串值）
	PromptType string `json:"prompt_type" example:"generate"`

	// 提示词类型（中文名称）
	PromptTypeName string `json:"prompt_type_name" example:"AI生成"`

	// 简历模块（字符串值）
	ResumeModule string `json:"resume_module" example:"basic_info"`

	// 简历模块（中文名称）
	ResumeModuleName string `json:"resume_module_name" example:"基本信息"`

	// 请求数据
	RequestData string `json:"request_data" example:"请生成一份专业的个人简介"`

	// 响应数据
	ResponseData string `json:"response_data" example:"我是一名经验丰富的软件工程师..."`

	// 调用时长（毫秒）
	CallDuration int64 `json:"call_duration" example:"1500"`

	// 创建时间
	CreatedAt time.Time `json:"created_at" example:"2024-01-01T12:00:00Z"`

	// 更新时间
	UpdatedAt time.Time `json:"updated_at" example:"2024-01-01T12:00:00Z"`
}

// GenerateResumeRequest AI生成简历请求
type GenerateResumeRequest struct {
	// 话术内容
	Prompt string `json:"prompt" binding:"required,max=5000" example:"我是一名软件工程师，有3年Java开发经验，熟悉Spring框架，参与过电商项目开发" label:"话术内容"`

	// 模板ID（可选）
	TemplateID *uint `json:"template_id" example:"1" label:"模板ID"`
}

// GenerateResumeResponse AI生成简历响应
type GenerateResumeResponse struct {
	// 新创建的简历ID
	ResumeID uint `json:"resume_id" example:"123"`
}

// OptimizeResumeRequest AI优化简历请求
type OptimizeResumeRequest struct {
	// 简历ID
	ResumeID uint `json:"resume_id" binding:"required" example:"1" label:"简历ID"`
}

// ScoreResumeRequest AI简历打分请求
type ScoreResumeRequest struct {
	// 简历ID
	ResumeID uint `json:"resume_id" binding:"required" example:"1" label:"简历ID"`
	// 目标岗位ID（来自target_position表）
	PositionID uint `json:"position_id" binding:"required" example:"1" label:"目标岗位ID"`
}

// OptimizeResumeResponse AI优化简历响应
type OptimizeResumeResponse struct {
	// 简历草稿ID
	DraftID uint `json:"draft_id" example:"456"`
}

// BatchValidatePrivilegeRequest 批量权限校验请求
type BatchValidatePrivilegeRequest struct {
	// 需要校验的权限类型列表
	PrivilegeTypes []enum.PrivilegeType `json:"privilege_types" binding:"required,min=1" label:"权限类型列表"`
}
