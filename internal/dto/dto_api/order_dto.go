package dto_api

// CreateOrderRequest 创建订单请求
type CreateOrderRequest struct {
	PlanID uint   `json:"plan_id" binding:"required" example:"1"`        // 会员套餐ID
	BdVid  string `json:"bd_vid" binding:"omitempty" example:"bd123456"` // 百度投放ID
}

// QueryOrderStatusRequest 查询订单状态请求
type QueryOrderStatusRequest struct {
	OrderNo string `json:"order_no" binding:"required" example:"AL00011234567890"` // 订单号
}

// GetUserOrdersRequest 获取用户订单列表请求
type GetUserOrdersRequest struct {
	Page          int    `form:"page" binding:"omitempty,min=1" example:"1"`               // 页码，默认为1
	PageSize      int    `form:"page_size" binding:"omitempty,min=1,max=100" example:"10"` // 每页条数，默认为10，最大100
	PaymentStatus string `form:"payment_status" binding:"omitempty" example:"1,3"`         // 支付状态筛选：1待支付 2支付处理中 3支付成功 4支付失败 5支付超时，支持多个状态组合查询(用逗号分隔)，不传则查询所有状态
}
