package dto_admin

// AdminLoginRequest 管理员登录请求DTO
type AdminLoginRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50" example:"admin" label:"用户名"`
	Password string `json:"password" binding:"required,min=6,max=50" example:"123456" label:"密码"`
}

// AdminChangePasswordRequest 管理员修改密码请求DTO
type AdminChangePasswordRequest struct {
	OldPassword string `json:"old_password" binding:"required,min=6,max=50" example:"123456" label:"旧密码"`
	NewPassword string `json:"new_password" binding:"required,min=6,max=50" example:"newpass123" label:"新密码"`
}
