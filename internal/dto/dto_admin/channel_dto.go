package dto_admin

import "time"

// GetChannelUsersRequest 获取渠道用户列表请求DTO
type GetChannelUsersRequest struct {
	Page      int       `form:"page" binding:"omitempty,min=1" example:"1" label:"页码"`
	PageSize  int       `form:"page_size" binding:"omitempty,min=1,max=100" example:"10" label:"每页条数"`
	Channel   string    `form:"channel" binding:"omitempty,max=50" example:"web" label:"渠道号"`
	StartTime *time.Time `form:"start_time" binding:"omitempty" example:"2024-01-01T00:00:00Z" label:"开始时间"`
	EndTime   *time.Time `form:"end_time" binding:"omitempty" example:"2024-12-31T23:59:59Z" label:"结束时间"`
}

// GetChannelPaymentsRequest 获取渠道付费列表请求DTO
type GetChannelPaymentsRequest struct {
	Page      int       `form:"page" binding:"omitempty,min=1" example:"1" label:"页码"`
	PageSize  int       `form:"page_size" binding:"omitempty,min=1,max=100" example:"10" label:"每页条数"`
	Channel   string    `form:"channel" binding:"omitempty,max=50" example:"web" label:"渠道号"`
	StartTime *time.Time `form:"start_time" binding:"omitempty" example:"2024-01-01T00:00:00Z" label:"开始时间"`
	EndTime   *time.Time `form:"end_time" binding:"omitempty" example:"2024-12-31T23:59:59Z" label:"结束时间"`
}
