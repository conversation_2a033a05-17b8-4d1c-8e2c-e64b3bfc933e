package models

import (
	"resume-server/internal/enum"
)

// Category 分类模型
type Category struct {
	ID             uint              `json:"id" gorm:"primaryKey"`
	CategoryName   string            `json:"category_name" gorm:"size:100;not null;default:'';index;comment:分类名称"`
	SlugCn         string            `json:"slug_cn" gorm:"size:255;not null;default:'';index;comment:中文URL别名"`
	SlugEn         string            `json:"slug_en" gorm:"size:255;not null;default:'';index;comment:英文URL别名"`
	SeoTitle       string            `json:"seo_title" gorm:"size:255;not null;default:'';comment:SEO标题"`
	SeoKeywords    StringArray       `json:"seo_keywords" gorm:"type:json;not null;default:(JSON_ARRAY());comment:SEO关键词(JSON格式的数组)"`
	SeoDescription string            `json:"seo_description" gorm:"size:500;not null;default:'';comment:SEO描述"`
	Sort           int               `json:"sort" gorm:"type:int;default:0;index;comment:排序字段，数值越小越靠前"`
	CategoryType   enum.CategoryType `json:"category_type" gorm:"type:tinyint(1);not null;index;comment:分类类型 1:热门模板 2:大学专业 3:设计风格"`
	IsShow         bool              `json:"is_show" gorm:"not null;default:false;index;comment:是否展示"`
	IsInternship   bool              `json:"is_internship" gorm:"not null;default:false;index;comment:是否是实习"`
	Base
}

// TableName 指定表名
func (Category) TableName() string {
	return "category"
}
