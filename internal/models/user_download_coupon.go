package models

import (
	"time"

	"resume-server/internal/enum"
)

// UserDownloadCoupon 用户下载券表
type UserDownloadCoupon struct {
	ID           uint              `json:"id" gorm:"primaryKey"`
	UserID       uint              `json:"user_id" gorm:"not null;default:0;index;comment:用户ID"`
	IsUsed       bool              `json:"is_used" gorm:"type:tinyint(1);not null;default:0;index;comment:是否使用 0:未使用 1:已使用"`
	UsedTime     *time.Time        `json:"used_time" gorm:"type:datetime;null;index;comment:使用时间"`
	CouponSource enum.CouponSource `json:"coupon_source" gorm:"type:tinyint(1);not null;default:1;index;comment:券来源 1:订单充值 2:分享活动"`
	OrderID      uint              `json:"order_id" gorm:"not null;default:0;index;comment:订单ID"`
	Base

	// 关联（非外键）
	User  User  `json:"user" gorm:"-"`
	Order Order `json:"order" gorm:"-"`
}

// TableName 指定表名
func (UserDownloadCoupon) TableName() string {
	return "user_download_coupon"
}

// Use 使用下载券
func (c *UserDownloadCoupon) Use() {
	if !c.IsUsed {
		c.IsUsed = true
		now := time.Now()
		c.UsedTime = &now
	}
}

// IsAvailable 检查下载券是否可用
func (c *UserDownloadCoupon) IsAvailable() bool {
	return !c.IsUsed
}

// GetSourceDescription 获取券来源描述
func (c *UserDownloadCoupon) GetSourceDescription() string {
	return c.CouponSource.String()
}
