package models

import (
	"resume-server/internal/enum"
)

// Admin 管理员模型
type Admin struct {
	ID           uint             `json:"id" gorm:"primaryKey"`
	Username     string           `json:"username" gorm:"size:50;not null;index;comment:管理员用户名"`
	Password     string           `json:"-" gorm:"size:255;not null;default:'';comment:密码"`
	Channels     StringArray      `json:"channels" gorm:"type:json;not null;default:(JSON_ARRAY());comment:可管理的渠道列表"`
	IsSuperAdmin bool             `json:"is_super_admin" gorm:"type:tinyint(1);default:0;index;comment:是否为超级管理员 0:否 1:是"`
	Status       enum.AdminStatus `json:"status" gorm:"type:tinyint(1);default:1;index;comment:状态 0:禁用 1:启用"`
	Base
}

// TableName 指定表名
func (Admin) TableName() string {
	return "admin"
}
