package models

import (
	"resume-server/internal/enum"
)

// User 用户模型
type User struct {
	ID          uint            `json:"id" gorm:"primaryKey"`
	FingerPrint string          `json:"-" gorm:"size:255;not null;default:'';index;comment:浏览器指纹"`
	Username    string          `json:"username" gorm:"size:20;not null;comment:用户名"`
	Password    string          `json:"-" gorm:"size:255;comment:密码"`
	OpenID      string          `json:"open_id" gorm:"size:50;index;comment:微信OpenID"`
	Email       string          `json:"email" gorm:"size:50;index;comment:邮箱"`
	Phone       string          `json:"phone" gorm:"size:20;index;comment:手机号"`
	Avatar      string          `json:"avatar" gorm:"size:255;comment:头像"`
	Status      enum.UserStatus `json:"status" gorm:"type:tinyint(1);default:1;index;comment:状态 0:禁用 1:启用"`
	UserType    enum.UserType   `json:"user_type" gorm:"column:user_type;type:tinyint(1);default:2;index;comment:用户类型 1:游客 2:普通用户 3:会员"`
	BdVid       string          `json:"bd_vid" gorm:"size:255;not null;default:'';comment:百度投放ID"`
	Channel     string          `json:"channel" gorm:"size:50;index;comment:渠道"`
	Base
}

// TableName 指定表名
func (User) TableName() string {
	return "user"
}
