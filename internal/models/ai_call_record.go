package models

import (
	"time"

	"resume-server/internal/enum"
)

// AICallRecord AI调用记录表
type AICallRecord struct {
	ID           uint              `json:"id" gorm:"primaryKey"`
	UserID       uint              `json:"user_id" gorm:"not null;default:0;index;comment:用户ID"`
	ResumeID     uint              `json:"resume_id" gorm:"not null;default:0;index;comment:简历ID"`
	PromptType   enum.PromptType   `json:"prompt_type" gorm:"type:varchar(50);not null;default:'';index;comment:提示词类型"`
	ResumeModule enum.ResumeModule `json:"resume_module" gorm:"type:varchar(50);not null;default:'';index;comment:简历模块"`
	RequestData  string            `json:"request_data" gorm:"type:text;not null;comment:发送给AI的请求内容"`
	ResponseData string            `json:"response_data" gorm:"type:text;not null;comment:AI返回的结果"`
	CallDuration int64             `json:"call_duration" gorm:"not null;default:0;comment:整体调用时间(毫秒)"`
	CreatedAt    time.Time         `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt    time.Time         `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
}

// TableName 指定表名
func (AICallRecord) TableName() string {
	return "ai_call_records"
}
