package models

// Template 模板表
type Template struct {
	ID              uint        `json:"id" gorm:"primaryKey"`
	TemplateName    string      `json:"template_name" gorm:"size:100;not null;default:'';index;comment:模板名称"`
	OldTemplateName string      `json:"old_template_name" gorm:"size:100;not null;default:'';comment:旧模板名称"`
	ComponentName   string      `json:"component_name" gorm:"size:100;not null;default:'';index;comment:模板前端组件名称"`
	StyleID         UintArray   `json:"style_id" gorm:"type:json;not null;default:(JSON_ARRAY());comment:风格ID(JSON格式的数组)"`
	Tags            StringArray `json:"tags" gorm:"type:json;not null;default:(JSON_ARRAY());comment:标签(JSON格式的数组)"`
	ResumeStyle     ResumeStyle `json:"resume_style" gorm:"type:json;not null;default:(JSON_OBJECT());comment:简历样式配置(JSON格式的对象)"`
	PreviewImageUrl string      `json:"preview_image_url" gorm:"size:500;not null;default:'';comment:预览图链接"`
	UsageCount      int         `json:"usage_count" gorm:"type:int;not null;default:0;index;comment:使用人数"`
	Sort            int         `json:"sort" gorm:"type:int;default:0;index;comment:排序字段，数值越小越靠前"`
	Base
}

// TableName 指定表名
func (Template) TableName() string {
	return "template"
}
