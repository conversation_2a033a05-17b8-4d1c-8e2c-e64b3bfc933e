package models

// ResumeDetail 简历详情表
type ResumeDetail struct {
	ID              uint            `json:"id" gorm:"primaryKey"`
	UserID          uint            `json:"user_id" gorm:"not null;index;comment:用户ID"`
	ResumeID        uint            `json:"resume_id" gorm:"not null;index;comment:简历ID"`
	BasicInfo       BasicInfo       `json:"basic_info" gorm:"type:json;not null;default:(JSON_OBJECT());comment:基本信息(JSON格式的对象)"`
	Education       Education       `json:"education" gorm:"type:json;not null;default:(JSON_OBJECT());comment:教育经历(JSON格式的对象)"`
	Work            Work            `json:"work" gorm:"type:json;not null;default:(JSON_OBJECT());comment:工作经历(JSON格式的对象)"`
	Project         Project         `json:"project" gorm:"type:json;not null;default:(JSON_OBJECT());comment:项目经历(JSON格式的对象)"`
	Research        Research        `json:"research" gorm:"type:json;not null;default:(JSON_OBJECT());comment:研究经历(JSON格式的对象)"`
	Team            Team            `json:"team" gorm:"type:json;not null;default:(JSON_OBJECT());comment:社团经历(JSON格式的对象)"`
	Portfolio       Portfolio       `json:"portfolio" gorm:"type:json;not null;default:(JSON_OBJECT());comment:作品集(JSON格式的对象)"`
	Other           Other           `json:"other" gorm:"type:json;not null;default:(JSON_OBJECT());comment:其他模块(JSON格式的对象)"`
	PersonalSummary PersonalSummary `json:"personal_summary" gorm:"type:json;not null;default:(JSON_OBJECT());comment:个人总结(JSON格式的对象)"`
	Honors          Honors          `json:"honors" gorm:"type:json;not null;default:(JSON_OBJECT());comment:荣誉栏(JSON格式的对象)"`
	Skills          Skills          `json:"skills" gorm:"type:json;not null;default:(JSON_OBJECT());comment:技能条(JSON格式的对象)"`
	CustomModules   CustomModules   `json:"custom_modules" gorm:"type:json;not null;default:(JSON_ARRAY());comment:自定义模块(JSON格式的数组)"`
	Slogan          Slogan          `json:"slogan" gorm:"type:json;not null;default:(JSON_OBJECT());comment:简历标语(JSON格式的对象)"`
	ResumeStyle     ResumeStyle     `json:"resume_style" gorm:"type:json;not null;default:(JSON_OBJECT());comment:简历样式配置(JSON格式的对象)"`
	Base
}

// TableName 指定表名
func (ResumeDetail) TableName() string {
	return "resume_detail"
}
