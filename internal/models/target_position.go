package models

// TargetPosition 目标岗位表
type TargetPosition struct {
	ID             uint   `json:"id" gorm:"primaryKey"`
	UserID         uint   `json:"user_id" gorm:"not null;index;comment:用户ID"`
	PositionName   string `json:"position_name" gorm:"size:100;not null;default:'';comment:岗位名称"`
	CompanyName    string `json:"company_name" gorm:"size:100;not null;default:'';comment:公司名称"`
	JobSource      string `json:"job_source" gorm:"size:50;not null;default:'';comment:岗位来源"`
	JobDescription string `json:"job_description" gorm:"type:text;comment:岗位职责描述/要求"`
	Base

	// 关联（非外键）
	User User `json:"user" gorm:"-"`
}

// TableName 指定表名
func (TargetPosition) TableName() string {
	return "target_position"
}
