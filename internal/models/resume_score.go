package models

import "encoding/json"

// ResumeScore 简历评分记录表
type ResumeScore struct {
	ID                             uint    `json:"id" gorm:"primaryKey"`
	UserID                         uint    `json:"user_id" gorm:"not null;index;comment:用户ID"`
	ResumeID                       uint    `json:"resume_id" gorm:"not null;index;comment:简历ID"`
	TargetPositionID               uint    `json:"target_position_id" gorm:"not null;index;comment:目标岗位ID"`
	LanguageExpressionScore        float64 `json:"language_expression_score" gorm:"type:decimal(5,2);not null;default:0;comment:语言与表达评分"`
	LanguageExpressionDetails      string  `json:"language_expression_details" gorm:"type:text;comment:语言与表达详细评价JSON"`
	InformationCompletenessScore   float64 `json:"information_completeness_score" gorm:"type:decimal(5,2);not null;default:0;comment:信息完整性评分"`
	InformationCompletenessDetails string  `json:"information_completeness_details" gorm:"type:text;comment:信息完整性详细评价JSON"`
	ContentRelevanceScore          float64 `json:"content_relevance_score" gorm:"type:decimal(5,2);not null;default:0;comment:内容相关性评分"`
	ContentRelevanceDetails        string  `json:"content_relevance_details" gorm:"type:text;comment:内容相关性详细评价JSON"`
	ProfessionalismScore           float64 `json:"professionalism_score" gorm:"type:decimal(5,2);not null;default:0;comment:简历专业性评分"`
	ProfessionalismDetails         string  `json:"professionalism_details" gorm:"type:text;comment:简历专业性详细评价JSON"`
	OverallScore                   float64 `json:"overall_score" gorm:"type:decimal(5,2);not null;default:0;comment:总体评分"`
	OverallComment                 string  `json:"overall_comment" gorm:"type:text;comment:总体评价"`
	Base

	// 关联（非外键）
	User           User           `json:"user" gorm:"-"`
	Resume         Resume         `json:"resume" gorm:"-"`
	TargetPosition TargetPosition `json:"target_position" gorm:"-"`
}

// TableName 指定表名
func (ResumeScore) TableName() string {
	return "resume_score"
}

// ScoreDetailItem 详细评分项
type ScoreDetailItem struct {
	Title   string `json:"title"`
	Comment string `json:"comment"`
}

// SetLanguageExpressionDetails 设置语言与表达详细评价
func (rs *ResumeScore) SetLanguageExpressionDetails(details []ScoreDetailItem) error {
	data, err := json.Marshal(details)
	if err != nil {
		return err
	}
	rs.LanguageExpressionDetails = string(data)
	return nil
}

// GetLanguageExpressionDetails 获取语言与表达详细评价
func (rs *ResumeScore) GetLanguageExpressionDetails() ([]ScoreDetailItem, error) {
	if rs.LanguageExpressionDetails == "" {
		return nil, nil
	}

	var details []ScoreDetailItem
	err := json.Unmarshal([]byte(rs.LanguageExpressionDetails), &details)
	if err != nil {
		return nil, err
	}
	return details, nil
}

// SetInformationCompletenessDetails 设置信息完整性详细评价
func (rs *ResumeScore) SetInformationCompletenessDetails(details []ScoreDetailItem) error {
	data, err := json.Marshal(details)
	if err != nil {
		return err
	}
	rs.InformationCompletenessDetails = string(data)
	return nil
}

// GetInformationCompletenessDetails 获取信息完整性详细评价
func (rs *ResumeScore) GetInformationCompletenessDetails() ([]ScoreDetailItem, error) {
	if rs.InformationCompletenessDetails == "" {
		return nil, nil
	}

	var details []ScoreDetailItem
	err := json.Unmarshal([]byte(rs.InformationCompletenessDetails), &details)
	if err != nil {
		return nil, err
	}
	return details, nil
}

// SetContentRelevanceDetails 设置内容相关性详细评价
func (rs *ResumeScore) SetContentRelevanceDetails(details []ScoreDetailItem) error {
	data, err := json.Marshal(details)
	if err != nil {
		return err
	}
	rs.ContentRelevanceDetails = string(data)
	return nil
}

// GetContentRelevanceDetails 获取内容相关性详细评价
func (rs *ResumeScore) GetContentRelevanceDetails() ([]ScoreDetailItem, error) {
	if rs.ContentRelevanceDetails == "" {
		return nil, nil
	}

	var details []ScoreDetailItem
	err := json.Unmarshal([]byte(rs.ContentRelevanceDetails), &details)
	if err != nil {
		return nil, err
	}
	return details, nil
}

// SetProfessionalismDetails 设置简历专业性详细评价
func (rs *ResumeScore) SetProfessionalismDetails(details []ScoreDetailItem) error {
	data, err := json.Marshal(details)
	if err != nil {
		return err
	}
	rs.ProfessionalismDetails = string(data)
	return nil
}

// GetProfessionalismDetails 获取简历专业性详细评价
func (rs *ResumeScore) GetProfessionalismDetails() ([]ScoreDetailItem, error) {
	if rs.ProfessionalismDetails == "" {
		return nil, nil
	}

	var details []ScoreDetailItem
	err := json.Unmarshal([]byte(rs.ProfessionalismDetails), &details)
	if err != nil {
		return nil, err
	}
	return details, nil
}
