package models

// Position 职位模型
type Position struct {
	ID             uint        `json:"id" gorm:"primaryKey"`
	PositionName   string      `json:"position_name" gorm:"size:100;not null;default:'';index;comment:职位名称"`
	SlugCn         string      `json:"slug_cn" gorm:"size:255;not null;default:'';index;comment:中文URL别名"`
	SlugEn         string      `json:"slug_en" gorm:"size:255;not null;default:'';index;comment:英文URL别名"`
	SeoTitle       string      `json:"seo_title" gorm:"size:255;not null;default:'';comment:SEO标题"`
	SeoKeywords    StringArray `json:"seo_keywords" gorm:"type:json;not null;default:(JSON_ARRAY());comment:SEO关键词(JSON格式的数组)"`
	SeoDescription string      `json:"seo_description" gorm:"size:500;not null;default:'';comment:SEO描述"`
	Keywords       StringArray `json:"keywords" gorm:"type:json;not null;default:(JSON_ARRAY());comment:关键字(JSON格式的数组)"`
	ParentID       uint        `json:"parent_id" gorm:"type:int unsigned;not null;default:0;index;comment:父级ID，0表示顶级职位"`
	Path           string      `json:"path" gorm:"size:500;not null;default:'';index;comment:层级路径，以逗号分隔的ID字符串"`
	Level          int         `json:"level" gorm:"type:tinyint;not null;default:1;index;comment:层级深度，1表示一级分类，2表示二级分类，3表示三级分类"`
	Sort           int         `json:"sort" gorm:"type:int;default:0;index;comment:排序字段，数值越小越靠前"`
	Base
}

// TableName 指定表名
func (Position) TableName() string {
	return "position"
}
