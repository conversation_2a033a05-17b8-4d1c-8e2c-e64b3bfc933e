package models

// Resume 简历表
type Resume struct {
	ID              uint   `json:"id" gorm:"primaryKey"`
	UserID          uint   `json:"user_id" gorm:"not null;index;comment:用户ID"`
	TemplateID      uint   `json:"template_id" gorm:"not null;index;comment:模板ID"`
	ResumeName      string `json:"resume_name" gorm:"size:100;not null;default:'';index;comment:简历名称"`
	PreviewImageUrl string `json:"preview_image_url" gorm:"size:500;not null;default:'';comment:预览图链接"`
	CompletionRate  string `json:"completion_rate" gorm:"size:10;not null;default:'0%';comment:完整度百分比"`
	Base

	// 关联（非外键）
	User         User         `json:"user" gorm:"-"`
	Template     Template     `json:"template" gorm:"-"`
	ResumeDetail ResumeDetail `json:"resume_detail" gorm:"-"`
}

// TableName 指定表名
func (Resume) TableName() string {
	return "resume"
}
