package utils

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"go.uber.org/zap"
)

// BaiduConversionRequest 百度转化数据上传请求结构
type BaiduConversionRequest struct {
	Token           string                `json:"token"`
	ConversionTypes []BaiduConversionType `json:"conversionTypes"`
}

// BaiduConversionType 百度转化类型
type BaiduConversionType struct {
	LogidURL string `json:"logidUrl"`
	NewType  int    `json:"newType"`
}

// UploadBaiduConversionData 上传百度转化数据
// ctx: 上下文
// bdVid: 百度投放ID
// frontendDomain: 前端域名
// newType: 转化类型
// logger: 日志记录器
func UploadBaiduConversionData(ctx context.Context, bdVid, frontendDomain string, newType int, logger *zap.Logger) error {
	// 如果bd_vid为空，跳过上传
	if bdVid == "" {
		logger.Info("bd_vid为空，跳过百度转化数据上传")
		return nil
	}

	// 构建logidUrl
	logidURL := fmt.Sprintf("%s?bd_vid=%s", frontendDomain, bdVid)

	// 构建请求数据
	request := BaiduConversionRequest{
		Token: "hOuG0CqiIqnIraMdD5H1fJqkBR44g7Xr@tafc5CVBECFzfmRAYLFMSTyVfzsp4mDX",
		ConversionTypes: []BaiduConversionType{
			{
				LogidURL: logidURL,
				NewType:  newType,
			},
		},
	}

	// 序列化请求数据
	jsonData, err := json.Marshal(request)
	if err != nil {
		logger.Error("序列化百度转化数据请求失败", zap.Error(err), zap.String("bd_vid", bdVid))
		return fmt.Errorf("序列化百度转化数据请求失败: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", "https://ocpc.baidu.com/ocpcapi/api/uploadConvertData", bytes.NewBuffer(jsonData))
	if err != nil {
		logger.Error("创建百度转化数据上传请求失败", zap.Error(err), zap.String("bd_vid", bdVid))
		return fmt.Errorf("创建百度转化数据上传请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 创建HTTP客户端并发送请求
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	logger.Info("开始上传百度转化数据",
		zap.String("bd_vid", bdVid),
		zap.String("logid_url", logidURL),
		zap.String("request_body", string(jsonData)))

	resp, err := client.Do(req)
	if err != nil {
		logger.Error("上传百度转化数据失败", zap.Error(err), zap.String("bd_vid", bdVid))
		return fmt.Errorf("上传百度转化数据失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody := make([]byte, 1024)
	n, _ := resp.Body.Read(responseBody)
	responseStr := string(responseBody[:n])

	if resp.StatusCode == http.StatusOK {
		logger.Info("百度转化数据上传成功",
			zap.String("bd_vid", bdVid),
			zap.Int("status_code", resp.StatusCode),
			zap.String("response", responseStr))
	} else {
		logger.Error("百度转化数据上传失败",
			zap.String("bd_vid", bdVid),
			zap.Int("status_code", resp.StatusCode),
			zap.String("response", responseStr))
		return fmt.Errorf("百度转化数据上传失败，状态码: %d, 响应: %s", resp.StatusCode, responseStr)
	}

	return nil
}
