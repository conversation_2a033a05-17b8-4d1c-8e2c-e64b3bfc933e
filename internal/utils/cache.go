package utils

import (
	"context"
	"time"

	"github.com/goccy/go-json"
	"github.com/redis/go-redis/v9"
)

// 缓存键常量定义
const (
	// CacheKeyHomePageExamples 首页示例列表缓存键
	CacheKeyHomePageExamples = "homepage_examples"

	// CacheKeyAllCategoriesGrouped 所有分类分组数据缓存键
	CacheKeyAllCategoriesGrouped = "all_categories_grouped"

	// CacheKeyCategoryBySlug 根据slug获取分类详情缓存键前缀
	// 实际使用时会拼接slug，如: "category_by_slug:tech"
	CacheKeyCategoryBySlug = "category_by_slug"

	// CacheKeyAllPositionsHierarchy 所有职位层级结构缓存键
	CacheKeyAllPositionsHierarchy = "all_positions_hierarchy"

	// CacheKeyPositionsByParentID 根据父级ID获取职位层级结构缓存键前缀
	// 实际使用时会拼接parentID，如: "positions_by_parent_id:123"
	CacheKeyPositionsByParentID = "positions_by_parent_id"

	// CacheKeyExampleById 根据ID获取示例详情缓存键前缀
	// 实际使用时会拼接示例ID，如: "example_by_id:123"
	CacheKeyExampleById = "example_by_id"

	// CacheKeyTemplateRecommendations 模板推荐列表缓存键
	CacheKeyTemplateRecommendations = "template_recommendations"

	// CacheKeyVisibleMembershipPlans 可见会员套餐列表缓存键
	CacheKeyVisibleMembershipPlans = "visible_membership_plans"

	// CacheKeyVisibleDownloadCouponPlans 可见下载券套餐列表缓存键
	CacheKeyVisibleDownloadCouponPlans = "visible_download_coupon_plans"
)

// CacheFunc 通用缓存函数，支持泛型
// T: 返回值类型
// U: 参数类型
// cacheKey: 缓存键
// callback: 回调函数
// ttl: 缓存过期时间（秒）
// client: Redis客户端
// params: 回调函数参数
func CacheFunc[T any, U any](cacheKey string, callback func(params ...U) T, ttl int64, client *redis.Client, params ...U) T {
	ctx := context.Background()
	var result T
	s, _ := client.Get(ctx, cacheKey).Result()

	if s != "" {
		json.Unmarshal([]byte(s), &result)
	} else {
		// 调用闭包函数
		result = callback(params...)

		str, _ := json.Marshal(result)
		client.Set(ctx, cacheKey, string(str), time.Duration(ttl)*time.Second)
	}

	return result
}

// CacheFunc2 通用缓存函数，支持泛型和错误处理
// T: 返回值类型
// U: 参数类型
// cacheKey: 缓存键
// callback: 回调函数（返回值和错误）
// ttl: 缓存过期时间（秒）
// client: Redis客户端
// params: 回调函数参数
func CacheFunc2[T any, U any](cacheKey string, callback func(params ...U) (T, error), ttl int64, client *redis.Client, params ...U) (T, error) {
	ctx := context.Background()
	var result T
	var err error
	s, _ := client.Get(ctx, cacheKey).Result()

	if s != "" {
		json.Unmarshal([]byte(s), &result)
	} else {
		// 调用闭包函数
		result, err = callback(params...)
		if err != nil {
			return result, err
		}

		str, _ := json.Marshal(result)
		client.Set(ctx, cacheKey, string(str), time.Duration(ttl)*time.Second)
	}
	return result, err
}
