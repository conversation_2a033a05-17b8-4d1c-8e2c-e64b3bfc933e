package utils

import (
	"fmt"
	"time"

	"resume-server/internal/enum"
)

// GenerateOrderNo 生成订单号
// userID: 用户ID
// paymentMethod: 支付方式，用于区分不同支付渠道的订单
// 返回值: 生成的订单号
func GenerateOrderNo(userID uint, paymentMethod enum.PaymentMethod) string {
	// 获取当前时间戳和随机数
	timestamp := time.Now().Unix()
	randomNum := time.Now().UnixNano() % 10000

	// 根据支付方式生成不同前缀
	var prefix string
	switch paymentMethod {
	case enum.PaymentMethodWechat:
		prefix = "WX" // 微信支付前缀
	case enum.PaymentMethodAlipay:
		prefix = "AL" // 支付宝前缀
	default:
		prefix = "OR" // 默认订单前缀
	}

	// 格式：{前缀} + 用户ID(4位) + 时间戳(10位) + 随机数(4位)
	return fmt.Sprintf("%s%04d%010d%04d", prefix, userID%10000, timestamp, randomNum)
}
