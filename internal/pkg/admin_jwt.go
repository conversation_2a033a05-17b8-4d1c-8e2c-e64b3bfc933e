package pkg

import (
	"errors"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"go.uber.org/zap"
	"resume-server/config"
)

// AdminClaims 管理员JWT声明
type AdminClaims struct {
	AdminID uint `json:"admin_id"`
	jwt.RegisteredClaims
}

// AdminJWTService 管理员JWT服务接口
type AdminJWTService interface {
	GenerateToken(adminID uint) (string, error)
	ParseToken(tokenString string) (*AdminClaims, error)
	GetAdminID(claims *AdminClaims) uint
	IsExpired(claims *AdminClaims) bool
	GetExpirationTime(claims *AdminClaims) time.Time
}

// adminJWTService 管理员JWT服务实现
type adminJWTService struct {
	config *config.Config
}

// NewAdminJWTService 创建管理员JWT服务
func NewAdminJWTService(config *config.Config) AdminJWTService {
	return &adminJWTService{
		config: config,
	}
}

// GenerateToken 生成管理员JWT令牌
func (s *adminJWTService) GenerateToken(adminID uint) (string, error) {
	claims := AdminClaims{
		AdminID: adminID,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Duration(s.config.AdminJWT.TTL) * time.Second)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
		},
	}

	// 创建Token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// 签名生成Token字符串
	tokenString, err := token.SignedString([]byte(s.config.AdminJWT.Secret))
	if err != nil {
		Error("管理员JWT Token生成失败", zap.Error(err))
		return "", err
	}

	return tokenString, nil
}

// ParseToken 解析管理员JWT令牌
func (s *adminJWTService) ParseToken(tokenString string) (*AdminClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &AdminClaims{}, func(token *jwt.Token) (interface{}, error) {
		// 验证签名算法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("无效的签名算法")
		}
		return []byte(s.config.AdminJWT.Secret), nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*AdminClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("无效的Token")
}

// GetAdminID 从Token中获取管理员ID
func (s *adminJWTService) GetAdminID(claims *AdminClaims) uint {
	return claims.AdminID
}

// IsExpired 检查Token是否已过期
func (s *adminJWTService) IsExpired(claims *AdminClaims) bool {
	now := time.Now()
	if claims.ExpiresAt != nil {
		return now.After(claims.ExpiresAt.Time)
	}
	return false
}

// GetExpirationTime 获取Token的过期时间
func (s *adminJWTService) GetExpirationTime(claims *AdminClaims) time.Time {
	if claims.ExpiresAt != nil {
		return claims.ExpiresAt.Time
	}
	return time.Time{}
}
