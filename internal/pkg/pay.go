package pkg

import (
	"context"
	"crypto/x509"
	"encoding/hex"
	"encoding/pem"
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/smartwalle/alipay/v3"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/core/auth/verifiers"
	"github.com/wechatpay-apiv3/wechatpay-go/core/downloader"
	"github.com/wechatpay-apiv3/wechatpay-go/core/notify"
	"github.com/wechatpay-apiv3/wechatpay-go/core/option"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments/native"
	"github.com/wechatpay-apiv3/wechatpay-go/utils"
	"resume-server/config"
)

// PayService 支付服务
type PayService struct {
	Client          *alipay.Client // 支付宝客户端
	WechatPayClient *core.Client   // 微信支付客户端
	config          *config.Config // 配置信息
	mchPrivateKey   interface{}    // 商户私钥
	mchSerialNo     string         // 商户证书序列号
}

// NewPayService 创建支付服务实例（用于依赖注入）
func NewPayService(cfg *config.Config) (*PayService, error) {
	// 读取支付宝应用私钥文件内容
	appSecretCert, err := os.ReadFile(cfg.AliPay.AppSecretCertPath)
	if err != nil {
		return nil, fmt.Errorf("读取支付宝应用私钥文件失败: %w", err)
	}

	// 使用配置文件中的支付宝配置
	client, err := alipay.New(cfg.AliPay.AppID, string(appSecretCert), cfg.AliPay.IsProduction)
	if err != nil {
		return nil, fmt.Errorf("初始化支付宝客户端失败: %w", err)
	}

	// 加载支付宝证书
	err = client.LoadAppCertPublicKeyFromFile(cfg.AliPay.AppPublicCertPath)
	if err != nil {
		return nil, fmt.Errorf("加载应用公钥证书失败: %w", err)
	}

	err = client.LoadAlipayCertPublicKeyFromFile(cfg.AliPay.PublicCertPath)
	if err != nil {
		return nil, fmt.Errorf("加载支付宝公钥证书失败: %w", err)
	}

	err = client.LoadAliPayRootCertFromFile(cfg.AliPay.RootCertPath)
	if err != nil {
		return nil, fmt.Errorf("加载支付宝根证书失败: %w", err)
	}

	// 初始化微信支付客户端
	ctx := context.Background()

	// 使用 utils 提供的函数从本地文件中加载商户私钥，商户私钥会用来生成请求的签名
	mchPrivateKey, err := utils.LoadPrivateKeyWithPath(cfg.WeChat.PayPrivateKey)
	if err != nil {
		return nil, fmt.Errorf("加载商户私钥失败: %w", err)
	}

	// 获取商户证书序列号
	serialNo, err := GetCertificateSerialNumber(cfg.WeChat.PayCertificate)
	if err != nil {
		return nil, fmt.Errorf("获取商户证书序列号失败: %w", err)
	}

	// 使用商户私钥等初始化 client，并使它具有自动定时获取微信支付平台证书的能力
	opts := []core.ClientOption{
		option.WithWechatPayAutoAuthCipher(
			cfg.WeChat.PayMchID,
			serialNo, // 使用动态获取的证书序列号
			mchPrivateKey,
			cfg.WeChat.PaySecretKey,
		),
	}
	wechatPayClient, err := core.NewClient(ctx, opts...)
	if err != nil {
		return nil, fmt.Errorf("创建微信支付客户端失败: %w", err)
	}

	// WithWechatPayAutoAuthCipher 已经自动注册了下载器，无需手动注册
	// 等待一段时间让证书下载完成
	certificateVisitor := downloader.MgrInstance().GetCertificateVisitor(cfg.WeChat.PayMchID)
	if certificateVisitor == nil {
		// 等待证书下载完成
		for i := 0; i < 10; i++ {
			time.Sleep(1 * time.Second)
			certificateVisitor = downloader.MgrInstance().GetCertificateVisitor(cfg.WeChat.PayMchID)
			if certificateVisitor != nil {
				break
			}
		}
	}

	return &PayService{
		Client:          client,
		WechatPayClient: wechatPayClient,
		config:          cfg,
		mchPrivateKey:   mchPrivateKey,
		mchSerialNo:     serialNo,
	}, nil
}

// AlipayTradePreCreate 支付宝扫码支付
// 文档地址: https://opendocs.alipay.com/open/02ekfg
func (s *PayService) AlipayTradePreCreate(cfg *config.Config, outTradeNo, subject, totalAmount string) (string, error) {
	// 创建预下单请求
	var p = alipay.TradePreCreate{}
	p.NotifyURL = cfg.AliPay.NotifyURL
	p.OutTradeNo = outTradeNo   // 商户订单号，需要保证不重复
	p.Subject = subject         // 订单标题
	p.TotalAmount = totalAmount // 订单金额
	p.TimeoutExpress = "20m"    // 订单有效期，超时将关闭交易，取值范围：1m～15d

	// 调用支付宝接口
	ctx := context.Background()
	rsp, err := s.Client.TradePreCreate(ctx, p)
	if err != nil {
		return "", fmt.Errorf("调用支付宝预下单接口失败: %w", err)
	}

	// 检查响应是否成功
	if rsp.Code != alipay.CodeSuccess {
		return "", fmt.Errorf("支付宝预下单失败，错误码: %s, 错误信息: %s", rsp.Code, rsp.Msg)
	}

	// 返回二维码链接
	return rsp.QRCode, nil
}

// GetCertificateSerialNumber 获取证书序列号
// 等同于 openssl x509 -in certPath -noout -serial 命令的输出
func GetCertificateSerialNumber(certPath string) (string, error) {
	// 读取证书文件
	certData, err := os.ReadFile(certPath)
	if err != nil {
		return "", fmt.Errorf("读取证书文件失败: %w", err)
	}

	// 解析PEM格式的证书
	block, _ := pem.Decode(certData)
	if block == nil {
		return "", fmt.Errorf("解析证书失败: 无效的PEM格式")
	}

	// 解析X509证书
	cert, err := x509.ParseCertificate(block.Bytes)
	if err != nil {
		return "", fmt.Errorf("解析X509证书失败: %w", err)
	}

	// 获取证书序列号并转换为十六进制字符串
	serialNumberHex := hex.EncodeToString(cert.SerialNumber.Bytes())

	// 转换为大写
	serialNumberHex = strings.ToUpper(serialNumberHex)

	// 返回格式化后的序列号
	return serialNumberHex, nil
}

// WechatPayNativePreCreate 微信支付Native扫码支付
// 文档地址: https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_4_1.shtml
func (s *PayService) WechatPayNativePreCreate(cfg *config.Config, outTradeNo, subject, totalAmount string) (string, error) {
	ctx := context.Background()

	// 创建Native支付服务
	svc := native.NativeApiService{Client: s.WechatPayClient}

	// 计算金额（单位：分）
	amountInt, err := strconv.ParseFloat(totalAmount, 64)
	if err != nil {
		return "", fmt.Errorf("金额格式错误: %w", err)
	}
	amountCent := int64(amountInt * 100)

	// 发送预下单请求
	resp, result, err := svc.Prepay(ctx,
		native.PrepayRequest{
			Appid:       core.String(cfg.WeChat.PayAppID),
			Mchid:       core.String(cfg.WeChat.PayMchID),
			Description: core.String(subject),
			OutTradeNo:  core.String(outTradeNo),
			Attach:      core.String("熊猫简历-会员购买"),
			NotifyUrl:   core.String(cfg.WeChat.PayNotifyURL),
			Amount: &native.Amount{
				Total: core.Int64(amountCent),
			},
		},
	)

	if err != nil {
		return "", fmt.Errorf("调用微信支付预下单接口失败: %w", err)
	}

	if result.Response.StatusCode != 200 {
		return "", fmt.Errorf("微信支付预下单失败，状态码: %d", result.Response.StatusCode)
	}

	// 返回二维码链接
	return *resp.CodeUrl, nil
}

// CreateNotifyHandler 创建微信支付回调通知处理器
func (s *PayService) CreateNotifyHandler() *notify.Handler {
	// 获取商户号对应的微信支付平台证书访问器
	certificateVisitor := downloader.MgrInstance().GetCertificateVisitor(s.config.WeChat.PayMchID)
	if certificateVisitor == nil {
		// 等待证书下载完成
		for i := 0; i < 5; i++ {
			time.Sleep(1 * time.Second)
			certificateVisitor = downloader.MgrInstance().GetCertificateVisitor(s.config.WeChat.PayMchID)
			if certificateVisitor != nil {
				break
			}
		}

		if certificateVisitor == nil {
			// 返回一个基础的处理器，但可能无法验签
			return notify.NewNotifyHandler(s.config.WeChat.PaySecretKey, nil)
		}
	}

	// 使用证书访问器初始化 notify.Handler
	return notify.NewNotifyHandler(s.config.WeChat.PaySecretKey, verifiers.NewSHA256WithRSAVerifier(certificateVisitor))
}
