package pkg

import (
	"bytes"
	"fmt"
	"html/template"
	"io"
	"time"

	"go.uber.org/zap"
	"gopkg.in/gomail.v2"
	"resume-server/config"
)

// EmailService 邮件服务接口
type EmailService interface {
	// SendVerificationCode 发送验证码邮件
	SendVerificationCode(email, code string) error
	// SendResumeAttachment 发送简历附件邮件
	SendResumeAttachment(email string, resumeData []byte, resumeName string) error
}

// emailService 邮件服务实现
type emailService struct {
	config   *config.Config
	smtpHost string
	smtpPort int
	username string
	password string
	fromName string
	logger   *zap.Logger
}

// 验证码邮件模板
const emailTemplate = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        .container {
            max-width: 600px;
            margin: 20px auto;
            padding: 30px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .code {
            font-size: 24px;
            color: #1890ff;
            padding: 10px 20px;
            background: #f0f9ff;
            margin: 20px 0;
            display: inline-block;
        }
        .warning {
            color: #ff4d4f;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>欢迎登录 {{.AppName}}</h2>
        <p>您的验证码是：</p>
        <div class="code">{{.Code}}</div>
        <p>有效期 {{.ExpireMinutes}} 分钟，请勿泄露给他人</p>
        <p class="warning">* 如非本人操作，请忽略本邮件</p>
    </div>
</body>
</html>`

// 简历邮件模板
const resumeEmailTemplate = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        .container {
            max-width: 600px;
            margin: 20px auto;
            padding: 30px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-family: Arial, sans-serif;
        }
        .header {
            text-align: center;
            color: #1890ff;
            margin-bottom: 30px;
        }
        .content {
            line-height: 1.6;
            color: #333;
        }
        .attachment-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #1890ff;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>{{.AppName}} - 简历文件</h2>
        </div>
        <div class="content">
            <p>您好！</p>
            <p>感谢您使用 {{.AppName}} 制作简历。您的简历PDF文件已生成完成，请查收附件。</p>

            <div class="attachment-info">
                <strong>📎 附件信息：</strong><br>
                文件名：{{.ResumeName}}<br>
                文件格式：PDF<br>
                生成时间：{{.GenerateTime}}
            </div>

            <p>如有任何问题，请随时联系我们。</p>
            <p>祝您求职顺利！</p>
        </div>
        <div class="footer">
            <p>此邮件由系统自动发送，请勿回复。</p>
            <p>© {{.AppName}} 团队</p>
        </div>
    </div>
</body>
</html>`

// 模板数据
type TemplateData struct {
	AppName       string
	Code          string
	ExpireMinutes int
}

// 简历邮件模板数据
type ResumeTemplateData struct {
	AppName      string
	ResumeName   string
	GenerateTime string
}

// NewEmailService 创建邮件服务
func NewEmailService(cfg *config.Config, logger *zap.Logger) EmailService {
	// 从配置中读取SMTP配置
	return &emailService{
		config:   cfg,
		smtpHost: cfg.Email.SMTPHost,
		smtpPort: cfg.Email.SMTPPort,
		username: cfg.Email.Username,
		password: cfg.Email.Password,
		fromName: cfg.Email.FromName,
		logger:   logger,
	}
}

// SendVerificationCode 发送验证码邮件
func (s *emailService) SendVerificationCode(email, code string) error {
	// 解析模板
	tmpl, err := template.New("email").Parse(emailTemplate)
	if err != nil {
		s.logger.Error("邮件模板解析失败", zap.Error(err))
		return err
	}

	// 构建HTML内容
	var htmlBody bytes.Buffer
	data := TemplateData{
		AppName:       s.config.App.Name,
		Code:          code,
		ExpireMinutes: 5, // 验证码有效期5分钟
	}
	if err := tmpl.Execute(&htmlBody, data); err != nil {
		s.logger.Error("邮件模板渲染失败", zap.Error(err))
		return err
	}

	// 创建邮件对象
	m := gomail.NewMessage()
	m.SetHeader("From", m.FormatAddress(s.username, s.fromName))
	m.SetHeader("To", email)
	m.SetHeader("Subject", fmt.Sprintf("%s - 登录验证码", s.config.App.Name))

	// 设置HTML正文
	m.SetBody("text/html", htmlBody.String())

	// 添加纯文本备用内容
	textContent := fmt.Sprintf("您的验证码是：%s，有效期%d分钟", code, 5)
	m.AddAlternative("text/plain", textContent)

	// 发送邮件
	d := gomail.NewDialer(s.smtpHost, s.smtpPort, s.username, s.password)
	d.SSL = true

	if err := d.DialAndSend(m); err != nil {
		s.logger.Error("发送邮件失败", zap.String("email", email), zap.Error(err))
		return err
	}

	s.logger.Info("验证码邮件发送成功", zap.String("email", email))
	return nil
}

// SendResumeAttachment 发送简历PDF附件邮件
func (s *emailService) SendResumeAttachment(email string, resumeData []byte, resumeName string) error {
	// 解析简历邮件模板
	tmpl, err := template.New("resumeEmail").Parse(resumeEmailTemplate)
	if err != nil {
		s.logger.Error("简历邮件模板解析失败", zap.Error(err))
		return err
	}

	// 构建HTML内容
	var htmlBody bytes.Buffer
	data := ResumeTemplateData{
		AppName:      s.config.App.Name,
		ResumeName:   resumeName,
		GenerateTime: time.Now().Format("2006-01-02 15:04:05"), // 使用当前时间
	}
	if err := tmpl.Execute(&htmlBody, data); err != nil {
		s.logger.Error("简历邮件模板渲染失败", zap.Error(err))
		return err
	}

	// 创建邮件对象
	m := gomail.NewMessage()
	m.SetHeader("From", m.FormatAddress(s.username, s.fromName))
	m.SetHeader("To", email)
	m.SetHeader("Subject", fmt.Sprintf("%s - 您的简历PDF文件", s.config.App.Name))

	// 设置HTML正文
	m.SetBody("text/html", htmlBody.String())

	// 添加纯文本备用内容
	textContent := fmt.Sprintf("您好！您的简历PDF文件 %s 已生成完成，请查收附件。", resumeName)
	m.AddAlternative("text/plain", textContent)

	// 添加PDF附件
	filename := resumeName
	if filename == "" {
		filename = "resume.pdf"
	}
	// 确保文件名以.pdf结尾
	if len(filename) < 4 || filename[len(filename)-4:] != ".pdf" {
		filename += ".pdf"
	}

	// 使用 Attach 从 []byte 添加PDF附件
	m.Attach(filename, gomail.SetCopyFunc(func(w io.Writer) error {
		_, err := w.Write(resumeData)
		return err
	}), gomail.SetHeader(map[string][]string{
		"Content-Type": {"application/pdf"},
	}))

	// 发送邮件
	d := gomail.NewDialer(s.smtpHost, s.smtpPort, s.username, s.password)
	d.SSL = true

	if err := d.DialAndSend(m); err != nil {
		s.logger.Error("发送简历邮件失败",
			zap.String("email", email),
			zap.String("resumeName", resumeName),
			zap.Error(err))
		return err
	}

	s.logger.Info("简历邮件发送成功",
		zap.String("email", email),
		zap.String("resumeName", resumeName),
		zap.Int("attachmentSize", len(resumeData)))
	return nil
}
