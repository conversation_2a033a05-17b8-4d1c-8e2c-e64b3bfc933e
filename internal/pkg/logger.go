package pkg

import (
	"os"
	"strings"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"resume-server/config"
)

// Logger 全局日志实例
var Logger *zap.Logger

// InitLogger 初始化日志配置
func InitLogger(cfg *config.Config) error {
	var level zapcore.Level
	switch strings.ToLower(cfg.Log.Level) {
	case "debug":
		level = zapcore.DebugLevel
	case "info":
		level = zapcore.InfoLevel
	case "warn":
		level = zapcore.WarnLevel
	case "error":
		level = zapcore.ErrorLevel
	default:
		level = zapcore.InfoLevel
	}

	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.LowercaseLevelEncoder,
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
	}

	var encoder zapcore.Encoder
	var writeSyncer zapcore.WriteSyncer

	// 根据配置确定日志输出方式
	if strings.ToLower(cfg.Log.Channel) == "file" {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
		// 创建logs目录
		if err := os.MkdirAll("logs", 0755); err != nil {
			return err
		}
		file, err := os.OpenFile("logs/app.log", os.O_CREATE|os.O_APPEND|os.O_WRONLY, 0644)
		if err != nil {
			return err
		}
		writeSyncer = zapcore.AddSync(file)
	} else {
		// 默认控制台输出
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
		writeSyncer = zapcore.AddSync(os.Stdout)
	}

	core := zapcore.NewCore(encoder, writeSyncer, level)

	// 开发环境添加调用信息
	var opts []zap.Option
	if cfg.App.Debug {
		opts = append(opts, zap.Development(), zap.AddCaller(), zap.AddCallerSkip(1))
	}

	Logger = zap.New(core, opts...)
	return nil
}

// Debug Debug级别日志
func Debug(msg string, fields ...zap.Field) {
	Logger.Debug(msg, fields...)
}

// Info Info级别日志
func Info(msg string, fields ...zap.Field) {
	Logger.Info(msg, fields...)
}

// Warn Warn级别日志
func Warn(msg string, fields ...zap.Field) {
	Logger.Warn(msg, fields...)
}

// Error Error级别日志
func Error(msg string, fields ...zap.Field) {
	Logger.Error(msg, fields...)
}

// Fatal Fatal级别日志
func Fatal(msg string, fields ...zap.Field) {
	Logger.Fatal(msg, fields...)
}
