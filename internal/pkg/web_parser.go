package pkg

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"go.uber.org/zap"
	"resume-server/config"
)

// WebParserService 网页解析服务接口
type WebParserService interface {
	// ReadLink 解析网页链接内容
	ReadLink(ctx context.Context, url string) (string, error)
}

// webParserService 网页解析服务实现
type webParserService struct {
	client  *http.Client
	baseURL string
	logger  *zap.Logger
}

// ReadLinkRequest 读取链接请求结构
type ReadLinkRequest struct {
	URL string `json:"url" binding:"required"`
}

// ReadLinkResponse 读取链接响应结构
type ReadLinkResponse struct {
	Content string `json:"content"`
	Title   string `json:"title,omitempty"`
	Error   string `json:"error,omitempty"`
}

// APIResponse API标准响应结构
type APIResponse struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    string `json:"data"`
}

// NewWebParserService 创建网页解析服务实例
func NewWebParserService(cfg *config.Config, logger *zap.Logger) WebParserService {
	return &webParserService{
		client: &http.Client{
			Timeout: cfg.WebParser.Timeout,
		},
		baseURL: cfg.WebParser.BaseURL,
		logger:  logger,
	}
}

// NewWebParserServiceWithBaseURL 创建带自定义API地址的网页解析服务实例
func NewWebParserServiceWithBaseURL(baseURL string, logger *zap.Logger) WebParserService {
	return &webParserService{
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
		baseURL: baseURL,
		logger:  logger,
	}
}

// ReadLink 解析网页链接内容
// url: 要解析的网页URL
// 返回: 解析后的网页内容字符串和错误信息
func (s *webParserService) ReadLink(ctx context.Context, url string) (string, error) {
	if url == "" {
		return "", fmt.Errorf("URL不能为空")
	}

	// 构建请求体
	requestBody := ReadLinkRequest{
		URL: url,
	}

	// 序列化请求体
	jsonData, err := json.Marshal(requestBody)
	if err != nil {
		s.logger.Error("序列化请求体失败", zap.Error(err), zap.String("url", url))
		return "", fmt.Errorf("序列化请求体失败: %w", err)
	}

	// 构建API请求URL
	apiURL := fmt.Sprintf("%s/read-link", s.baseURL)

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		s.logger.Error("创建HTTP请求失败", zap.Error(err), zap.String("api_url", apiURL))
		return "", fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	// 调试日志：打印请求详情
	s.logger.Info("发送API请求",
		zap.String("method", "POST"),
		zap.String("api_url", apiURL),
		zap.String("request_body", string(jsonData)),
		zap.String("target_url", url))

	// 记录请求开始时间
	startTime := time.Now()

	// 发送HTTP请求
	resp, err := s.client.Do(req)
	if err != nil {
		s.logger.Error("发送HTTP请求失败",
			zap.Error(err),
			zap.String("url", url),
			zap.String("api_url", apiURL),
			zap.Duration("duration", time.Since(startTime)))

		// 检查是否是连接错误
		if strings.Contains(err.Error(), "connection refused") {
			return "", fmt.Errorf("无法连接到网页解析服务 %s，请确保服务正在运行: %w", apiURL, err)
		}

		return "", fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 记录请求完成时间
	duration := time.Since(startTime)

	// 读取响应体
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		s.logger.Error("读取响应体失败",
			zap.Error(err),
			zap.String("url", url),
			zap.Int("status_code", resp.StatusCode),
			zap.Duration("duration", duration))
		return "", fmt.Errorf("读取响应体失败: %w", err)
	}

	// 调试日志：打印响应体内容和响应头
	responseHeaders := make(map[string]string)
	for key, values := range resp.Header {
		if len(values) > 0 {
			responseHeaders[key] = values[0]
		}
	}

	s.logger.Info("API响应详情",
		zap.String("url", url),
		zap.Int("status_code", resp.StatusCode),
		zap.Int("response_length", len(responseBody)),
		zap.String("response_body", string(responseBody)),
		zap.Any("response_headers", responseHeaders),
		zap.Duration("duration", duration))

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		s.logger.Error("API请求失败",
			zap.String("url", url),
			zap.Int("status_code", resp.StatusCode),
			zap.String("response_body", string(responseBody)),
			zap.Duration("duration", duration))
		return "", fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(responseBody))
	}

	// 首先尝试解析为API标准响应格式
	var apiResponse APIResponse
	if err := json.Unmarshal(responseBody, &apiResponse); err == nil && apiResponse.Code == 200 {
		// 使用API响应中的data字段作为内容
		content := apiResponse.Data

		s.logger.Info("网页解析成功",
			zap.String("url", url),
			zap.String("api_message", apiResponse.Message),
			zap.Int("content_length", len(content)),
			zap.Duration("duration", duration))

		return content, nil
	}

	// 如果不是标准API响应，尝试解析为原始格式
	var response ReadLinkResponse
	if err := json.Unmarshal(responseBody, &response); err != nil {
		s.logger.Error("解析响应JSON失败",
			zap.Error(err),
			zap.String("url", url),
			zap.String("response_body", string(responseBody)),
			zap.Duration("duration", duration))
		return "", fmt.Errorf("解析响应JSON失败: %w", err)
	}

	// 检查API返回的错误
	if response.Error != "" {
		s.logger.Error("API返回错误",
			zap.String("url", url),
			zap.String("api_error", response.Error),
			zap.Duration("duration", duration))
		return "", fmt.Errorf("API返回错误: %s", response.Error)
	}

	// 记录成功日志
	s.logger.Info("网页解析成功",
		zap.String("url", url),
		zap.String("title", response.Title),
		zap.Int("content_length", len(response.Content)),
		zap.Duration("duration", duration))

	return response.Content, nil
}

// ReadLinkWithRetry 带重试机制的网页解析
// url: 要解析的网页URL
// maxRetries: 最大重试次数
// 返回: 解析后的网页内容字符串和错误信息
func (s *webParserService) ReadLinkWithRetry(ctx context.Context, url string, maxRetries int) (string, error) {
	var lastErr error

	for i := 0; i <= maxRetries; i++ {
		content, err := s.ReadLink(ctx, url)
		if err == nil {
			return content, nil
		}

		lastErr = err

		// 如果不是最后一次重试，等待一段时间后重试
		if i < maxRetries {
			waitTime := time.Duration(i+1) * time.Second
			s.logger.Warn("网页解析失败，准备重试",
				zap.String("url", url),
				zap.Error(err),
				zap.Int("retry_count", i+1),
				zap.Int("max_retries", maxRetries),
				zap.Duration("wait_time", waitTime))

			select {
			case <-ctx.Done():
				return "", ctx.Err()
			case <-time.After(waitTime):
				// 继续重试
			}
		}
	}

	return "", fmt.Errorf("网页解析失败，已重试%d次: %w", maxRetries, lastErr)
}

// SetBaseURL 设置API基础URL
func (s *webParserService) SetBaseURL(baseURL string) {
	s.baseURL = baseURL
}

// SetTimeout 设置HTTP客户端超时时间
func (s *webParserService) SetTimeout(timeout time.Duration) {
	s.client.Timeout = timeout
}
