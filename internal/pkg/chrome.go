package pkg

import (
	"bytes"
	"context"
	"fmt"
	"image"
	"image/jpeg"
	_ "image/png" // 导入PNG解码器
	"time"

	"github.com/chromedp/cdproto/network"
	"github.com/chromedp/cdproto/page"
	"github.com/chromedp/chromedp"
)

// ImageOptions 图片生成选项
type ImageOptions struct {
	Format  string  // "png" 或 "jpeg"，默认 "png"
	Quality int     // JPEG质量 1-100，默认 80
	Scale   float64 // 缩放比例，默认 1.0
}

// ChromeService Chrome浏览器服务接口
type ChromeService interface {
	GeneratePDF(url string, jwtToken string, fingerprint string) ([]byte, error)
	GenerateElementImage(url string, elementID string, jwtToken string, fingerprint string) ([]byte, error)
	GenerateElementImageWithOptions(url string, elementID string, jwtToken string, fingerprint string, options *ImageOptions) ([]byte, error)
}

// chromeService Chrome浏览器服务实现
type chromeService struct {
	timeout time.Duration
}

// NewChromeService 创建Chrome服务实例
func NewChromeService() ChromeService {
	return &chromeService{
		timeout: 60 * time.Second, // 默认60秒超时
	}
}

// createChromeContext 创建Chrome上下文并返回清理函数
func (s *chromeService) createChromeContext(ctx context.Context, opts []chromedp.ExecAllocatorOption) (context.Context, func(), error) {
	// 创建分配器上下文
	allocCtx, allocCancel := chromedp.NewExecAllocator(ctx, opts...)

	// 创建浏览器上下文
	browserCtx, browserCancel := chromedp.NewContext(allocCtx)

	// 返回清理函数
	cleanup := func() {
		// 确保浏览器进程被正确终止
		chromedp.Cancel(browserCtx)
		browserCancel()
		allocCancel()
	}

	return browserCtx, cleanup, nil
}

// GeneratePDF 生成PDF文档
// url: 要访问的网址
// jwtToken: JWT认证令牌
// fingerprint: 浏览器指纹
// 返回: PDF文件的字节数组和错误信息
func (s *chromeService) GeneratePDF(url string, jwtToken string, fingerprint string) ([]byte, error) {
	// 创建Chrome上下文，设置超时
	ctx, cancel := context.WithTimeout(context.Background(), s.timeout)
	defer cancel()

	// 配置Chrome选项
	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.Flag("headless", true),              // 无头模式
		chromedp.Flag("disable-gpu", true),           // 禁用GPU
		chromedp.Flag("no-sandbox", true),            // 禁用沙箱
		chromedp.Flag("disable-dev-shm-usage", true), // 禁用/dev/shm使用
		chromedp.WindowSize(1920, 1080),              // 设置窗口大小
	)

	// 创建Chrome上下文
	browserCtx, cleanup, err := s.createChromeContext(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("创建Chrome上下文失败: %w", err)
	}
	defer cleanup()

	var pdfBuffer []byte

	// 添加panic恢复机制，确保进程清理
	defer func() {
		if r := recover(); r != nil {
			// 发生panic时也要确保进程被清理
			chromedp.Cancel(browserCtx)
		}
	}()

	// 执行浏览器操作
	err = chromedp.Run(browserCtx,
		// 导航到指定URL
		chromedp.Navigate(url),

		// 设置JWT Token Cookie
		chromedp.ActionFunc(func(ctx context.Context) error {
			if jwtToken != "" {
				err := network.SetCookie("token", jwtToken).
					WithDomain(extractDomain(url)).
					WithPath("/").
					WithHTTPOnly(true).
					WithSecure(true).
					WithSameSite(network.CookieSameSiteLax).
					Do(ctx)
				if err != nil {
					return err
				}
			}
			return nil
		}),

		// 设置浏览器指纹Cookie
		chromedp.ActionFunc(func(ctx context.Context) error {
			if fingerprint != "" {
				err := network.SetCookie("FingerPrint", fingerprint).
					WithDomain(extractDomain(url)).
					WithPath("/").
					WithHTTPOnly(true).
					WithSecure(true).
					WithSameSite(network.CookieSameSiteLax).
					Do(ctx)
				if err != nil {
					return err
				}
			}
			return nil
		}),

		// 如果设置了Token或指纹，重新加载页面以应用认证状态
		chromedp.ActionFunc(func(ctx context.Context) error {
			if jwtToken != "" || fingerprint != "" {
				return chromedp.Reload().Do(ctx)
			}
			return nil
		}),

		// 等待页面body元素加载完成
		chromedp.WaitVisible("body", chromedp.ByQuery),

		// 尝试等待markdown-viewer-container元素出现，如果失败则降级到固定等待
		chromedp.ActionFunc(func(ctx context.Context) error {
			// 创建一个较短的超时上下文用于等待特定元素
			waitCtx, waitCancel := context.WithTimeout(ctx, 20*time.Second)
			defer waitCancel()

			// 尝试等待markdown-viewer-container元素
			err := chromedp.WaitVisible(".markdown-viewer-container", chromedp.ByQuery).Do(waitCtx)
			if err != nil {
				// 如果等待失败，使用固定等待时间作为降级策略
				return chromedp.Sleep(3 * time.Second).Do(ctx)
			}
			return nil
		}),

		// 等待额外1秒确保兼容性
		chromedp.Sleep(2*time.Second),

		// 生成PDF
		chromedp.ActionFunc(func(ctx context.Context) error {
			var err error
			pdfBuffer, _, err = page.PrintToPDF().
				WithPrintBackground(true).   // 包含背景色和图片
				WithPreferCSSPageSize(true). // 使用CSS页面大小设置
				WithScale(1.0).              // 缩放比例
				Do(ctx)
			return err
		}),
	)

	if err != nil {
		return nil, fmt.Errorf("生成PDF失败: %w", err)
	}

	if len(pdfBuffer) == 0 {
		return nil, fmt.Errorf("生成的PDF文件为空")
	}

	return pdfBuffer, nil
}

// extractDomain 从URL中提取域名
func extractDomain(url string) string {
	// 简单的域名提取，实际项目中可能需要更复杂的解析
	if len(url) > 8 && url[:8] == "https://" {
		domain := url[8:]
		if idx := findFirstSlash(domain); idx != -1 {
			domain = domain[:idx]
		}
		return domain
	}
	if len(url) > 7 && url[:7] == "http://" {
		domain := url[7:]
		if idx := findFirstSlash(domain); idx != -1 {
			domain = domain[:idx]
		}
		return domain
	}
	return "localhost"
}

// findFirstSlash 查找第一个斜杠的位置
func findFirstSlash(s string) int {
	for i, c := range s {
		if c == '/' {
			return i
		}
	}
	return -1
}

// GenerateElementImage 生成指定DOM元素的图片（使用默认设置）
// url: 要访问的网址
// elementID: 要截图的DOM元素ID（不包含#号）
// jwtToken: JWT认证令牌
// fingerprint: 浏览器指纹
// 返回: 图片文件的字节数组和错误信息
func (s *chromeService) GenerateElementImage(url string, elementID string, jwtToken string, fingerprint string) ([]byte, error) {
	// 使用JPEG压缩作为默认选项（针对简历预览图优化）
	defaultOptions := &ImageOptions{
		Format:  "jpeg",
		Quality: 70, // 降低到70，对简历文档类图片效果仍然很好
		Scale:   1.0,
	}
	return s.GenerateElementImageWithOptions(url, elementID, jwtToken, fingerprint, defaultOptions)
}

// GenerateElementImageWithOptions 生成指定DOM元素的图片（支持质量控制）
// url: 要访问的网址
// elementID: 要截图的DOM元素ID（不包含#号）
// jwtToken: JWT认证令牌
// fingerprint: 浏览器指纹
// options: 图片生成选项
// 返回: 图片文件的字节数组和错误信息
func (s *chromeService) GenerateElementImageWithOptions(url string, elementID string, jwtToken string, fingerprint string, options *ImageOptions) ([]byte, error) {
	// 设置默认选项
	if options == nil {
		options = &ImageOptions{
			Format:  "png",
			Quality: 80,
			Scale:   1.0,
		}
	}

	// 验证和设置默认值
	if options.Format == "" {
		options.Format = "png"
	}
	if options.Quality <= 0 || options.Quality > 100 {
		options.Quality = 80
	}
	if options.Scale <= 0 {
		options.Scale = 1.0
	}

	// 创建Chrome上下文，设置超时
	ctx, cancel := context.WithTimeout(context.Background(), s.timeout)
	defer cancel()

	// 配置Chrome选项，根据缩放比例调整窗口大小
	windowWidth := int(1920 * options.Scale)
	windowHeight := int(1080 * options.Scale)

	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.Flag("headless", true),                // 无头模式
		chromedp.Flag("disable-gpu", true),             // 禁用GPU
		chromedp.Flag("no-sandbox", true),              // 禁用沙箱
		chromedp.Flag("disable-dev-shm-usage", true),   // 禁用/dev/shm使用
		chromedp.WindowSize(windowWidth, windowHeight), // 根据缩放比例设置窗口大小
	)

	// 创建Chrome上下文
	browserCtx, cleanup, err := s.createChromeContext(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("创建Chrome上下文失败: %w", err)
	}
	defer cleanup()

	var imageBuffer []byte
	selector := "#" + elementID // 构建CSS选择器

	// 添加panic恢复机制，确保进程清理
	defer func() {
		if r := recover(); r != nil {
			// 发生panic时也要确保进程被清理
			chromedp.Cancel(browserCtx)
		}
	}()

	// 执行浏览器操作
	err = chromedp.Run(browserCtx,
		// 导航到指定URL
		chromedp.Navigate(url),

		// 设置JWT Token Cookie
		chromedp.ActionFunc(func(ctx context.Context) error {
			if jwtToken != "" {
				err := network.SetCookie("token", jwtToken).
					WithDomain(extractDomain(url)).
					WithPath("/").
					WithHTTPOnly(true).
					WithSecure(true).
					WithSameSite(network.CookieSameSiteLax).
					Do(ctx)
				if err != nil {
					return err
				}
			}
			return nil
		}),

		// 设置浏览器指纹Cookie
		chromedp.ActionFunc(func(ctx context.Context) error {
			if fingerprint != "" {
				err := network.SetCookie("FingerPrint", fingerprint).
					WithDomain(extractDomain(url)).
					WithPath("/").
					WithHTTPOnly(true).
					WithSecure(true).
					WithSameSite(network.CookieSameSiteLax).
					Do(ctx)
				if err != nil {
					return err
				}
			}
			return nil
		}),

		// 如果设置了Token或指纹，重新加载页面以应用认证状态
		chromedp.ActionFunc(func(ctx context.Context) error {
			if jwtToken != "" || fingerprint != "" {
				return chromedp.Reload().Do(ctx)
			}
			return nil
		}),

		// 等待页面body元素加载完成
		chromedp.WaitVisible("body", chromedp.ByQuery),

		// 尝试等待markdown-viewer-container元素出现，如果失败则降级到固定等待
		chromedp.ActionFunc(func(ctx context.Context) error {
			// 创建一个较短的超时上下文用于等待特定元素
			waitCtx, waitCancel := context.WithTimeout(ctx, 20*time.Second)
			defer waitCancel()

			// 尝试等待markdown-viewer-container元素
			err := chromedp.WaitVisible(".markdown-viewer-container", chromedp.ByQuery).Do(waitCtx)
			if err != nil {
				// 如果等待失败，使用固定等待时间作为降级策略
				return chromedp.Sleep(3 * time.Second).Do(ctx)
			}
			return nil
		}),

		// 等待指定元素可见
		chromedp.WaitVisible(selector, chromedp.ByQuery),

		// 滚动到目标元素位置，确保元素在视口内
		chromedp.ScrollIntoView(selector),

		// 等待额外1秒确保兼容性
		chromedp.Sleep(2*time.Second),

		// 截取指定元素的截图
		chromedp.Screenshot(selector, &imageBuffer, chromedp.ByQuery),
	)

	if err != nil {
		return nil, fmt.Errorf("生成元素图片失败: %w", err)
	}

	if len(imageBuffer) == 0 {
		return nil, fmt.Errorf("生成的图片文件为空")
	}

	// 如果需要转换格式或调整质量，进行图片处理
	if options.Format == "jpeg" {
		return s.convertToJPEG(imageBuffer, options.Quality)
	}

	return imageBuffer, nil
}

// convertToJPEG 将PNG图片转换为JPEG格式并设置质量
func (s *chromeService) convertToJPEG(pngData []byte, quality int) ([]byte, error) {
	// 解码PNG图片
	reader := bytes.NewReader(pngData)
	img, _, err := image.Decode(reader)
	if err != nil {
		return nil, fmt.Errorf("解码PNG图片失败: %w", err)
	}

	// 创建JPEG编码器
	var buf bytes.Buffer
	err = jpeg.Encode(&buf, img, &jpeg.Options{Quality: quality})
	if err != nil {
		return nil, fmt.Errorf("编码JPEG图片失败: %w", err)
	}

	return buf.Bytes(), nil
}
