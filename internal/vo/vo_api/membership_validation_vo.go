package vo_api

import (
	"time"

	"resume-server/internal/enum"
	"resume-server/internal/models"
)

// ValidationResult 权益校验结果
type ValidationResult struct {
	// 是否允许使用
	Allowed bool `json:"allowed" example:"true"`

	// 不允许使用的原因（当Allowed为false时）
	Reason string `json:"reason,omitempty" example:"已达到使用上限，请升级会员"`

	// 当前使用次数
	CurrentUsage int `json:"current_usage" example:"5"`

	// 限制次数 (-1表示无限制)
	Limit int `json:"limit" example:"10"`

	// 剩余次数 (-1表示无限制)
	Remaining int `json:"remaining" example:"5"`
}

// MembershipInfo 会员信息
type MembershipInfo struct {
	// 是否登录
	IsLoggedIn bool `json:"is_logged_in"`

	// 用户类型
	UserType enum.UserType `json:"user_type"`

	// 会员套餐信息（如果是会员）
	MembershipPlan *models.MembershipPlan `json:"membership_plan,omitempty"`

	// 会员开始时间
	StartTime *time.Time `json:"start_time,omitempty"`

	// 会员结束时间
	EndTime *time.Time `json:"end_time,omitempty"`

	// 会员是否有效（未过期）
	IsValid bool `json:"is_valid"`
}

// UserPrivilegeStatus 用户权益状态
type UserPrivilegeStatus struct {
	// 用户ID
	UserID uint `json:"user_id" example:"1"`

	// 会员信息
	MembershipInfo *MembershipInfo `json:"membership_info"`

	// 各项权益的使用情况
	Privileges map[enum.PrivilegeType]*ValidationResult `json:"privileges"`
}

// PrivilegeUsageInfo 权益使用信息
type PrivilegeUsageInfo struct {
	// 权益类型
	PrivilegeType enum.PrivilegeType `json:"privilege_type"`

	// 权益名称
	PrivilegeName string `json:"privilege_name"`

	// 当前使用次数
	CurrentUsage int `json:"current_usage"`

	// 限制次数 (-1表示无限制)
	Limit int `json:"limit"`

	// 剩余次数 (-1表示无限制)
	Remaining int `json:"remaining"`

	// 是否可用
	Available bool `json:"available"`
}
