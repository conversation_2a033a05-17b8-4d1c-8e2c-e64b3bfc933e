package vo_api

import (
	"time"

	"resume-server/internal/models"
)

// ResumeDetailResponse 简历详情响应VO
type ResumeDetailResponse struct {
	ID              uint      `json:"id" example:"1"`
	UserID          uint      `json:"user_id" example:"1"`
	TemplateID      uint      `json:"template_id" example:"1"`
	ComponentName   string    `json:"component_name" example:"ResumeTemplate1"`
	ResumeName      string    `json:"resume_name" example:"我的简历"`
	PreviewImageUrl string    `json:"preview_image_url" example:"https://cdn.avrilko.com/preview/123.jpg"`
	CompletionRate  string    `json:"completion_rate" example:"75%"`
	CreatedAt       time.Time `json:"created_at" example:"2023-01-01T12:00:00Z"`
	UpdatedAt       time.Time `json:"updated_at" example:"2023-01-01T12:00:00Z"`
	Diff            string    `json:"diff" example:"优化了工作经历描述，增强了项目经历的技术细节"`

	// 简历详情内容
	BasicInfo       models.BasicInfo       `json:"basic_info"`
	Education       models.Education       `json:"education"`
	Work            models.Work            `json:"work"`
	Project         models.Project         `json:"project"`
	Research        models.Research        `json:"research"`
	Team            models.Team            `json:"team"`
	Portfolio       models.Portfolio       `json:"portfolio"`
	Other           models.Other           `json:"other"`
	PersonalSummary models.PersonalSummary `json:"personal_summary"`
	Honors          models.Honors          `json:"honors"`
	Skills          models.Skills          `json:"skills"`
	CustomModules   models.CustomModules   `json:"custom_modules"`
	Slogan          models.Slogan          `json:"slogan"`
	ResumeStyle     models.ResumeStyle     `json:"resume_style"`
}

// SaveResumeResponse 保存简历响应VO
type SaveResumeResponse struct {
	UpdatedAt time.Time `json:"updated_at" example:"2023-01-01T12:00:00Z"`
}

// UpdateResumeNameResponse 修改简历名称响应VO
type UpdateResumeNameResponse struct {
	ResumeName string    `json:"resume_name" example:"我的新简历"`
	UpdatedAt  time.Time `json:"updated_at" example:"2023-01-01T12:00:00Z"`
}

// DownloadResumePDFResponse 下载简历PDF响应VO
type DownloadResumePDFResponse struct {
	PdfUrl string `json:"pdf_url" example:"https://cdn.avrilko.com/resume/pdf/123456.pdf"`
}

// ShareResumeByEmailResponse 邮件分享简历响应VO
type ShareResumeByEmailResponse struct {
	Message string `json:"message" example:"简历邮件发送成功"`
}

// GetResumeBasicInfoResponse 获取简历基本信息响应VO
type GetResumeBasicInfoResponse struct {
	ID              uint      `json:"id" example:"1"`
	ResumeName      string    `json:"resume_name" example:"张三的简历"`
	PreviewImageUrl string    `json:"preview_image_url" example:"https://cdn.avrilko.com/resume/preview/123.png"`
	CompletionRate  string    `json:"completion_rate" example:"75%"`
	CreatedAt       time.Time `json:"created_at" example:"2023-01-01T00:00:00Z"`
}

// GetAllMyResumesResponse 获取所有自己简历响应VO
type GetAllMyResumesResponse struct {
	Resumes []ResumeBasicItem `json:"resumes"`
}

// ResumeBasicItem 简历基本信息项
type ResumeBasicItem struct {
	ID              uint      `json:"id" example:"1"`
	TemplateID      uint      `json:"template_id" example:"1"`
	ResumeName      string    `json:"resume_name" example:"我的简历"`
	PreviewImageUrl string    `json:"preview_image_url" example:"https://cdn.avrilko.com/preview/123.jpg"`
	CompletionRate  string    `json:"completion_rate" example:"75%"`
	CreatedAt       time.Time `json:"created_at" example:"2023-01-01T12:00:00Z"`
	UpdatedAt       time.Time `json:"updated_at" example:"2023-01-01T12:00:00Z"`
}

// GetDeletedResumesResponse 获取回收站简历响应VO
type GetDeletedResumesResponse struct {
	Resumes []DeletedResumeItem `json:"resumes"`
}

// DeletedResumeItem 回收站简历项
type DeletedResumeItem struct {
	ID              uint      `json:"id" example:"1"`
	TemplateID      uint      `json:"template_id" example:"1"`
	ResumeName      string    `json:"resume_name" example:"我的简历"`
	PreviewImageUrl string    `json:"preview_image_url" example:"https://cdn.avrilko.com/preview/123.jpg"`
	CompletionRate  string    `json:"completion_rate" example:"75%"`
	CreatedAt       time.Time `json:"created_at" example:"2023-01-01T12:00:00Z"`
	UpdatedAt       time.Time `json:"updated_at" example:"2023-01-01T12:00:00Z"`
	DeletedAt       time.Time `json:"deleted_at" example:"2023-01-01T12:00:00Z"`
}

// ApplyDraftResponse 应用草稿响应VO
type ApplyDraftResponse struct {
	ResumeID uint `json:"resume_id" example:"1"`
}
