package vo_api

import (
	"resume-server/internal/models"
)

// TemplateListItemResponse 模板列表项响应VO
type TemplateListItemResponse struct {
	ID              uint               `json:"id" example:"1"`                                                // 模板ID
	TemplateName    string             `json:"template_name" example:"简洁风格简历模板"`                              // 模板名称
	Tags            models.StringArray `json:"tags" example:"['简洁','专业','技术']"`                               // 模板标签
	PreviewImageUrl string             `json:"preview_image_url" example:"https://example.com/preview/1.jpg"` // 预览图链接
	UsageCount      int                `json:"usage_count" example:"1250"`                                    // 使用人数
}

// TemplateListResponse 模板列表响应VO（用于Swagger文档）
type TemplateListResponse struct {
	Total    int64                      `json:"total" example:"100"`    // 总记录数
	Page     int                        `json:"page" example:"1"`       // 当前页码
	PageSize int                        `json:"page_size" example:"20"` // 每页条数
	List     []TemplateListItemResponse `json:"list"`                   // 模板列表
}
