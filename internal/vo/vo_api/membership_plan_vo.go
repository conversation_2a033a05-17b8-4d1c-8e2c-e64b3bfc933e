package vo_api

import (
	"resume-server/internal/models"
)

// MembershipPlanResponse 会员套餐响应视图对象
type MembershipPlanResponse struct {
	ID              uint     `json:"id" example:"1"`                                                   // 套餐ID
	Name            string   `json:"name" example:"月度会员"`                                              // 套餐名称
	ActualPrice     float64  `json:"actual_price" example:"29.9"`                                      // 实际价格(单位:元)
	OriginalPrice   float64  `json:"original_price" example:"39.9"`                                    // 原价(单位:元)
	Description     []string `json:"description" example:"无限简历创建,高级模板使用权,AI优化建议"`                      // 描述信息
	ResumeLimit     int      `json:"resume_limit" example:"0"`                                         // 能创建简历的个数(0表示无限制)
	AiGenerateLimit int      `json:"ai_generate_limit" example:"0"`                                    // AI生成次数限制(0表示无限制)
	AiRewriteLimit  int      `json:"ai_rewrite_limit" example:"0"`                                     // AI改写次数限制(0表示无限制)
	AiOptimizeLimit int      `json:"ai_optimize_limit" example:"0"`                                    // AI简历优化次数限制(0表示无限制)
	AiDiagnoseLimit int      `json:"ai_diagnose_limit" example:"0"`                                    // AI简历打分次数限制(0表示无限制)
	AiOneClickLimit int      `json:"ai_one_click_limit" example:"0"`                                   // AI一键生成简历次数限制(0表示无限制)
	CornerImageUrl  string   `json:"corner_image_url" example:"https://example.com/images/corner.png"` // 右上角图片地址
	DiscountTip     string   `json:"discount_tip" example:"限时优惠"`                                      // 优惠提示
	IsDefault       int      `json:"is_default" example:"2"`                                           // 是否默认 1:非默认 2:默认
}

// MembershipPlanListResponse 会员套餐列表响应
type MembershipPlanListResponse struct {
	List []MembershipPlanResponse `json:"list"` // 列表数据
}

// DownloadCouponPlanResponse 下载券套餐响应视图对象
type DownloadCouponPlanResponse struct {
	ID            uint    `json:"id" example:"1"`                // 套餐ID
	Name          string  `json:"name" example:"10次下载券"`         // 套餐名称
	ActualPrice   float64 `json:"actual_price" example:"9.9"`    // 实际价格(单位:元)
	OriginalPrice float64 `json:"original_price" example:"19.9"` // 原价(单位:元)
	ResumeLimit   int     `json:"resume_limit" example:"10"`     // 简历数量限制 (0表示无限制)
}

// DownloadCouponPlanListResponse 下载券套餐列表响应
type DownloadCouponPlanListResponse struct {
	List []DownloadCouponPlanResponse `json:"list"` // 列表数据
}

// ConvertToMembershipPlanResponse 将会员套餐模型转换为响应视图对象
func ConvertToMembershipPlanResponse(plan *models.MembershipPlan, _ string) MembershipPlanResponse {
	return MembershipPlanResponse{
		ID:              plan.ID,
		Name:            plan.Name,
		ActualPrice:     plan.ActualPrice,
		OriginalPrice:   plan.OriginalPrice,
		Description:     plan.Description,
		ResumeLimit:     plan.ResumeLimit,
		AiGenerateLimit: plan.AiGenerateLimit,
		AiRewriteLimit:  plan.AiRewriteLimit,
		AiOptimizeLimit: plan.AiOptimizeLimit,
		AiDiagnoseLimit: plan.AiDiagnoseLimit,
		AiOneClickLimit: plan.AiOneClickLimit,
		CornerImageUrl:  plan.CornerImageUrl,
		DiscountTip:     plan.DiscountTip,
		IsDefault:       int(plan.IsDefault),
	}
}

// ConvertToDownloadCouponPlanResponse 将套餐模型转换为下载券套餐响应视图对象
func ConvertToDownloadCouponPlanResponse(plan *models.MembershipPlan) DownloadCouponPlanResponse {
	return DownloadCouponPlanResponse{
		ID:            plan.ID,
		Name:          plan.Name,
		ActualPrice:   plan.ActualPrice,
		OriginalPrice: plan.OriginalPrice,
		ResumeLimit:   plan.ResumeLimit,
	}
}
