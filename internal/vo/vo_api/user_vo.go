package vo_api

import (
	"time"

	"resume-server/internal/enum"
)

// UserResponse 用户信息响应视图对象
type UserResponse struct {
	ID         uint          `json:"id" example:"1"`
	Username   string        `json:"username" example:"johndoe"`
	Email      string        `json:"email" example:"<EMAIL>"`
	Phone      string        `json:"phone" example:"13812345678"`
	OpenID     string        `json:"open_id" example:"oNHwxjgrzgL9H_A2pGLSMuME-X-Q"`
	Avatar     string        `json:"avatar" example:"http://example.com/avatar.jpg"`
	CreatedAt  time.Time     `json:"created_at" example:"2023-01-01T12:00:00Z"` // 创建时间
	UserType   enum.UserType `json:"user_type" example:"2"`                     // 用户类型 1:游客 2:普通用户 3:会员
	IsLoggedIn bool          `json:"is_logged_in" example:"true"`               // 是否登录，JWT认证成功为true，指纹认证为false
}

// TokenResponse 令牌响应视图对象
type TokenResponse struct {
	Token string `json:"token" example:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."`
}

// SimpleUserResponse 简化的用户信息响应视图对象
type SimpleUserResponse struct {
	ID           uint   `json:"id" example:"1"`
	Username     string `json:"username" example:"johndoe"`
	Avatar       string `json:"avatar" example:"http://example.com/avatar.jpg"`
	IsTeamMember bool   `json:"is_team_member" example:"false"` // 是否已加入当前团队
}
