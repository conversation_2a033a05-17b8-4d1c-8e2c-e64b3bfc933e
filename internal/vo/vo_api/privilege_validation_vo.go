package vo_api

import (
	"resume-server/internal/enum"
)

// BatchValidatePrivilegeResponse 批量权限校验响应
type BatchValidatePrivilegeResponse struct {
	// 是否所有权限都通过校验
	AllAllowed bool `json:"all_allowed" example:"false"`

	// 弹窗类型（当AllAllowed为false时）
	ModalType enum.ModalType `json:"modal_type,omitempty" example:"1"`

	// 弹窗标题（当AllAllowed为false时）
	ModalTitle string `json:"modal_title,omitempty" example:"登录提示"`

	// 弹窗描述（当AllAllowed为false时）
	ModalDescription string `json:"modal_description,omitempty" example:"请先登录后使用此功能"`
}
