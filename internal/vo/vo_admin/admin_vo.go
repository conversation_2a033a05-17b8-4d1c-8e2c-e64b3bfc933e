package vo_admin

import (
	"time"
)

// AdminInfoResponse 管理员信息响应视图对象
type AdminInfoResponse struct {
	ID           uint      `json:"id" example:"1"`
	Username     string    `json:"username" example:"admin"`
	Channels     []string  `json:"channels" example:"['web','mobile']"`
	IsSuperAdmin bool      `json:"is_super_admin" example:"true"`
	CreatedAt    time.Time `json:"created_at" example:"2024-01-01T12:00:00Z"`
}
