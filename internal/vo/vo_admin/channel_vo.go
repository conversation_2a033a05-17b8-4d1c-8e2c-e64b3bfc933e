package vo_admin

import (
	"resume-server/internal/enum"
	"time"
)

// ChannelUserResponse 渠道用户响应视图对象
type ChannelUserResponse struct {
	ID        uint            `json:"id" example:"1"`
	Username  string          `json:"username" example:"user123"`
	Email     string          `json:"email" example:"<EMAIL>"`
	Phone     string          `json:"phone" example:"13812345678"`
	Channel   string          `json:"channel" example:"web"`
	UserType  enum.UserType   `json:"user_type" example:"2"`
	Status    enum.UserStatus `json:"status" example:"1"`
	CreatedAt time.Time       `json:"created_at" example:"2024-01-01T12:00:00Z"`
}

// ChannelUsersListResponse 渠道用户列表响应视图对象
type ChannelUsersListResponse struct {
	Total    int64                 `json:"total" example:"100"`
	Page     int                   `json:"page" example:"1"`
	PageSize int                   `json:"page_size" example:"10"`
	List     []ChannelUserResponse `json:"list"`
}

// ChannelPaymentResponse 渠道付费响应视图对象
type ChannelPaymentResponse struct {
	ID               uint      `json:"id" example:"1"`
	OrderNo          string    `json:"order_no" example:"ORD20240101001"`
	UserID           uint      `json:"user_id" example:"123"`
	Username         string    `json:"username" example:"user123"`
	Channel          string    `json:"channel" example:"web"`
	Amount           float64              `json:"amount" example:"99.00"`
	PaymentMethod    enum.PaymentMethod   `json:"payment_method" example:"1"`
	PaymentStatus    enum.PaymentStatus   `json:"payment_status" example:"3"`
	OrderCreatedAt   time.Time `json:"order_created_at" example:"2024-01-01T12:00:00Z"`
	UserCreatedAt    time.Time `json:"user_created_at" example:"2024-01-01T10:00:00Z"`
}

// ChannelPaymentsListResponse 渠道付费列表响应视图对象
type ChannelPaymentsListResponse struct {
	Total       int64                    `json:"total" example:"100"`
	Page        int                      `json:"page" example:"1"`
	PageSize    int                      `json:"page_size" example:"10"`
	TotalAmount float64                  `json:"total_amount" example:"9900.00"`
	List        []ChannelPaymentResponse `json:"list"`
}
