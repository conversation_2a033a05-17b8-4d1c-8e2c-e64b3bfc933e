package api

import (
	"github.com/gin-gonic/gin"
	"resume-server/internal/controllers/api"
)

// RegisterWechatRoutes 注册微信相关路由
func RegisterWechatRoutes(router *gin.RouterGroup, wechatController *api.WechatController) {
	// 微信接口路由组
	wechatGroup := router.Group("/wechat")
	{
		// 接收微信服务器推送的消息，支持GET(服务器验证)和POST(接收消息)请求
		wechatGroup.GET("/serve", wechatController.HandleServerVerification)
		wechatGroup.POST("/serve", wechatController.HandleMessagePush)
	}
}
