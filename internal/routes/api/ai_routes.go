package api

import (
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"resume-server/internal/controllers/api"
	"resume-server/internal/middleware"
	"resume-server/internal/pkg"
	"resume-server/internal/repository"
	"resume-server/internal/services"
)

// RegisterAIRoutes 注册AI相关路由
func RegisterAIRoutes(
	apiGroup *gin.RouterGroup,
	aiController *api.AIController,
	jwtService pkg.JWTService,
	userService services.UserService,
	userRepo repository.UserRepository,
	redisClient *redis.Client,
) {
	// AI路由组（使用游客中间件）
	aiGroup := apiGroup.Group("/ai")

	// 使用游客认证中间件，允许游客和登录用户访问
	aiGroup.Use(middleware.GuestAuthMiddleware(jwtService, userService, userRepo, redisClient))
	{
		// AI提示词处理接口
		aiGroup.POST("/prompt", middleware.WithValidation(aiController.Prompt))
		// 获取AI调用记录列表
		aiGroup.GET("/records", middleware.WithValidation(aiController.GetAICallRecords))
		// 文件解析接口
		aiGroup.POST("/parse-file", middleware.WithValidation(aiController.ParseFile))
		// AI生成简历接口
		aiGroup.POST("/generate-resume", middleware.WithValidation(aiController.GenerateResume))
		// AI优化简历接口
		aiGroup.POST("/optimize-resume", middleware.WithValidation(aiController.OptimizeResume))
		// AI简历打分接口
		aiGroup.POST("/score-resume", middleware.WithValidation(aiController.ScoreResume))
		// 批量权限校验接口
		aiGroup.POST("/batch-validate-privilege", middleware.WithValidation(aiController.BatchValidatePrivilege))
	}
}
