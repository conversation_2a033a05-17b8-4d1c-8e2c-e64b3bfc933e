package api

import (
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"resume-server/internal/controllers/api"
	"resume-server/internal/middleware"
	"resume-server/internal/pkg"
	"resume-server/internal/repository"
	"resume-server/internal/services"
)

// RegisterTemplateRoutes 注册模板相关路由
func RegisterTemplateRoutes(
	apiGroup *gin.RouterGroup,
	templateController *api.TemplateController,
	jwtService pkg.JWTService,
	userService services.UserService,
	userRepo repository.UserRepository,
	redisClient *redis.Client,
) {
	// 模板路由组（无需认证）
	templateGroup := apiGroup.Group("/templates")
	{
		// 获取模板列表（支持分页）
		templateGroup.GET("", middleware.WithValidation(templateController.GetTemplateList))

		// 使用模板（游客认证）
		templateGroup.POST("/use", middleware.GuestAuthMiddleware(jwtService, userService, userRepo, redisClient), middleware.WithValidation(templateController.UseTemplate))
	}
}
