package api

import (
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"resume-server/internal/controllers/api"
	"resume-server/internal/middleware"
	"resume-server/internal/pkg"
	"resume-server/internal/services"
)

// RegisterOrderRoutes 注册订单相关路由
func RegisterOrderRoutes(
	router *gin.RouterGroup,
	orderController *api.OrderController,
	jwtService pkg.JWTService,
	userService services.UserService,
	redisClient *redis.Client,
) {
	// 订单路由组
	orderGroup := router.Group("/orders")
	// 使用JWT认证中间件，只有登录用户才能访问
	orderGroup.Use(middleware.JWTAuthMiddleware(jwtService, userService, redisClient))
	{
		// 获取用户订单列表
		orderGroup.GET("", middleware.WithValidation(orderController.GetUserOrders))
		// 创建支付宝订单
		orderGroup.POST("/alipay", middleware.WithValidation(orderController.CreateAlipayOrder))
		// 创建微信支付订单
		orderGroup.POST("/wechat", middleware.WithValidation(orderController.CreateWechatPayOrder))
		// 查询订单状态
		orderGroup.POST("/status", middleware.WithValidation(orderController.GetOrderStatus))
	}
}
