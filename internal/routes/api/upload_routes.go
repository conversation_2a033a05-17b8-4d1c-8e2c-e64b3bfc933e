package api

import (
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"resume-server/internal/controllers/api"
	"resume-server/internal/middleware"
	"resume-server/internal/pkg"
	"resume-server/internal/repository"
	"resume-server/internal/services"
)

// RegisterUploadRoutes 注册上传相关路由
func RegisterUploadRoutes(
	router *gin.RouterGroup,
	uploadController *api.UploadController,
	jwtService pkg.JWTService,
	userService services.UserService,
	userRepo repository.UserRepository,
	redisClient *redis.Client,
) {
	// 上传路由组
	uploadGroup := router.Group("/upload")

	// 使用游客中间件，允许游客和登录用户上传
	uploadGroup.Use(middleware.GuestAuthMiddleware(jwtService, userService, userRepo, redisClient))
	{
		// 上传头像
		uploadGroup.POST("/avatar", middleware.WithValidation(uploadController.UploadAvatar))
		// 上传附件
		uploadGroup.POST("/attachment", middleware.WithValidation(uploadController.UploadAttachment))
	}
}
