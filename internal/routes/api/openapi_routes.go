package api

import (
	"github.com/gin-gonic/gin"
	"resume-server/internal/controllers/api"
)

// RegisterOpenAPIRoutes 注册OpenAPI相关路由
func RegisterOpenAPIRoutes(
	router *gin.RouterGroup,
	openAPIController *api.OpenAPIController,
) {
	// OpenAPI路由组
	openAPIGroup := router.Group("/openapi")
	{
		// 支付宝支付回调
		openAPIGroup.POST("/alipay", openAPIController.AlipayNotify)
		// 微信支付回调
		openAPIGroup.POST("/wechatpay", openAPIController.WechatPayNotify)
	}
}
