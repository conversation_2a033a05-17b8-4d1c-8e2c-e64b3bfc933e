package api

import (
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"resume-server/internal/controllers/api"
	"resume-server/internal/middleware"
	"resume-server/internal/pkg"
	"resume-server/internal/repository"
	"resume-server/internal/services"
)

// RegisterAuthRoutes 注册认证相关路由
func RegisterAuthRoutes(
	router *gin.RouterGroup,
	authController *api.AuthController,
	jwtService pkg.JWTService,
	userService services.UserService,
	userRepo repository.UserRepository,
	redisClient *redis.Client,
) {
	// 认证路由组
	authGroup := router.Group("/auth")
	// 使用游客中间件，允许游客和登录用户访问认证相关接口
	authGroup.Use(middleware.GuestAuthMiddleware(jwtService, userService, userRepo, redisClient))
	{
		// 验证码登录路由
		authGroup.POST("/login-code", middleware.WithValidation(authController.LoginCode))
		// 发送短信验证码
		authGroup.POST("/sms/code", middleware.WithValidation(authController.SendSMSCode))
		// 发送邮件验证码
		authGroup.POST("/email/code", middleware.WithValidation(authController.SendEmailCode))
		// 邮箱验证码登录
		authGroup.POST("/email/login", middleware.WithValidation(authController.LoginEmailCode))
		// 获取微信扫码登录二维码
		authGroup.GET("/login/qrcode", authController.GetLoginQrCode)
		// 检查二维码状态
		authGroup.POST("/login/qrcode/status", middleware.WithValidation(authController.CheckQrCodeStatus))
	}
}
