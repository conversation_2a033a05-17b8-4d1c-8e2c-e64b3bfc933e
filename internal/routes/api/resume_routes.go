package api

import (
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"resume-server/internal/controllers/api"
	"resume-server/internal/middleware"
	"resume-server/internal/pkg"
	"resume-server/internal/repository"
	"resume-server/internal/services"
)

// RegisterResumeRoutes 注册简历相关路由
func RegisterResumeRoutes(
	apiGroup *gin.RouterGroup,
	resumeController *api.ResumeController,
	jwtService pkg.JWTService,
	userService services.UserService,
	userRepo repository.UserRepository,
	redisClient *redis.Client,
) {
	// 简历路由组（使用游客中间件）
	resumeGroup := apiGroup.Group("/resumes")

	// 使用游客认证中间件，允许游客和登录用户访问
	resumeGroup.Use(middleware.GuestAuthMiddleware(jwtService, userService, userRepo, redisClient))
	{
		// 获取所有自己的简历
		resumeGroup.GET("", middleware.WithoutValidation(resumeController.GetAllMyResumes))

		// 获取回收站简历
		resumeGroup.GET("/trash", middleware.WithoutValidation(resumeController.GetDeletedResumes))

		// 根据简历ID获取简历详情
		resumeGroup.GET("/:resume_id", middleware.WithValidation(resumeController.GetResumeDetail))

		// 根据简历ID获取简历基本信息
		resumeGroup.GET("/:resume_id/basic", middleware.WithValidation(resumeController.GetResumeBasicInfo))

		// 保存简历详情
		resumeGroup.PUT("/:resume_id", middleware.WithValidation(resumeController.SaveResumeDetail))

		// 修改简历名称
		resumeGroup.PUT("/:resume_id/name", middleware.WithValidation(resumeController.UpdateResumeName))

		// 删除简历
		resumeGroup.DELETE("/:resume_id", middleware.WithValidation(resumeController.DeleteResume))

		// 物理删除简历
		resumeGroup.DELETE("/:resume_id/permanently", middleware.WithValidation(resumeController.PermanentlyDeleteResume))

		// 恢复简历
		resumeGroup.PUT("/:resume_id/restore", middleware.WithValidation(resumeController.RestoreResume))

		// 复制简历
		resumeGroup.POST("/:resume_id/copy", middleware.WithValidation(resumeController.CopyResume))

		// 应用草稿到简历
		resumeGroup.POST("/:resume_id/apply-draft", middleware.WithValidation(resumeController.ApplyDraft))

		// 记录在线用户
		resumeGroup.POST("/online", middleware.WithoutValidation(resumeController.RecordOnline))
	}

	// 需要会员权限的简历路由组（使用JWT认证中间件）
	memberResumeGroup := apiGroup.Group("/resumes")
	memberResumeGroup.Use(middleware.JWTAuthMiddleware(jwtService, userService, redisClient))
	{
		// 下载简历PDF（需要会员权限）
		memberResumeGroup.GET("/:resume_id/download", middleware.WithValidation(resumeController.DownloadResumePDF))

		// 邮件分享简历（需要会员权限）
		memberResumeGroup.POST("/:resume_id/share/email", middleware.WithValidation(resumeController.ShareResumeByEmail))
	}

	// 简历草稿路由组（使用游客中间件）
	resumeDraftGroup := apiGroup.Group("/resume-drafts")
	resumeDraftGroup.Use(middleware.GuestAuthMiddleware(jwtService, userService, userRepo, redisClient))
	{
		// 根据简历草稿ID获取简历草稿详情
		resumeDraftGroup.GET("/:draft_id", middleware.WithValidation(resumeController.GetResumeDraftDetail))
	}
}
