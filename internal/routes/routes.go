package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"resume-server/config"
	"resume-server/internal/controllers/admin"
	"resume-server/internal/controllers/api"
	"resume-server/internal/middleware"
	"resume-server/internal/pkg"
	"resume-server/internal/repository"
	adminRoutes "resume-server/internal/routes/admin"
	apiRoutes "resume-server/internal/routes/api"
	"resume-server/internal/services"
)

// InitRoutes 初始化所有路由
func InitRoutes(
	router *gin.Engine,
	cfg *config.Config,
	userController *api.UserController,
	authController *api.AuthController,
	wechatController *api.WechatController,
	commonController *api.CommonController,
	uploadController *api.UploadController,
	planController *api.MembershipPlanController,
	orderController *api.OrderController,
	openAPIController *api.OpenAPIController,
	categoryController *api.Category<PERSON><PERSON>roller,
	positionController *api.PositionController,
	resumeController *api.ResumeController,
	aiController *api.AIController,
	exampleController *api.ExampleController,
	templateController *api.TemplateController,
	targetPositionController *api.TargetPositionController,
	adminAuthController *admin.AuthController,
	adminController *admin.AdminController,
	channelController *admin.ChannelController,
	jwtService pkg.JWTService,
	adminJWTService pkg.AdminJWTService,
	adminService services.AdminService,
	userService services.UserService,
	userRepo repository.UserRepository,
	redisClient *redis.Client,
) {
	// 注册全局中间件
	router.Use(middleware.ErrorHandler())   // 错误处理中间件必须放在最前面
	router.Use(middleware.CORSMiddleware()) // 跨域中间件
	router.Use(gin.Recovery())
	router.Use(gin.Logger())

	// API路由组
	apiGroup := router.Group("/api")

	// 注册认证路由
	apiRoutes.RegisterAuthRoutes(apiGroup, authController, jwtService, userService, userRepo, redisClient)

	// 注册用户路由
	apiRoutes.RegisterUserRoutes(apiGroup, userController, authController, jwtService, userService, userRepo, redisClient)

	// 注册微信路由
	apiRoutes.RegisterWechatRoutes(apiGroup, wechatController)

	// 注册通用路由
	apiRoutes.RegisterCommonRoutes(apiGroup, commonController, jwtService, userService, redisClient)

	// 注册上传路由
	apiRoutes.RegisterUploadRoutes(apiGroup, uploadController, jwtService, userService, userRepo, redisClient)

	// 注册会员套餐路由（无需认证）
	apiRoutes.RegisterMembershipPlanRoutes(apiGroup, planController)

	// 注册订单路由
	apiRoutes.RegisterOrderRoutes(apiGroup, orderController, jwtService, userService, redisClient)

	// 注册OpenAPI路由
	apiRoutes.RegisterOpenAPIRoutes(apiGroup, openAPIController)

	// 注册分类路由（无需认证）
	apiRoutes.RegisterCategoryRoutes(apiGroup, categoryController)

	// 注册职位路由（无需认证）
	apiRoutes.RegisterPositionRoutes(apiGroup, positionController)

	// 注册简历路由（需要认证）
	apiRoutes.RegisterResumeRoutes(apiGroup, resumeController, jwtService, userService, userRepo, redisClient)

	// 注册AI路由（需要认证）
	apiRoutes.RegisterAIRoutes(apiGroup, aiController, jwtService, userService, userRepo, redisClient)

	// 注册示例路由（无需认证）
	apiRoutes.RegisterExampleRoutes(apiGroup, exampleController, jwtService, userService, userRepo, redisClient)

	// 注册模板路由
	apiRoutes.RegisterTemplateRoutes(apiGroup, templateController, jwtService, userService, userRepo, redisClient)

	// 注册目标岗位路由（需要认证）
	apiRoutes.RegisterTargetPositionRoutes(apiGroup, targetPositionController, jwtService, userService, userRepo, redisClient)

	// 管理员路由组
	adminGroup := router.Group("/admin")

	// 注册管理员认证路由
	adminRoutes.RegisterAdminAuthRoutes(adminGroup, adminAuthController)

	// 注册管理员路由
	adminRoutes.RegisterAdminRoutes(adminGroup, adminController, adminJWTService, adminService, redisClient)

	// 注册渠道路由
	adminRoutes.RegisterChannelRoutes(adminGroup, channelController, adminJWTService, adminService, redisClient)
}
