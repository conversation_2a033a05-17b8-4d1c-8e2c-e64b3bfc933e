package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"resume-server/config"
	"resume-server/internal/controllers/api"
	"resume-server/internal/controllers/admin"
	"resume-server/internal/pkg"
	"resume-server/internal/repository"
	"resume-server/internal/services"
)

// RouteInitializer 路由初始化接口
type RouteInitializer interface {
	// Initialize 初始化路由
	Initialize(engine *gin.Engine)
}

// Router 路由结构体
type Router struct {
	cfg                      *config.Config
	userController           *api.UserController
	authController           *api.AuthController
	wechatController         *api.WechatController
	commonController         *api.CommonController
	uploadController         *api.UploadController
	planController           *api.MembershipPlanController
	orderController          *api.OrderController
	openAPIController        *api.OpenAPIController
	categoryController       *api.CategoryController
	positionController       *api.PositionController
	resumeController         *api.ResumeController
	aiController             *api.AIController
	exampleController        *api.ExampleController
	templateController       *api.TemplateController
	targetPositionController *api.TargetPositionController
	adminAuthController      *admin.AuthController
	adminController          *admin.AdminController
	channelController        *admin.ChannelController
	jwtService               pkg.JWTService
	adminJWTService          pkg.AdminJWTService
	adminService             services.AdminService
	userService              services.UserService
	userRepo                 repository.UserRepository
	redisClient              *redis.Client
}

// NewRouter 创建路由结构体
func NewRouter(
	cfg *config.Config,
	userController *api.UserController,
	authController *api.AuthController,
	wechatController *api.WechatController,
	commonController *api.CommonController,
	uploadController *api.UploadController,
	planController *api.MembershipPlanController,
	orderController *api.OrderController,
	openAPIController *api.OpenAPIController,
	categoryController *api.CategoryController,
	positionController *api.PositionController,
	resumeController *api.ResumeController,
	aiController *api.AIController,
	exampleController *api.ExampleController,
	templateController *api.TemplateController,
	targetPositionController *api.TargetPositionController,
	adminAuthController *admin.AuthController,
	adminController *admin.AdminController,
	channelController *admin.ChannelController,
	jwtService pkg.JWTService,
	adminJWTService pkg.AdminJWTService,
	adminService services.AdminService,
	userService services.UserService,
	userRepo repository.UserRepository,
	redisClient *redis.Client,
) *Router {
	return &Router{
		cfg:                      cfg,
		userController:           userController,
		authController:           authController,
		wechatController:         wechatController,
		commonController:         commonController,
		uploadController:         uploadController,
		planController:           planController,
		orderController:          orderController,
		openAPIController:        openAPIController,
		categoryController:       categoryController,
		positionController:       positionController,
		resumeController:         resumeController,
		aiController:             aiController,
		exampleController:        exampleController,
		templateController:       templateController,
		targetPositionController: targetPositionController,
		adminAuthController:      adminAuthController,
		adminController:          adminController,
		channelController:        channelController,
		jwtService:               jwtService,
		adminJWTService:          adminJWTService,
		adminService:             adminService,
		userService:              userService,
		userRepo:                 userRepo,
		redisClient:              redisClient,
	}
}

// Initialize 实现RouteInitializer接口
func (r *Router) Initialize(engine *gin.Engine) {
	InitRoutes(engine, r.cfg, r.userController, r.authController, r.wechatController, r.commonController, r.uploadController, r.planController, r.orderController, r.openAPIController, r.categoryController, r.positionController, r.resumeController, r.aiController, r.exampleController, r.templateController, r.targetPositionController, r.adminAuthController, r.adminController, r.channelController, r.jwtService, r.adminJWTService, r.adminService, r.userService, r.userRepo, r.redisClient)
}
