package admin

import (
	"github.com/gin-gonic/gin"
	"resume-server/internal/controllers/admin"
	"resume-server/internal/middleware"
)

// RegisterAdminAuthRoutes 注册管理员认证路由
func RegisterAdminAuthRoutes(
	router *gin.RouterGroup,
	authController *admin.AuthController,
) {
	// 管理员认证路由组
	authGroup := router.Group("/auth")
	{
		// 登录（无需认证）
		authGroup.POST("/login", middleware.WithValidation(authController.Login))
	}
}
