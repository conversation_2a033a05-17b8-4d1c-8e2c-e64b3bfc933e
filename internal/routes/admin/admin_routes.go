package admin

import (
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"resume-server/internal/controllers/admin"
	"resume-server/internal/middleware"
	"resume-server/internal/pkg"
	"resume-server/internal/services"
)

// RegisterAdminRoutes 注册管理员路由
func RegisterAdminRoutes(
	router *gin.RouterGroup,
	adminController *admin.AdminController,
	adminJWTService pkg.AdminJWTService,
	adminService services.AdminService,
	redisClient *redis.Client,
) {
	// 需要认证的管理员路由组
	adminGroup := router.Group("/admin")
	adminGroup.Use(middleware.AdminAuthMiddleware(adminJWTService, adminService, redisClient))
	{
		// 获取当前管理员信息
		adminGroup.GET("/info", middleware.WithoutValidation(adminController.GetAdminInfo))

		// 退出登录
		adminGroup.POST("/logout", middleware.WithoutValidation(adminController.Logout))

		// 修改密码
		adminGroup.POST("/change-password", middleware.WithValidation(adminController.ChangePassword))
	}
}
