package api

import (
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"resume-server/internal/dto/dto_api"
	"resume-server/internal/response"
	"resume-server/internal/services"
)

// ExampleController 示例控制器
type ExampleController struct {
	exampleService              services.ExampleService
	membershipValidationService services.MembershipValidationService
	logger                      *zap.Logger
}

// NewExampleController 创建示例控制器
func NewExampleController(
	exampleService services.ExampleService,
	membershipValidationService services.MembershipValidationService,
	logger *zap.Logger,
) *ExampleController {
	return &ExampleController{
		exampleService:              exampleService,
		membershipValidationService: membershipValidationService,
		logger:                      logger,
	}
}

// GetExampleById 根据ID获取示例详情
// @Summary 根据ID获取示例详情
// @Description 根据示例ID获取示例的详细信息，包括简历内容和模板样式
// @Tags API/示例管理
// @Accept json
// @Produce json
// @Param id path int true "示例ID" example(1)
// @Success 200 {object} vo.SuccessAPIResponse{data=vo_api.ExampleDetailResponse} "获取成功"
// @Failure 404 {object} vo.ErrorAPIResponse "示例不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/examples/id/{id} [get]
func (c *ExampleController) GetExampleById(ctx *gin.Context, req *dto_api.GetExampleByIdRequest) {
	// 调用服务层获取示例详情
	example, err := c.exampleService.GetExampleById(ctx, req.ID)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "获取成功", example)
}

// GetExampleTdkById 根据ID获取示例TDK信息
// @Summary 根据ID获取示例TDK信息
// @Description 根据示例ID获取示例的TDK（标题、描述、关键词）信息
// @Tags API/示例管理
// @Accept json
// @Produce json
// @Param id path int true "示例ID" example(1)
// @Success 200 {object} vo.SuccessAPIResponse{data=vo_api.ExampleTdkResponse} "获取成功"
// @Failure 404 {object} vo.ErrorAPIResponse "示例不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/examples/tdk/{id} [get]
func (c *ExampleController) GetExampleTdkById(ctx *gin.Context, req *dto_api.GetExampleTdkByIdRequest) {
	// 调用服务层获取示例TDK信息
	tdk, err := c.exampleService.GetExampleTdkById(ctx, req.ID)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "获取成功", tdk)
}

// UseExample 使用例子创建简历
// @Summary 使用例子创建简历
// @Description 根据例子ID复制例子的内容创建新的简历
// @Tags API/示例管理
// @Accept json
// @Produce json
// @Param request body dto_api.UseExampleRequest true "使用例子请求"
// @Success 200 {object} vo.SuccessAPIResponse{data=vo_api.UseExampleResponse} "创建成功"
// @Failure 400 {object} vo.ErrorAPIResponse "请求参数错误"
// @Failure 404 {object} vo.ErrorAPIResponse "示例不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/examples/use [post]
func (c *ExampleController) UseExample(ctx *gin.Context, req *dto_api.UseExampleRequest) {
	// 从游客中间件中获取用户ID（中间件确保一定存在）
	userID := ctx.MustGet("userId").(uint)

	// 从请求头中获取JWT Token
	authHeader := ctx.GetHeader("Authorization")
	var jwtToken string
	if authHeader != "" {
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) == 2 && parts[0] == "Bearer" {
			jwtToken = parts[1]
		}
	}

	// 从请求头中获取浏览器指纹
	fingerprint := ctx.GetHeader("X-Fingerprint")

	// 调用服务层使用例子创建简历
	result, err := c.exampleService.UseExample(ctx, req.ExampleID, userID, jwtToken, fingerprint)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "创建成功", result)
}

// GetHomePageExamples 获取首页示例列表
// @Summary 获取首页示例列表
// @Description 随机获取16个示例用于首页展示，包含预览图、标签、名称等信息
// @Tags API/示例管理
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse{data=[]vo_api.ExampleListItemResponse} "获取成功"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/examples/homepage [get]
func (c *ExampleController) GetHomePageExamples(ctx *gin.Context) {
	// 调用服务层获取首页示例
	examples, err := c.exampleService.GetHomePageExamples(ctx)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "获取成功", examples)
}

// GetTemplateRecommendationsWithCache 获取模板推荐列表（带永久缓存）
// @Summary 获取模板推荐列表（带永久缓存）
// @Description 随机获取20个示例作为模板推荐，包含预览图、标签、名称等信息，支持永久缓存
// @Tags API/示例管理
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse{data=[]vo_api.ExampleListItemResponse} "获取成功"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/examples/recommendations [get]
func (c *ExampleController) GetTemplateRecommendationsWithCache(ctx *gin.Context) {
	// 调用服务层获取模板推荐
	recommendations, err := c.exampleService.GetTemplateRecommendationsWithCache(ctx)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "获取成功", recommendations)
}

// GetTemplateRecommendations 获取模板推荐列表（不带缓存）
// @Summary 获取模板推荐列表（不带缓存）
// @Description 随机获取20个示例作为模板推荐，包含预览图、标签、名称等信息，实时查询不使用缓存
// @Tags API/示例管理
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse{data=[]vo_api.ExampleListItemResponse} "获取成功"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/examples/recommendations/fresh [get]
func (c *ExampleController) GetTemplateRecommendations(ctx *gin.Context) {
	// 调用服务层获取模板推荐（不带缓存）
	recommendations, err := c.exampleService.GetTemplateRecommendations(ctx)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "获取成功", recommendations)
}
