package api

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"resume-server/internal/dto/dto_api"
	"resume-server/internal/response"
	"resume-server/internal/services"
)

// TemplateController 模板控制器
type TemplateController struct {
	templateService services.TemplateService
	logger          *zap.Logger
}

// NewTemplateController 创建模板控制器
func NewTemplateController(
	templateService services.TemplateService,
	logger *zap.Logger,
) *TemplateController {
	return &TemplateController{
		templateService: templateService,
		logger:          logger,
	}
}

// GetTemplateList 获取模板列表
// @Summary 获取模板列表
// @Description 分页获取模板列表，返回模板ID、名称、标签、预览图和使用人数
// @Tags API/模板管理
// @Accept json
// @Produce json
// @Param page query int false "页码，默认为1" example(1)
// @Param page_size query int false "每页条数，默认为20" example(20)
// @Success 200 {object} vo.SuccessAPIResponse{data=vo_api.TemplateListResponse} "获取成功"
// @Failure 400 {object} vo.ErrorAPIResponse "请求参数错误"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/templates [get]
func (c *TemplateController) GetTemplateList(ctx *gin.Context, req *dto_api.GetTemplateListRequest) {
	// 调用服务层获取模板列表
	templates, err := c.templateService.GetTemplateList(ctx, req.Page, req.PageSize)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "获取成功", templates)
}

// UseTemplate 使用模板
// @Summary 使用模板
// @Description 将指定模板应用到简历，更新简历的模板ID和样式配置
// @Tags API/模板管理
// @Accept json
// @Produce json
// @Param request body dto_api.UseTemplateRequest true "使用模板请求"
// @Success 200 {object} vo.SuccessAPIResponse "应用成功"
// @Failure 400 {object} vo.ErrorAPIResponse "请求参数错误"
// @Failure 403 {object} vo.ErrorAPIResponse "无权限修改此简历"
// @Failure 404 {object} vo.ErrorAPIResponse "模板不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/templates/use [post]
func (c *TemplateController) UseTemplate(ctx *gin.Context, req *dto_api.UseTemplateRequest) {
	// 从中间件中获取用户ID
	userID := ctx.MustGet("userId").(uint)

	// 调用服务层使用模板
	err := c.templateService.UseTemplate(ctx, req.TemplateID, req.ResumeID, userID)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessNoDataJSON(ctx, "模板应用成功")
}
