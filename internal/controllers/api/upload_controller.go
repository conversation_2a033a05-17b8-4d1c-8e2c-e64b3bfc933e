package api

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"resume-server/internal/dto/dto_api"
	"resume-server/internal/response"
	"resume-server/internal/services"
	"resume-server/internal/vo/vo_api"
)

// UploadController 上传控制器
type UploadController struct {
	uploadService services.UploadService
	logger        *zap.Logger
}

// NewUploadController 创建上传控制器
func NewUploadController(uploadService services.UploadService, logger *zap.Logger) *UploadController {
	return &UploadController{
		uploadService: uploadService,
		logger:        logger,
	}
}

// UploadAvatar 上传头像
// @Summary 上传头像
// @Description 上传用户头像，支持jpg、png、webp格式，最大7MB
// @Tags API/文件上传
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "头像文件"
// @Success 200 {object} vo.SuccessAPIResponse{data=vo_api.UploadResponse} "上传成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 413 {object} vo.ErrorAPIResponse "文件过大"
// @Failure 415 {object} vo.ErrorAPIResponse "不支持的文件类型"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/upload/avatar [post]
func (c *UploadController) UploadAvatar(ctx *gin.Context, req dto_api.UploadAvatarRequest) {
	// 获取文件
	file := req.File

	// 调用上传服务上传头像
	fileURL, originalFilename, err := c.uploadService.UploadAvatar(ctx, file)
	if err != nil {
		c.logger.Error("上传头像失败", zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	// 返回上传结果
	response.SuccessJSON(ctx, "上传成功", vo_api.UploadResponse{
		URL:      fileURL,
		Filename: originalFilename,
	})
}

// UploadAttachment 上传附件
// @Summary 上传附件
// @Description 上传附件，支持PDF、DOC、DOCX、JPG、PNG、ZIP、RAR等格式，最大20MB
// @Tags API/文件上传
// @Accept multipart/form-data
// @Produce json
// @Param file formData file true "附件文件"
// @Success 200 {object} vo.SuccessAPIResponse{data=vo_api.UploadResponse} "上传成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 413 {object} vo.ErrorAPIResponse "文件过大"
// @Failure 415 {object} vo.ErrorAPIResponse "不支持的文件类型"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /api/upload/attachment [post]
func (c *UploadController) UploadAttachment(ctx *gin.Context, req dto_api.UploadAttachmentRequest) {
	// 获取文件
	file := req.File

	// 调用上传服务上传附件
	fileURL, originalFilename, err := c.uploadService.UploadAttachment(ctx, file)
	if err != nil {
		c.logger.Error("上传附件失败", zap.Error(err))
		response.ThrowError(ctx, err)
		return
	}

	// 返回上传结果
	response.SuccessJSON(ctx, "上传成功", vo_api.UploadResponse{
		URL:      fileURL,
		Filename: originalFilename,
	})
}
