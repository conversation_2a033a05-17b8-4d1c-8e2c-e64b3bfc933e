package admin

import (
	"github.com/gin-gonic/gin"
	"resume-server/internal/dto/dto_admin"
	"resume-server/internal/response"
	"resume-server/internal/services"
)

// AuthController 管理员认证控制器
type AuthController struct {
	adminService services.AdminService
}

// NewAuthController 创建管理员认证控制器
func NewAuthController(
	adminService services.AdminService,
) *AuthController {
	return &AuthController{
		adminService: adminService,
	}
}

// Login 管理员登录
// @Summary 管理员登录
// @Description 管理员使用用户名和密码登录并获取令牌
// @Tags Admin/认证管理
// @Accept json
// @Produce json
// @Param request body dto_admin.AdminLoginRequest true "登录信息"
// @Success 200 {object} vo.SuccessAPIResponse{data=vo_admin.AdminTokenResponse} "登录成功，返回token"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 401 {object} vo.ErrorAPIResponse "用户名或密码错误"
// @Failure 403 {object} vo.ErrorAPIResponse "账号已被禁用"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Router /admin/auth/login [post]
func (c *AuthController) Login(ctx *gin.Context, req dto_admin.AdminLoginRequest) {
	// 调用服务层进行登录
	loginResponse, err := c.adminService.LoginWithToken(req.Username, req.Password)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "登录成功", loginResponse)
}
