package admin

import (
	"github.com/gin-gonic/gin"
	"resume-server/internal/dto/dto_admin"
	"resume-server/internal/exception"
	"resume-server/internal/models"
	"resume-server/internal/response"
	"resume-server/internal/services"
	"resume-server/internal/vo/vo_admin"
	"strings"
)

// AdminController 管理员控制器
type AdminController struct {
	adminService services.AdminService
}

// NewAdminController 创建管理员控制器
func NewAdminController(
	adminService services.AdminService,
) *AdminController {
	return &AdminController{
		adminService: adminService,
	}
}

// GetAdminInfo 获取当前管理员信息
// @Summary 获取当前管理员信息
// @Description 获取当前登录管理员的详细信息
// @Tags Admin/管理员管理
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse{data=vo_admin.AdminInfoResponse} "获取成功"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 404 {object} vo.ErrorAPIResponse "管理员不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security AdminAuth
// @Router /admin/admin/info [get]
func (c *AdminController) GetAdminInfo(ctx *gin.Context) {
	// 从中间件中获取管理员信息
	admin := ctx.MustGet("admin").(*models.Admin)

	// 构建响应数据
	adminInfo := vo_admin.AdminInfoResponse{
		ID:           admin.ID,
		Username:     admin.Username,
		Channels:     admin.Channels,
		IsSuperAdmin: admin.IsSuperAdmin,
		CreatedAt:    admin.CreatedAt,
	}

	response.SuccessJSON(ctx, "获取管理员信息成功", adminInfo)
}

// Logout 管理员退出登录
// @Summary 管理员退出登录
// @Description 管理员退出登录，清除服务端token缓存
// @Tags Admin/管理员管理
// @Accept json
// @Produce json
// @Success 200 {object} vo.SuccessAPIResponse{data=map[string]string} "退出成功"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security AdminAuth
// @Router /admin/admin/logout [post]
func (c *AdminController) Logout(ctx *gin.Context) {
	// 从中间件中获取管理员信息
	admin := ctx.MustGet("admin").(*models.Admin)

	// 从请求头中获取token
	authHeader := ctx.GetHeader("Authorization")
	if authHeader == "" {
		response.ThrowError(ctx, exception.ErrUnauthorized.WithMessage("缺少认证token"))
		return
	}

	// 提取token（去掉"Bearer "前缀）
	token := strings.TrimPrefix(authHeader, "Bearer ")
	if token == authHeader {
		response.ThrowError(ctx, exception.ErrTokenInvalid.WithMessage("无效的token格式"))
		return
	}

	// 调用服务层处理退出登录逻辑，将token加入黑名单
	err := c.adminService.LogoutWithToken(admin.ID, token)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "退出登录成功", map[string]string{
		"message": "已成功退出登录",
	})
}

// ChangePassword 修改管理员密码
// @Summary 修改管理员密码
// @Description 管理员修改自己的密码，需要提供旧密码和新密码
// @Tags Admin/管理员管理
// @Accept json
// @Produce json
// @Param request body dto_admin.AdminChangePasswordRequest true "修改密码信息"
// @Success 200 {object} vo.SuccessAPIResponse{data=map[string]string} "修改成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 401 {object} vo.ErrorAPIResponse "旧密码错误"
// @Failure 404 {object} vo.ErrorAPIResponse "管理员不存在"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security AdminAuth
// @Router /admin/admin/change-password [post]
func (c *AdminController) ChangePassword(ctx *gin.Context, req dto_admin.AdminChangePasswordRequest) {
	// 从中间件中获取管理员信息
	admin := ctx.MustGet("admin").(*models.Admin)

	// 调用服务层修改密码
	err := c.adminService.ChangePassword(admin.ID, req.OldPassword, req.NewPassword)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "密码修改成功", map[string]string{
		"message": "密码已成功修改",
	})
}
