package admin

import (
	"github.com/gin-gonic/gin"
	"resume-server/internal/dto/dto_admin"
	"resume-server/internal/models"
	"resume-server/internal/response"
	"resume-server/internal/services"
)

// ChannelController 渠道控制器
type ChannelController struct {
	channelService services.ChannelService
}

// NewChannelController 创建渠道控制器
func NewChannelController(
	channelService services.ChannelService,
) *ChannelController {
	return &ChannelController{
		channelService: channelService,
	}
}

// GetChannelUsers 获取渠道用户列表
// @Summary 获取渠道用户列表
// @Description 获取渠道用户列表，支持分页、时间范围查询和渠道号查询，非超级管理员只能查看有权限的渠道
// @Tags Admin/渠道管理
// @Accept json
// @Produce json
// @Param page query int false "页码" example(1)
// @Param page_size query int false "每页条数" example(10)
// @Param channel query string false "渠道号" example("web")
// @Param start_time query string false "开始时间" example("2024-01-01T00:00:00Z")
// @Param end_time query string false "结束时间" example("2024-12-31T23:59:59Z")
// @Success 200 {object} vo.SuccessAPIResponse{data=vo_admin.ChannelUsersListResponse} "获取成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 403 {object} vo.ErrorAPIResponse "权限不足"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security AdminAuth
// @Router /admin/channel/users [get]
func (c *ChannelController) GetChannelUsers(ctx *gin.Context, req dto_admin.GetChannelUsersRequest) {
	// 从中间件中获取管理员信息
	admin := ctx.MustGet("admin").(*models.Admin)
	
	// 调用服务层获取渠道用户列表
	result, err := c.channelService.GetChannelUsers(admin.Channels, admin.IsSuperAdmin, &req)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "获取渠道用户列表成功", result)
}

// GetChannelPayments 获取渠道付费列表
// @Summary 获取渠道付费列表
// @Description 获取渠道付费列表，支持分页、时间范围查询和渠道号查询，返回合计付费金额，非超级管理员只能查看有权限的渠道
// @Tags Admin/渠道管理
// @Accept json
// @Produce json
// @Param page query int false "页码" example(1)
// @Param page_size query int false "每页条数" example(10)
// @Param channel query string false "渠道号" example("web")
// @Param start_time query string false "开始时间" example("2024-01-01T00:00:00Z")
// @Param end_time query string false "结束时间" example("2024-12-31T23:59:59Z")
// @Success 200 {object} vo.SuccessAPIResponse{data=vo_admin.ChannelPaymentsListResponse} "获取成功"
// @Failure 400 {object} vo.ErrorAPIResponse "参数错误"
// @Failure 401 {object} vo.ErrorAPIResponse "未授权"
// @Failure 403 {object} vo.ErrorAPIResponse "权限不足"
// @Failure 500 {object} vo.ErrorAPIResponse "服务器内部错误"
// @Security AdminAuth
// @Router /admin/channel/payments [get]
func (c *ChannelController) GetChannelPayments(ctx *gin.Context, req dto_admin.GetChannelPaymentsRequest) {
	// 从中间件中获取管理员信息
	admin := ctx.MustGet("admin").(*models.Admin)

	// 调用服务层获取渠道付费列表
	result, err := c.channelService.GetChannelPayments(admin.Channels, admin.IsSuperAdmin, &req)
	if err != nil {
		response.ThrowError(ctx, err)
		return
	}

	response.SuccessJSON(ctx, "获取渠道付费列表成功", result)
}
