package repository

import (
	"context"

	"gorm.io/gorm"
	"resume-server/internal/models"
)

// ResumeDraftRepository 简历草稿仓库接口
type ResumeDraftRepository interface {
	// Create 创建简历草稿
	Create(ctx context.Context, draft *models.ResumeDraft) error

	// GetByID 根据ID获取简历草稿
	GetByID(ctx context.Context, id uint) (*models.ResumeDraft, error)

	// GetByUserID 根据用户ID获取简历草稿列表
	GetByUserID(ctx context.Context, userID uint) ([]*models.ResumeDraft, error)

	// Update 更新简历草稿
	Update(ctx context.Context, draft *models.ResumeDraft) error

	// Delete 删除简历草稿
	Delete(ctx context.Context, id uint) error
}

// resumeDraftRepository 简历草稿仓库实现
type resumeDraftRepository struct {
	db *gorm.DB
}

// NewResumeDraftRepository 创建简历草稿仓库
func NewResumeDraftRepository(db *gorm.DB) ResumeDraftRepository {
	return &resumeDraftRepository{
		db: db,
	}
}

// Create 创建简历草稿
func (r *resumeDraftRepository) Create(ctx context.Context, draft *models.ResumeDraft) error {
	return r.db.WithContext(ctx).Create(draft).Error
}

// GetByID 根据ID获取简历草稿
func (r *resumeDraftRepository) GetByID(ctx context.Context, id uint) (*models.ResumeDraft, error) {
	var draft models.ResumeDraft
	if err := r.db.WithContext(ctx).First(&draft, id).Error; err != nil {
		return nil, err
	}
	return &draft, nil
}

// GetByUserID 根据用户ID获取简历草稿列表
func (r *resumeDraftRepository) GetByUserID(ctx context.Context, userID uint) ([]*models.ResumeDraft, error) {
	var drafts []*models.ResumeDraft
	err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("updated_at DESC").
		Find(&drafts).Error
	if err != nil {
		return nil, err
	}
	return drafts, nil
}

// Update 更新简历草稿
func (r *resumeDraftRepository) Update(ctx context.Context, draft *models.ResumeDraft) error {
	return r.db.WithContext(ctx).Save(draft).Error
}

// Delete 删除简历草稿
func (r *resumeDraftRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.ResumeDraft{}, id).Error
}
