package repository

import (
	"context"

	"gorm.io/gorm"
	"resume-server/internal/enum"
	"resume-server/internal/models"
)

// MembershipPlanRepository 会员套餐仓库接口
type MembershipPlanRepository interface {
	// GetVisiblePlans 获取所有可见的会员套餐
	GetVisiblePlans(ctx context.Context) ([]*models.MembershipPlan, error)

	// GetVisibleDownloadCouponPlans 获取所有可见的下载券套餐
	GetVisibleDownloadCouponPlans(ctx context.Context) ([]*models.MembershipPlan, error)

	// GetPlanByID 根据ID获取会员套餐
	GetPlanByID(ctx context.Context, id uint) (*models.MembershipPlan, error)
}

// membershipPlanRepository 会员套餐仓库实现
type membershipPlanRepository struct {
	db *gorm.DB
}

// NewMembershipPlanRepository 创建会员套餐仓库
func NewMembershipPlanRepository(db *gorm.DB) MembershipPlanRepository {
	return &membershipPlanRepository{
		db: db,
	}
}

// GetVisiblePlans 获取所有可见的会员套餐
func (r *membershipPlanRepository) GetVisiblePlans(ctx context.Context) ([]*models.MembershipPlan, error) {

	var plans []*models.MembershipPlan

	// 查询所有可见的会员套餐，只查询plan_type为会员类型的套餐，按照排序字段降序排列（值越大越靠前）
	err := r.db.WithContext(ctx).
		Where("visibility = ? AND plan_type = ?", enum.VisibilityStatusVisible, enum.PlanTypeMembership).
		Order("sort DESC").
		Find(&plans).Error

	if err != nil {
		return nil, err
	}

	return plans, nil
}

// GetVisibleDownloadCouponPlans 获取所有可见的下载券套餐
func (r *membershipPlanRepository) GetVisibleDownloadCouponPlans(ctx context.Context) ([]*models.MembershipPlan, error) {
	var plans []*models.MembershipPlan

	// 查询所有可见的下载券套餐，只查询plan_type为下载券类型的套餐，按照排序字段降序排列（值越大越靠前）
	err := r.db.WithContext(ctx).
		Where("visibility = ? AND plan_type = ?", enum.VisibilityStatusVisible, enum.PlanTypeDownloadCoupon).
		Order("sort DESC").
		Find(&plans).Error

	if err != nil {
		return nil, err
	}

	return plans, nil
}

// GetPlanByID 根据ID获取会员套餐
func (r *membershipPlanRepository) GetPlanByID(ctx context.Context, id uint) (*models.MembershipPlan, error) {
	var plan models.MembershipPlan

	err := r.db.WithContext(ctx).First(&plan, id).Error
	if err != nil {
		return nil, err
	}

	return &plan, nil
}
