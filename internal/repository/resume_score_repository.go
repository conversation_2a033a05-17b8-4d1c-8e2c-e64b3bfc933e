package repository

import (
	"context"

	"gorm.io/gorm"
	"resume-server/internal/models"
)

// ResumeScoreRepository 简历评分仓库接口
type ResumeScoreRepository interface {
	// Create 创建简历评分记录
	Create(ctx context.Context, resumeScore *models.ResumeScore) error
	// GetByID 根据ID获取简历评分记录
	GetByID(ctx context.Context, id uint) (*models.ResumeScore, error)
	// GetByResumeIDAndPositionID 根据简历ID和目标岗位ID获取评分记录
	GetByResumeIDAndPositionID(ctx context.Context, resumeID uint, targetPositionID uint) (*models.ResumeScore, error)
	// GetByUserID 根据用户ID获取评分记录列表
	GetByUserID(ctx context.Context, userID uint, limit int, offset int) ([]*models.ResumeScore, error)
	// GetByResumeID 根据简历ID获取评分记录列表
	GetByResumeID(ctx context.Context, resumeID uint) ([]*models.ResumeScore, error)
	// Update 更新简历评分记录
	Update(ctx context.Context, resumeScore *models.ResumeScore) error
	// Delete 删除简历评分记录
	Delete(ctx context.Context, id uint) error
	// CheckOwnership 检查评分记录是否属于指定用户
	CheckOwnership(ctx context.Context, id uint, userID uint) (bool, error)
}

// resumeScoreRepository 简历评分仓库实现
type resumeScoreRepository struct {
	db *gorm.DB
}

// NewResumeScoreRepository 创建简历评分仓库
func NewResumeScoreRepository(db *gorm.DB) ResumeScoreRepository {
	return &resumeScoreRepository{
		db: db,
	}
}

// Create 创建简历评分记录
func (r *resumeScoreRepository) Create(ctx context.Context, resumeScore *models.ResumeScore) error {
	return r.db.WithContext(ctx).Create(resumeScore).Error
}

// GetByID 根据ID获取简历评分记录
func (r *resumeScoreRepository) GetByID(ctx context.Context, id uint) (*models.ResumeScore, error) {
	var resumeScore models.ResumeScore
	err := r.db.WithContext(ctx).First(&resumeScore, id).Error
	if err != nil {
		return nil, err
	}
	return &resumeScore, nil
}

// GetByResumeIDAndPositionID 根据简历ID和目标岗位ID获取评分记录
func (r *resumeScoreRepository) GetByResumeIDAndPositionID(ctx context.Context, resumeID uint, targetPositionID uint) (*models.ResumeScore, error) {
	var resumeScore models.ResumeScore
	err := r.db.WithContext(ctx).
		Where("resume_id = ? AND target_position_id = ?", resumeID, targetPositionID).
		Order("created_at DESC").
		First(&resumeScore).Error
	if err != nil {
		return nil, err
	}
	return &resumeScore, nil
}

// GetByUserID 根据用户ID获取评分记录列表
func (r *resumeScoreRepository) GetByUserID(ctx context.Context, userID uint, limit int, offset int) ([]*models.ResumeScore, error) {
	var resumeScores []*models.ResumeScore
	err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&resumeScores).Error
	if err != nil {
		return nil, err
	}
	return resumeScores, nil
}

// GetByResumeID 根据简历ID获取评分记录列表
func (r *resumeScoreRepository) GetByResumeID(ctx context.Context, resumeID uint) ([]*models.ResumeScore, error) {
	var resumeScores []*models.ResumeScore
	err := r.db.WithContext(ctx).
		Where("resume_id = ?", resumeID).
		Order("created_at DESC").
		Find(&resumeScores).Error
	if err != nil {
		return nil, err
	}
	return resumeScores, nil
}

// Update 更新简历评分记录
func (r *resumeScoreRepository) Update(ctx context.Context, resumeScore *models.ResumeScore) error {
	return r.db.WithContext(ctx).Save(resumeScore).Error
}

// Delete 删除简历评分记录
func (r *resumeScoreRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.ResumeScore{}, id).Error
}

// CheckOwnership 检查评分记录是否属于指定用户
func (r *resumeScoreRepository) CheckOwnership(ctx context.Context, id uint, userID uint) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.ResumeScore{}).
		Where("id = ? AND user_id = ?", id, userID).
		Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}
