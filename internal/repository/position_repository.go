package repository

import (
	"context"
	"fmt"

	"gorm.io/gorm"
	"resume-server/internal/models"
)

// PositionRepository 职位仓库接口
type PositionRepository interface {
	// GetAllPositions 获取所有职位数据，按排序字段排序
	GetAllPositions(ctx context.Context) ([]*models.Position, error)
	// GetPositionsByParentID 根据父级ID获取所有子分类数据（包括子子分类）
	GetPositionsByParentID(ctx context.Context, parentID uint) ([]*models.Position, error)
	// GetRandomPositionsByLevel 根据level随机获取指定数量的职位
	GetRandomPositionsByLevel(ctx context.Context, level int, limit int) ([]*models.Position, error)
	// GetPositionsByLevels 根据level数组获取职位数据，按排序字段排序
	GetPositionsByLevels(ctx context.Context, levels []int) ([]*models.Position, error)
	// GetByID 根据ID获取职位信息
	GetByID(ctx context.Context, id uint) (*models.Position, error)
}

// positionRepository 职位仓库实现
type positionRepository struct {
	db *gorm.DB
}

// NewPositionRepository 创建职位仓库
func NewPositionRepository(db *gorm.DB) PositionRepository {
	return &positionRepository{
		db: db,
	}
}

// GetAllPositions 获取所有职位数据，按排序字段排序
func (r *positionRepository) GetAllPositions(ctx context.Context) ([]*models.Position, error) {
	var positions []*models.Position

	// 按排序字段升序排列（数值越小越靠前）
	err := r.db.WithContext(ctx).
		Order("sort ASC, id ASC").
		Find(&positions).Error

	if err != nil {
		return nil, err
	}

	return positions, nil
}

// GetPositionsByParentID 根据父级ID获取所有子分类数据（包括子子分类）
func (r *positionRepository) GetPositionsByParentID(ctx context.Context, parentID uint) ([]*models.Position, error) {
	var positions []*models.Position

	// 使用path字段查询所有子分类
	// path字段存储的是逗号分隔的层级路径，例如："1,2,3"
	// 我们需要查询path字段包含指定parentID的所有记录
	err := r.db.WithContext(ctx).
		Where("path LIKE ? OR path LIKE ? OR parent_id = ?",
			fmt.Sprintf("%d,%%", parentID), fmt.Sprintf("%%,%d,%%", parentID), parentID).
		Order("sort ASC, id ASC").
		Find(&positions).Error

	if err != nil {
		return nil, err
	}

	return positions, nil
}

// GetRandomPositionsByLevel 根据level随机获取指定数量的职位
func (r *positionRepository) GetRandomPositionsByLevel(ctx context.Context, level int, limit int) ([]*models.Position, error) {
	var positions []*models.Position

	// 随机查询指定level的职位，限制数量
	err := r.db.WithContext(ctx).
		Where("level = ?", level).
		Order("RAND()").
		Limit(limit).
		Find(&positions).Error

	if err != nil {
		return nil, err
	}

	return positions, nil
}

// GetPositionsByLevels 根据level数组获取职位数据，按排序字段排序
func (r *positionRepository) GetPositionsByLevels(ctx context.Context, levels []int) ([]*models.Position, error) {
	var positions []*models.Position

	// 按排序字段升序排列（数值越小越靠前），只获取指定level的职位
	err := r.db.WithContext(ctx).
		Where("level IN (?)", levels).
		Order("sort ASC, id ASC").
		Find(&positions).Error

	if err != nil {
		return nil, err
	}

	return positions, nil
}

// GetByID 根据ID获取职位信息
func (r *positionRepository) GetByID(ctx context.Context, id uint) (*models.Position, error) {
	var position models.Position
	err := r.db.WithContext(ctx).First(&position, id).Error
	if err != nil {
		return nil, err
	}
	return &position, nil
}
