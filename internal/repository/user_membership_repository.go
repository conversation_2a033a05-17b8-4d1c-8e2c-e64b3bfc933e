package repository

import (
	"context"
	"time"

	"gorm.io/gorm"
	"resume-server/internal/enum"
	"resume-server/internal/models"
)

// UserMembershipRepository 用户会员关系仓库接口
type UserMembershipRepository interface {
	// Create 创建用户会员关系
	Create(ctx context.Context, membership *models.UserMembership) error

	// GetByUserID 根据用户ID获取用户会员关系
	GetByUserID(ctx context.Context, userID uint) (*models.UserMembership, error)

	// GetActiveByUserID 根据用户ID获取生效中的会员关系
	GetActiveByUserID(ctx context.Context, userID uint) (*models.UserMembership, error)

	// Update 更新用户会员关系
	Update(ctx context.Context, membership *models.UserMembership) error

	// CreateOrUpdate 创建或更新用户会员关系
	CreateOrUpdate(ctx context.Context, userID, planID uint, startTime, endTime time.Time) error

	// ExpireUserMembership 使用户会员关系过期
	ExpireUserMembership(ctx context.Context, userID uint) error

	// GetExpiredActiveMemberships 获取已过期但状态仍为生效中的会员关系
	GetExpiredActiveMemberships(ctx context.Context) ([]*models.UserMembership, error)

	// BatchUpdateMembershipStatus 批量更新会员状态
	BatchUpdateMembershipStatus(ctx context.Context, membershipIDs []uint, status enum.MembershipStatus) error
}

// userMembershipRepository 用户会员关系仓库实现
type userMembershipRepository struct {
	db *gorm.DB
}

// NewUserMembershipRepository 创建用户会员关系仓库
func NewUserMembershipRepository(db *gorm.DB) UserMembershipRepository {
	return &userMembershipRepository{
		db: db,
	}
}

// Create 创建用户会员关系
func (r *userMembershipRepository) Create(ctx context.Context, membership *models.UserMembership) error {
	return r.db.WithContext(ctx).Create(membership).Error
}

// GetByUserID 根据用户ID获取用户会员关系
func (r *userMembershipRepository) GetByUserID(ctx context.Context, userID uint) (*models.UserMembership, error) {
	var membership models.UserMembership
	err := r.db.WithContext(ctx).Where("user_id = ?", userID).Order("created_at DESC").First(&membership).Error
	if err != nil {
		return nil, err
	}
	return &membership, nil
}

// GetActiveByUserID 根据用户ID获取生效中的会员关系
func (r *userMembershipRepository) GetActiveByUserID(ctx context.Context, userID uint) (*models.UserMembership, error) {
	var membership models.UserMembership
	now := time.Now()
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND membership_status = ? AND end_time > ?",
			userID, enum.MembershipStatusActive, now).
		Order("end_time DESC").
		First(&membership).Error
	if err != nil {
		return nil, err
	}
	return &membership, nil
}

// Update 更新用户会员关系
func (r *userMembershipRepository) Update(ctx context.Context, membership *models.UserMembership) error {
	return r.db.WithContext(ctx).Save(membership).Error
}

// CreateOrUpdate 创建或更新用户会员关系
func (r *userMembershipRepository) CreateOrUpdate(ctx context.Context, userID, planID uint, startTime, endTime time.Time) error {
	// 开启事务
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 1. 先查询用户是否已有生效中的会员关系
		var existingMembership models.UserMembership
		now := time.Now()

		err := tx.Where("user_id = ? AND membership_status = ? AND end_time > ?",
			userID, enum.MembershipStatusActive, now).
			Order("end_time DESC").
			First(&existingMembership).Error

		if err != nil && err != gorm.ErrRecordNotFound {
			return err
		}

		if err == gorm.ErrRecordNotFound {
			// 2. 没有生效中的会员关系，直接创建新的
			newMembership := &models.UserMembership{
				UserID:           userID,
				MembershipPlanID: planID,
				StartTime:        startTime,
				EndTime:          endTime,
				MembershipStatus: enum.MembershipStatusActive,
			}
			return tx.Create(newMembership).Error
		} else {
			// 3. 已有生效中的会员关系，需要延长时间
			// 如果新的结束时间比现有的结束时间晚，则延长
			if endTime.After(existingMembership.EndTime) {
				existingMembership.EndTime = endTime
				existingMembership.MembershipPlanID = planID // 更新为新的套餐
				return tx.Save(&existingMembership).Error
			}
			// 如果新的结束时间不比现有的晚，则不做任何操作
			return nil
		}
	})
}

// ExpireUserMembership 使用户会员关系过期
func (r *userMembershipRepository) ExpireUserMembership(ctx context.Context, userID uint) error {
	return r.db.WithContext(ctx).
		Model(&models.UserMembership{}).
		Where("user_id = ? AND membership_status = ?", userID, enum.MembershipStatusActive).
		Update("membership_status", enum.MembershipStatusExpired).Error
}

// GetExpiredActiveMemberships 获取已过期但状态仍为生效中的会员关系
func (r *userMembershipRepository) GetExpiredActiveMemberships(ctx context.Context) ([]*models.UserMembership, error) {
	var memberships []*models.UserMembership
	now := time.Now()

	err := r.db.WithContext(ctx).
		Where("membership_status = ? AND end_time < ?", enum.MembershipStatusActive, now).
		Find(&memberships).Error

	if err != nil {
		return nil, err
	}

	return memberships, nil
}

// BatchUpdateMembershipStatus 批量更新会员状态
func (r *userMembershipRepository) BatchUpdateMembershipStatus(ctx context.Context, membershipIDs []uint, status enum.MembershipStatus) error {
	if len(membershipIDs) == 0 {
		return nil
	}

	updates := map[string]interface{}{
		"membership_status": status,
		"updated_at":        time.Now(),
	}

	return r.db.WithContext(ctx).
		Model(&models.UserMembership{}).
		Where("id IN ?", membershipIDs).
		Updates(updates).Error
}
