package repository

import (
	"resume-server/internal/models"
	"gorm.io/gorm"
)

// AdminRepository 管理员仓库接口
type AdminRepository interface {
	Create(admin *models.Admin) error
	GetByID(id uint) (*models.Admin, error)
	GetByUsername(username string) (*models.Admin, error)
	Update(admin *models.Admin) error
	Delete(id uint) error
	List(page, pageSize int) ([]*models.Admin, int64, error)
}

// adminRepository 管理员仓库实现
type adminRepository struct {
	db *gorm.DB
}

// NewAdminRepository 创建管理员仓库
func NewAdminRepository(db *gorm.DB) AdminRepository {
	return &adminRepository{
		db: db,
	}
}

// Create 创建管理员
func (r *adminRepository) Create(admin *models.Admin) error {
	return r.db.Create(admin).Error
}

// GetByID 根据ID获取管理员
func (r *adminRepository) GetByID(id uint) (*models.Admin, error) {
	var admin models.Admin
	err := r.db.Where("id = ?", id).First(&admin).Error
	if err != nil {
		return nil, err
	}
	return &admin, nil
}

// GetByUsername 根据用户名获取管理员
func (r *adminRepository) GetByUsername(username string) (*models.Admin, error) {
	var admin models.Admin
	err := r.db.Where("username = ?", username).First(&admin).Error
	if err != nil {
		return nil, err
	}
	return &admin, nil
}

// Update 更新管理员
func (r *adminRepository) Update(admin *models.Admin) error {
	return r.db.Save(admin).Error
}

// Delete 删除管理员
func (r *adminRepository) Delete(id uint) error {
	return r.db.Delete(&models.Admin{}, id).Error
}

// List 获取管理员列表
func (r *adminRepository) List(page, pageSize int) ([]*models.Admin, int64, error) {
	var admins []*models.Admin
	var total int64
	
	// 计算总数
	if err := r.db.Model(&models.Admin{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// 分页查询
	offset := (page - 1) * pageSize
	err := r.db.Offset(offset).Limit(pageSize).Find(&admins).Error
	if err != nil {
		return nil, 0, err
	}
	
	return admins, total, nil
}
