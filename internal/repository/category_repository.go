package repository

import (
	"context"

	"gorm.io/gorm"
	"resume-server/internal/models"
)

// CategoryRepository 分类仓库接口
type CategoryRepository interface {
	// GetAllCategories 获取所有分类数据，按排序字段排序
	GetAllCategories(ctx context.Context) ([]*models.Category, error)
	// GetVisibleCategories 获取所有可见的分类数据，按排序字段排序
	GetVisibleCategories(ctx context.Context) ([]*models.Category, error)
}

// categoryRepository 分类仓库实现
type categoryRepository struct {
	db *gorm.DB
}

// NewCategoryRepository 创建分类仓库
func NewCategoryRepository(db *gorm.DB) CategoryRepository {
	return &categoryRepository{
		db: db,
	}
}

// GetAllCategories 获取所有分类数据，按排序字段排序
func (r *categoryRepository) GetAllCategories(ctx context.Context) ([]*models.Category, error) {
	var categories []*models.Category

	// 按分类类型和排序字段排序
	err := r.db.WithContext(ctx).
		Order("category_type ASC, sort ASC").
		Find(&categories).Error

	if err != nil {
		return nil, err
	}

	return categories, nil
}

// GetVisibleCategories 获取所有可见的分类数据，按排序字段排序
func (r *categoryRepository) GetVisibleCategories(ctx context.Context) ([]*models.Category, error) {
	var categories []*models.Category

	// 只查询is_show为true的分类，按分类类型和排序字段排序
	err := r.db.WithContext(ctx).
		Where("is_show = ?", true).
		Order("category_type ASC, sort ASC").
		Find(&categories).Error

	if err != nil {
		return nil, err
	}

	return categories, nil
}
