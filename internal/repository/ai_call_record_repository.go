package repository

import (
	"context"
	"time"

	"gorm.io/gorm"
	"resume-server/internal/enum"
	"resume-server/internal/models"
)

// AICallRecordRepository AI调用记录仓储接口
type AICallRecordRepository interface {
	// Create 创建AI调用记录
	Create(ctx context.Context, record *models.AICallRecord) error
	// UpdateResponse 更新AI调用记录的响应数据和调用时长
	UpdateResponse(ctx context.Context, id uint, responseData string, callDuration int64) error
	// GetByID 根据ID获取AI调用记录
	GetByID(ctx context.Context, id uint) (*models.AICallRecord, error)
	// GetByUserID 根据用户ID获取AI调用记录列表
	GetByUserID(ctx context.Context, userID uint, limit, offset int) ([]*models.AICallRecord, error)
	// GetByResumeID 根据简历ID获取AI调用记录列表
	GetByResumeID(ctx context.Context, resumeID uint, limit, offset int) ([]*models.AICallRecord, error)
	// GetByUserIDPaginated 根据用户ID分页获取AI调用记录列表
	GetByUserIDPaginated(ctx context.Context, userID uint, page, pageSize int) ([]*models.AICallRecord, int64, error)
	// GetByResumeIDPaginated 根据简历ID分页获取AI调用记录列表
	GetByResumeIDPaginated(ctx context.Context, resumeID uint, page, pageSize int) ([]*models.AICallRecord, int64, error)
	// CountByUserIDAndPromptTypes 根据用户ID和提示词类型统计调用次数（在指定时间范围内）
	CountByUserIDAndPromptTypes(ctx context.Context, userID uint, promptTypes []enum.PromptType, startTime, endTime *time.Time) (int, error)
}

// aiCallRecordRepository AI调用记录仓储实现
type aiCallRecordRepository struct {
	db *gorm.DB
}

// NewAICallRecordRepository 创建AI调用记录仓储
func NewAICallRecordRepository(db *gorm.DB) AICallRecordRepository {
	return &aiCallRecordRepository{
		db: db,
	}
}

// Create 创建AI调用记录
func (r *aiCallRecordRepository) Create(ctx context.Context, record *models.AICallRecord) error {
	return r.db.WithContext(ctx).Create(record).Error
}

// UpdateResponse 更新AI调用记录的响应数据和调用时长
func (r *aiCallRecordRepository) UpdateResponse(ctx context.Context, id uint, responseData string, callDuration int64) error {
	return r.db.WithContext(ctx).Model(&models.AICallRecord{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"response_data": responseData,
			"call_duration": callDuration,
		}).Error
}

// GetByID 根据ID获取AI调用记录
func (r *aiCallRecordRepository) GetByID(ctx context.Context, id uint) (*models.AICallRecord, error) {
	var record models.AICallRecord
	err := r.db.WithContext(ctx).First(&record, id).Error
	if err != nil {
		return nil, err
	}
	return &record, nil
}

// GetByUserID 根据用户ID获取AI调用记录列表
func (r *aiCallRecordRepository) GetByUserID(ctx context.Context, userID uint, limit, offset int) ([]*models.AICallRecord, error) {
	var records []*models.AICallRecord
	err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&records).Error
	return records, err
}

// GetByResumeID 根据简历ID获取AI调用记录列表
func (r *aiCallRecordRepository) GetByResumeID(ctx context.Context, resumeID uint, limit, offset int) ([]*models.AICallRecord, error) {
	var records []*models.AICallRecord
	err := r.db.WithContext(ctx).
		Where("resume_id = ?", resumeID).
		Order("created_at DESC").
		Limit(limit).
		Offset(offset).
		Find(&records).Error
	return records, err
}

// GetByUserIDPaginated 根据用户ID分页获取AI调用记录列表
func (r *aiCallRecordRepository) GetByUserIDPaginated(ctx context.Context, userID uint, page, pageSize int) ([]*models.AICallRecord, int64, error) {
	var records []*models.AICallRecord
	var total int64

	// 计算偏移量
	offset := (page - 1) * pageSize

	// 构建查询条件
	query := r.db.WithContext(ctx).Model(&models.AICallRecord{}).Where("user_id = ?", userID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据，按创建时间倒序排列（最新的在前面）
	if err := query.
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&records).Error; err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

// GetByResumeIDPaginated 根据简历ID分页获取AI调用记录列表
func (r *aiCallRecordRepository) GetByResumeIDPaginated(ctx context.Context, resumeID uint, page, pageSize int) ([]*models.AICallRecord, int64, error) {
	var records []*models.AICallRecord
	var total int64

	// 计算偏移量
	offset := (page - 1) * pageSize

	// 构建查询条件
	query := r.db.WithContext(ctx).Model(&models.AICallRecord{}).Where("resume_id = ?", resumeID)

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 获取分页数据，按创建时间倒序排列（最新的在前面）
	if err := query.
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&records).Error; err != nil {
		return nil, 0, err
	}

	return records, total, nil
}

// CountByUserIDAndPromptTypes 根据用户ID和提示词类型统计调用次数（在指定时间范围内）
func (r *aiCallRecordRepository) CountByUserIDAndPromptTypes(ctx context.Context, userID uint, promptTypes []enum.PromptType, startTime, endTime *time.Time) (int, error) {
	var count int64

	query := r.db.WithContext(ctx).Model(&models.AICallRecord{}).Where("user_id = ?", userID)

	// 添加提示词类型过滤
	if len(promptTypes) > 0 {
		query = query.Where("prompt_type IN ?", promptTypes)
	}

	// 添加时间范围过滤（如果提供了时间范围）
	if startTime != nil {
		query = query.Where("created_at >= ?", *startTime)
	}
	if endTime != nil {
		query = query.Where("created_at <= ?", *endTime)
	}

	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}

	return int(count), nil
}
