package repository

import (
	"context"

	"gorm.io/gorm"
	"resume-server/internal/models"
)

// UserDownloadCouponRepository 用户下载券仓库接口
type UserDownloadCouponRepository interface {
	// GetAvailableCouponsCount 获取用户可用的下载券数量
	GetAvailableCouponsCount(ctx context.Context, userID uint) (int64, error)
	// ConsumeOneCoupon 消费一张下载券
	ConsumeOneCoupon(ctx context.Context, userID uint) error
	// CreateCoupons 批量创建下载券
	CreateCoupons(ctx context.Context, coupons []*models.UserDownloadCoupon) error
}

// userDownloadCouponRepository 用户下载券仓库实现
type userDownloadCouponRepository struct {
	db *gorm.DB
}

// NewUserDownloadCouponRepository 创建用户下载券仓库
func NewUserDownloadCouponRepository(db *gorm.DB) UserDownloadCouponRepository {
	return &userDownloadCouponRepository{
		db: db,
	}
}

// GetAvailableCouponsCount 获取用户可用的下载券数量
func (r *userDownloadCouponRepository) GetAvailableCouponsCount(ctx context.Context, userID uint) (int64, error) {
	var count int64

	// 查询用户未使用的下载券数量
	err := r.db.WithContext(ctx).
		Model(&models.UserDownloadCoupon{}).
		Where("user_id = ? AND is_used = ?", userID, false).
		Count(&count).Error

	if err != nil {
		return 0, err
	}

	return count, nil
}

// ConsumeOneCoupon 消费一张下载券
func (r *userDownloadCouponRepository) ConsumeOneCoupon(ctx context.Context, userID uint) error {
	// 查找用户第一张未使用的下载券
	var coupon models.UserDownloadCoupon
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND is_used = ?", userID, false).
		Order("created_at ASC"). // 按创建时间升序，先消费最早的券
		First(&coupon).Error

	if err != nil {
		return err
	}

	// 使用下载券
	coupon.Use()

	// 更新数据库
	err = r.db.WithContext(ctx).Save(&coupon).Error
	if err != nil {
		return err
	}

	return nil
}

// CreateCoupons 批量创建下载券
func (r *userDownloadCouponRepository) CreateCoupons(ctx context.Context, coupons []*models.UserDownloadCoupon) error {
	if len(coupons) == 0 {
		return nil
	}

	// 批量插入下载券
	err := r.db.WithContext(ctx).Create(&coupons).Error
	if err != nil {
		return err
	}

	return nil
}
