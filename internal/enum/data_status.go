package enum

// DataStatus 数据状态枚举
type DataStatus int

const (
	// DataStatusNoData 没数据
	DataStatusNoData DataStatus = 1

	// DataStatusProcessing 处理中
	DataStatusProcessing DataStatus = 2

	// DataStatusHasData 有数据
	DataStatusHasData DataStatus = 3
)

// String 返回数据状态的字符串表示
func (s DataStatus) String() string {
	switch s {
	case DataStatusNoData:
		return "没数据"
	case DataStatusProcessing:
		return "处理中"
	case DataStatusHasData:
		return "有数据"
	default:
		return "未知状态"
	}
}

// IsValid 检查数据状态是否有效
func (s DataStatus) IsValid() bool {
	return s >= DataStatusNoData && s <= DataStatusHasData
}

// IsNoData 检查是否为没数据状态
func (s DataStatus) IsNoData() bool {
	return s == DataStatusNoData
}

// IsProcessing 检查是否为处理中状态
func (s DataStatus) IsProcessing() bool {
	return s == DataStatusProcessing
}

// IsHasData 检查是否为有数据状态
func (s DataStatus) IsHasData() bool {
	return s == DataStatusHasData
}
