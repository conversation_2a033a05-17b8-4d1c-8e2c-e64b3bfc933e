package enum

// PromptType AI提示词类型枚举
type PromptType string

const (
	// PromptTypeGenerate AI生成
	PromptTypeGenerate PromptType = "generate"

	// PromptTypeContinue 续写
	PromptTypeContinue PromptType = "continue"

	// PromptTypeProfessional 更专业
	PromptTypeProfessional PromptType = "professional"

	// PromptTypeConcise 更凝练
	PromptTypeConcise PromptType = "concise"

	// PromptTypeDetailed 更详细
	PromptTypeDetailed PromptType = "detailed"

	// PromptTypeGenerateResume AI生成简历
	PromptTypeGenerateResume PromptType = "generate_resume"

	// PromptTypeOptimize AI优化
	PromptTypeOptimize PromptType = "optimize"

	// PromptTypeScore AI简历打分
	PromptTypeScore PromptType = "score"
)

// String 返回提示词类型的字符串表示
func (t PromptType) String() string {
	switch t {
	case PromptTypeGenerate:
		return "AI生成"
	case PromptTypeContinue:
		return "续写"
	case PromptTypeProfessional:
		return "更专业"
	case PromptTypeConcise:
		return "更凝练"
	case PromptTypeDetailed:
		return "更详细"
	case PromptTypeGenerateResume:
		return "AI生成简历"
	case PromptTypeOptimize:
		return "AI优化"
	case PromptTypeScore:
		return "AI简历打分"
	default:
		return "未知类型"
	}
}

// IsValid 检查提示词类型是否有效
func (t PromptType) IsValid() bool {
	switch t {
	case PromptTypeGenerate, PromptTypeContinue, PromptTypeProfessional,
		PromptTypeConcise, PromptTypeDetailed, PromptTypeGenerateResume, PromptTypeOptimize, PromptTypeScore:
		return true
	default:
		return false
	}
}
