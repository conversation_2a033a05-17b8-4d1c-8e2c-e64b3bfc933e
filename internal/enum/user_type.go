package enum

// UserType 用户类型枚举
type UserType int

const (
	// UserTypeGuest 游客
	UserTypeGuest UserType = 1

	// UserTypeRegular 普通用户
	UserTypeRegular UserType = 2

	// UserTypeMember 会员
	UserTypeMember UserType = 3
)

// String 返回用户类型的字符串表示
func (t UserType) String() string {
	switch t {
	case UserTypeGuest:
		return "游客"
	case UserTypeRegular:
		return "普通用户"
	case UserTypeMember:
		return "会员"
	default:
		return "未知类型"
	}
}

// IsValid 检查用户类型是否有效
func (t UserType) IsValid() bool {
	return t >= UserTypeGuest && t <= UserTypeMember
}

// IsMember 检查是否为会员
func (t UserType) IsMember() bool {
	return t == UserTypeMember
}

// IsGuest 检查是否为游客
func (t UserType) IsGuest() bool {
	return t == UserTypeGuest
}

// IsRegular 检查是否为普通用户
func (t UserType) IsRegular() bool {
	return t == UserTypeRegular
}
