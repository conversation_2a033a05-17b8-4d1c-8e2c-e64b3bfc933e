package enum

// PrivilegeType 权益类型枚举
type PrivilegeType int

const (
	// PrivilegeResumeDownload 简历修改、下载
	PrivilegeResumeDownload PrivilegeType = iota + 1

	// PrivilegeResumeCreate 简历最大创建数
	PrivilegeResumeCreate

	// PrivilegeAIGenerate AI生成
	PrivilegeAIGenerate

	// PrivilegeAIRewrite AI改写
	PrivilegeAIRewrite

	// PrivilegeAIOptimize AI简历优化
	PrivilegeAIOptimize

	// PrivilegeAIDiagnose AI简历打分
	PrivilegeAIDiagnose

	// PrivilegeAIOneClick AI一键生成简历
	PrivilegeAIOneClick
)

// String 返回权益类型的字符串表示
func (t PrivilegeType) String() string {
	switch t {
	case PrivilegeResumeDownload:
		return "简历修改、下载"
	case PrivilegeResumeCreate:
		return "简历最大创建数"
	case PrivilegeAIGenerate:
		return "AI生成"
	case PrivilegeAIRewrite:
		return "AI改写"
	case PrivilegeAIOptimize:
		return "AI简历优化"
	case PrivilegeAIDiagnose:
		return "AI简历打分"
	case PrivilegeAIOneClick:
		return "AI一键生成简历"
	default:
		return "未知权益类型"
	}
}

// IsValid 检查权益类型是否有效
func (t PrivilegeType) IsValid() bool {
	return t >= PrivilegeResumeDownload && t <= PrivilegeAIOneClick
}

// IsResumeRelated 检查是否为简历相关权益
func (t PrivilegeType) IsResumeRelated() bool {
	return t == PrivilegeResumeDownload || t == PrivilegeResumeCreate
}

// IsAIRelated 检查是否为AI相关权益
func (t PrivilegeType) IsAIRelated() bool {
	return t >= PrivilegeAIGenerate && t <= PrivilegeAIOneClick
}
