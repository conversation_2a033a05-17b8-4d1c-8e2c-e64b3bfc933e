package enum

// CouponSource 券来源枚举
type CouponSource int

const (
	// CouponSourceOrder 订单充值
	CouponSourceOrder CouponSource = 1

	// CouponSourceShare 分享活动
	CouponSourceShare CouponSource = 2
)

// String 返回券来源的字符串表示
func (s CouponSource) String() string {
	switch s {
	case CouponSourceOrder:
		return "订单充值"
	case CouponSourceShare:
		return "分享活动"
	default:
		return "未知来源"
	}
}

// IsValid 检查券来源是否有效
func (s CouponSource) IsValid() bool {
	return s >= CouponSourceOrder && s <= CouponSourceShare
}

// IsOrder 检查是否为订单充值来源
func (s CouponSource) IsOrder() bool {
	return s == CouponSourceOrder
}

// IsShare 检查是否为分享活动来源
func (s CouponSource) IsShare() bool {
	return s == CouponSourceShare
}
