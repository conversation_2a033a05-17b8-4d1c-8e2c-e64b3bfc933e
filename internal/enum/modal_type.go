package enum

// ModalType 弹窗类型枚举
type ModalType int

const (
	// ModalTypeLogin 登录弹窗
	ModalTypeLogin ModalType = 1

	// ModalTypeMembership 会员付费弹窗
	ModalTypeMembership ModalType = 2
)

// String 返回弹窗类型的字符串表示
func (t ModalType) String() string {
	switch t {
	case ModalTypeLogin:
		return "登录弹窗"
	case ModalTypeMembership:
		return "会员付费弹窗"
	default:
		return "未知弹窗类型"
	}
}

// IsValid 检查弹窗类型是否有效
func (t ModalType) IsValid() bool {
	return t >= ModalTypeLogin && t <= ModalTypeMembership
}
