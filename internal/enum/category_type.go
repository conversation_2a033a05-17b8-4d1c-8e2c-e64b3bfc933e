package enum

// CategoryType 分类类型枚举
type CategoryType int

const (
	// CategoryTypeHotTemplate 热门模板
	CategoryTypeHotTemplate CategoryType = 1

	// CategoryTypeUniversityMajor 大学专业
	CategoryTypeUniversityMajor CategoryType = 2

	// CategoryTypeDesignStyle 设计风格
	CategoryTypeDesignStyle CategoryType = 3

	// CategoryTypePosition 行业职位
	CategoryTypePosition CategoryType = 4
)

// String 返回分类类型的字符串表示
func (t CategoryType) String() string {
	switch t {
	case CategoryTypeHotTemplate:
		return "热门模板"
	case CategoryTypeUniversityMajor:
		return "大学专业"
	case CategoryTypeDesignStyle:
		return "设计风格"
	case CategoryTypePosition:
		return "行业职位"
	default:
		return "未知类型"
	}
}

// IsValid 检查分类类型是否有效
func (t CategoryType) IsValid() bool {
	return t >= CategoryTypeHotTemplate && t <= CategoryTypePosition
}

// IsHotTemplate 检查是否为热门模板
func (t CategoryType) IsHotTemplate() bool {
	return t == CategoryTypeHotTemplate
}

// IsUniversityMajor 检查是否为大学专业
func (t CategoryType) IsUniversityMajor() bool {
	return t == CategoryTypeUniversityMajor
}

// IsDesignStyle 检查是否为设计风格
func (t CategoryType) IsDesignStyle() bool {
	return t == CategoryTypeDesignStyle
}
