package enum

// UserStatus 用户状态枚举
type UserStatus int

const (
	// UserStatusDisabled 用户已禁用
	UserStatusDisabled UserStatus = 0

	// UserStatusEnabled 用户已启用
	UserStatusEnabled UserStatus = 1
)

// String 返回用户状态的字符串表示
func (s UserStatus) String() string {
	switch s {
	case UserStatusDisabled:
		return "已禁用"
	case UserStatusEnabled:
		return "已启用"
	default:
		return "未知状态"
	}
}

// IsValid 检查用户状态是否有效
func (s UserStatus) IsValid() bool {
	return s == UserStatusDisabled || s == UserStatusEnabled
}
