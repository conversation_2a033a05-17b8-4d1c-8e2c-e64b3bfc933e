package enum

// MembershipStatus 会员状态枚举
type MembershipStatus int

const (
	// MembershipStatusExpired 已过期
	MembershipStatusExpired MembershipStatus = 1

	// MembershipStatusActive 生效中
	MembershipStatusActive MembershipStatus = 2
)

// String 返回会员状态的字符串表示
func (s MembershipStatus) String() string {
	switch s {
	case MembershipStatusExpired:
		return "已过期"
	case MembershipStatusActive:
		return "生效中"
	default:
		return "未知状态"
	}
}

// IsValid 检查会员状态是否有效
func (s MembershipStatus) IsValid() bool {
	return s >= MembershipStatusExpired && s <= MembershipStatusActive
}

// IsActive 检查会员是否处于生效状态
func (s MembershipStatus) IsActive() bool {
	return s == MembershipStatusActive
}

// IsExpired 检查会员是否已过期
func (s MembershipStatus) IsExpired() bool {
	return s == MembershipStatusExpired
}
