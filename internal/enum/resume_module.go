package enum

// ResumeModule 简历模块枚举
type ResumeModule string

const (
	// ResumeModuleBasicInfo 基本信息
	ResumeModuleBasicInfo ResumeModule = "basic_info"

	// ResumeModuleEducation 教育经历
	ResumeModuleEducation ResumeModule = "education"

	// ResumeModuleWork 工作经历
	ResumeModuleWork ResumeModule = "work"

	// ResumeModuleProject 项目经历
	ResumeModuleProject ResumeModule = "project"

	// ResumeModuleResearch 科研经历
	ResumeModuleResearch ResumeModule = "research"

	// ResumeModuleTeam 社团经历
	ResumeModuleTeam ResumeModule = "team"

	// ResumeModulePortfolio 作品集
	ResumeModulePortfolio ResumeModule = "portfolio"

	// ResumeModuleOther 其他模块
	ResumeModuleOther ResumeModule = "other"

	// ResumeModulePersonalSummary 个人总结
	ResumeModulePersonalSummary ResumeModule = "personal_summary"

	// ResumeModuleHonors 荣誉奖项
	ResumeModuleHonors ResumeModule = "honors"

	// ResumeModuleSkills 技能特长
	ResumeModuleSkills ResumeModule = "skills"

	// ResumeModuleCustomModules 自定义模块
	ResumeModuleCustomModules ResumeModule = "custom_modules"

	// ResumeModuleSlogan 简历标语
	ResumeModuleSlogan ResumeModule = "slogan"

	// ResumeModuleResumeStyle 简历样式
	ResumeModuleResumeStyle ResumeModule = "resume_style"
)

// String 返回简历模块的中文名称
func (m ResumeModule) String() string {
	switch m {
	case ResumeModuleBasicInfo:
		return "基本信息"
	case ResumeModuleEducation:
		return "教育经历"
	case ResumeModuleWork:
		return "工作经历"
	case ResumeModuleProject:
		return "项目经历"
	case ResumeModuleResearch:
		return "科研经历"
	case ResumeModuleTeam:
		return "社团经历"
	case ResumeModulePortfolio:
		return "作品集"
	case ResumeModuleOther:
		return "其他模块"
	case ResumeModulePersonalSummary:
		return "个人总结"
	case ResumeModuleHonors:
		return "荣誉奖项"
	case ResumeModuleSkills:
		return "技能特长"
	case ResumeModuleCustomModules:
		return "自定义模块"
	case ResumeModuleSlogan:
		return "简历标语"
	case ResumeModuleResumeStyle:
		return "简历样式"
	default:
		return "未知模块"
	}
}

// IsValid 检查简历模块是否有效
func (m ResumeModule) IsValid() bool {
	switch m {
	case ResumeModuleBasicInfo, ResumeModuleEducation, ResumeModuleWork, ResumeModuleProject,
		ResumeModuleResearch, ResumeModuleTeam, ResumeModulePortfolio, ResumeModuleOther,
		ResumeModulePersonalSummary, ResumeModuleHonors, ResumeModuleSkills,
		ResumeModuleCustomModules, ResumeModuleSlogan, ResumeModuleResumeStyle:
		return true
	default:
		return false
	}
}

// GetFieldName 获取对应的字段名（现在直接返回string值）
func (m ResumeModule) GetFieldName() string {
	return string(m)
}
