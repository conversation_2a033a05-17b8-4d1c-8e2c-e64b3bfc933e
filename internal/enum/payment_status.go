package enum

// PaymentStatus 支付状态枚举
type PaymentStatus int

const (
	// PaymentStatusPending 待支付
	PaymentStatusPending PaymentStatus = 1

	// PaymentStatusProcessing 支付处理中
	PaymentStatusProcessing PaymentStatus = 2

	// PaymentStatusSuccess 支付成功
	PaymentStatusSuccess PaymentStatus = 3

	// PaymentStatusFailed 支付失败
	PaymentStatusFailed PaymentStatus = 4

	// PaymentStatusTimeout 支付超时
	PaymentStatusTimeout PaymentStatus = 5
)

// String 返回支付状态的字符串表示
func (s PaymentStatus) String() string {
	switch s {
	case PaymentStatusPending:
		return "待支付"
	case PaymentStatusProcessing:
		return "支付处理中"
	case PaymentStatusSuccess:
		return "支付成功"
	case PaymentStatusFailed:
		return "支付失败"
	case PaymentStatusTimeout:
		return "支付超时"
	default:
		return "未知状态"
	}
}

// IsValid 检查支付状态是否有效
func (s PaymentStatus) IsValid() bool {
	return s >= PaymentStatusPending && s <= PaymentStatusTimeout
}

// IsPending 检查是否为待支付状态
func (s PaymentStatus) IsPending() bool {
	return s == PaymentStatusPending
}

// IsProcessing 检查是否为支付处理中状态
func (s PaymentStatus) IsProcessing() bool {
	return s == PaymentStatusProcessing
}

// IsSuccess 检查是否为支付成功状态
func (s PaymentStatus) IsSuccess() bool {
	return s == PaymentStatusSuccess
}

// IsFailed 检查是否为支付失败状态
func (s PaymentStatus) IsFailed() bool {
	return s == PaymentStatusFailed
}

// IsTimeout 检查是否为支付超时状态
func (s PaymentStatus) IsTimeout() bool {
	return s == PaymentStatusTimeout
}
