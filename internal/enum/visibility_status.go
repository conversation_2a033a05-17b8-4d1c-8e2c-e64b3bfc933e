package enum

// VisibilityStatus 可见性状态枚举
type VisibilityStatus int

const (
	// VisibilityStatusVisible 可见
	VisibilityStatusVisible VisibilityStatus = 1

	// VisibilityStatusHidden 隐藏
	VisibilityStatusHidden VisibilityStatus = 2
)

// String 返回可见性状态的字符串表示
func (s VisibilityStatus) String() string {
	switch s {
	case VisibilityStatusVisible:
		return "可见"
	case VisibilityStatusHidden:
		return "隐藏"
	default:
		return "未知状态"
	}
}

// IsValid 检查可见性状态是否有效
func (s VisibilityStatus) IsValid() bool {
	return s >= VisibilityStatusVisible && s <= VisibilityStatusHidden
}

// IsVisible 检查是否为可见状态
func (s VisibilityStatus) IsVisible() bool {
	return s == VisibilityStatusVisible
}

// IsHidden 检查是否为隐藏状态
func (s VisibilityStatus) IsHidden() bool {
	return s == VisibilityStatusHidden
}
