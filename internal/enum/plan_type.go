package enum

// PlanType 套餐类型枚举
type PlanType int

const (
	// PlanTypeMembership 会员套餐
	PlanTypeMembership PlanType = 1

	// PlanTypeDownloadCoupon 下载券套餐
	PlanTypeDownloadCoupon PlanType = 2
)

// String 返回套餐类型的字符串表示
func (t PlanType) String() string {
	switch t {
	case PlanTypeMembership:
		return "会员套餐"
	case PlanTypeDownloadCoupon:
		return "下载券套餐"
	default:
		return "未知类型"
	}
}

// IsValid 检查套餐类型是否有效
func (t PlanType) IsValid() bool {
	return t >= PlanTypeMembership && t <= PlanTypeDownloadCoupon
}

// IsMembership 检查是否为会员套餐
func (t PlanType) IsMembership() bool {
	return t == PlanTypeMembership
}

// IsDownloadCoupon 检查是否为下载券套餐
func (t PlanType) IsDownloadCoupon() bool {
	return t == PlanTypeDownloadCoupon
}
