package enum

// PaymentMethod 支付方式枚举
type PaymentMethod int

const (
	// PaymentMethodWechat 微信支付
	PaymentMethodWechat PaymentMethod = 1

	// PaymentMethodAlipay 支付宝
	PaymentMethodAlipay PaymentMethod = 2
)

// String 返回支付方式的字符串表示
func (m PaymentMethod) String() string {
	switch m {
	case PaymentMethodWechat:
		return "微信支付"
	case PaymentMethodAlipay:
		return "支付宝"
	default:
		return "未知支付方式"
	}
}

// IsValid 检查支付方式是否有效
func (m PaymentMethod) IsValid() bool {
	return m >= PaymentMethodWechat && m <= PaymentMethodAlipay
}

// IsWechat 检查是否为微信支付
func (m PaymentMethod) IsWechat() bool {
	return m == PaymentMethodWechat
}

// IsAlipay 检查是否为支付宝
func (m PaymentMethod) IsAlipay() bool {
	return m == PaymentMethodAlipay
}
