package enum

// AdminStatus 管理员状态枚举
type AdminStatus int

const (
	// AdminStatusDisabled 管理员已禁用
	AdminStatusDisabled AdminStatus = 0

	// AdminStatusEnabled 管理员已启用
	AdminStatusEnabled AdminStatus = 1
)

// String 返回管理员状态的字符串表示
func (s AdminStatus) String() string {
	switch s {
	case AdminStatusDisabled:
		return "已禁用"
	case AdminStatusEnabled:
		return "已启用"
	default:
		return "未知状态"
	}
}

// IsValid 检查管理员状态是否有效
func (s AdminStatus) IsValid() bool {
	return s == AdminStatusDisabled || s == AdminStatusEnabled
}

// IsEnabled 检查管理员是否已启用
func (s AdminStatus) IsEnabled() bool {
	return s == AdminStatusEnabled
}

// IsDisabled 检查管理员是否已禁用
func (s AdminStatus) IsDisabled() bool {
	return s == AdminStatusDisabled
}
