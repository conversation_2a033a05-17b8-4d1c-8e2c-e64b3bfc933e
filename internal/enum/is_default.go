package enum

// IsDefault 是否默认枚举
type IsDefault int

const (
	// IsDefaultNo 非默认
	IsDefaultNo IsDefault = 1

	// IsDefaultYes 默认
	IsDefaultYes IsDefault = 2
)

// String 返回是否默认的字符串表示
func (s IsDefault) String() string {
	switch s {
	case IsDefaultNo:
		return "非默认"
	case IsDefaultYes:
		return "默认"
	default:
		return "未知状态"
	}
}

// IsValid 检查是否默认状态是否有效
func (s IsDefault) IsValid() bool {
	return s >= IsDefaultNo && s <= IsDefaultYes
}

// IsYes 检查是否为默认状态
func (s IsDefault) IsYes() bool {
	return s == IsDefaultYes
}

// IsNo 检查是否为非默认状态
func (s IsDefault) IsNo() bool {
	return s == IsDefaultNo
}
