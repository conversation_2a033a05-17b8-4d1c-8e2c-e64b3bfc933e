package services

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
	"resume-server/config"
	"resume-server/internal/dto/dto_api"
	"resume-server/internal/enum"
	"resume-server/internal/exception"
	"resume-server/internal/models"
	"resume-server/internal/pkg"
	"resume-server/internal/repository"
	"resume-server/internal/utils"
	"resume-server/internal/vo"
	"resume-server/internal/vo/vo_api"
)

// OrderService 订单服务接口
type OrderService interface {
	// CreateAlipayOrder 创建支付宝订单
	CreateAlipayOrder(ctx context.Context, userID, planID uint, bdVid string) (*vo_api.OrderResponse, error)

	// CreateWechatPayOrder 创建微信支付订单
	CreateWechatPayOrder(ctx context.Context, userID, planID uint, bdVid string) (*vo_api.OrderResponse, error)

	// GetOrderStatus 查询订单状态
	GetOrderStatus(ctx context.Context, orderNo string) (*vo_api.OrderStatusResponse, error)

	// GetUserOrders 获取用户订单列表
	GetUserOrders(ctx context.Context, userID uint, req *dto_api.GetUserOrdersRequest) (*vo.PaginatedList[vo_api.OrderListItem], error)

	// HandleAlipayCallback 处理支付宝回调
	HandleAlipayCallback(ctx context.Context, orderNo, tradeStatus, tradeNo, callbackMessage string) error

	// HandleWechatPayCallback 处理微信支付回调
	HandleWechatPayCallback(ctx context.Context, orderNo, tradeState, transactionId, callbackMessage string) error
}

// orderService 订单服务实现
type orderService struct {
	orderRepo                 repository.OrderRepository
	planRepo                  repository.MembershipPlanRepository
	membershipRepo            repository.UserMembershipRepository
	userRepo                  repository.UserRepository
	userDownloadCouponService UserDownloadCouponService
	payService                *pkg.PayService
	config                    *config.Config
	logger                    *zap.Logger
}

// NewOrderService 创建订单服务
func NewOrderService(
	orderRepo repository.OrderRepository,
	planRepo repository.MembershipPlanRepository,
	membershipRepo repository.UserMembershipRepository,
	userRepo repository.UserRepository,
	userDownloadCouponService UserDownloadCouponService,
	payService *pkg.PayService,
	config *config.Config,
	logger *zap.Logger,
) OrderService {
	return &orderService{
		orderRepo:                 orderRepo,
		planRepo:                  planRepo,
		membershipRepo:            membershipRepo,
		userRepo:                  userRepo,
		userDownloadCouponService: userDownloadCouponService,
		payService:                payService,
		config:                    config,
		logger:                    logger,
	}
}

// createOrder 创建订单的通用方法
func (s *orderService) createOrder(
	ctx context.Context,
	userID, planID uint,
	paymentMethod enum.PaymentMethod,
	bdVid string,
	createPayQrCode func(orderNo, title, amount string) (string, error),
) (*vo_api.OrderResponse, error) {
	// 1. 检查会员套餐是否存在
	plan, err := s.planRepo.GetPlanByID(ctx, planID)
	if err != nil {
		s.logger.Error("获取会员套餐失败", zap.Uint("planID", planID), zap.Error(err))
		return nil, exception.ErrMembershipPlanNotFound
	}

	// 2. 检查是否有未支付的订单（15分钟内创建的）
	existingOrder, err := s.orderRepo.GetPendingOrderByUserAndPlan(ctx, userID, planID, paymentMethod, 15)
	if err == nil {
		// 存在未支付订单，检查bdVid是否需要更新
		if existingOrder.BdVid != bdVid {
			// bdVid不同，更新订单的bdVid字段
			existingOrder.BdVid = bdVid
			if updateErr := s.orderRepo.Update(ctx, existingOrder); updateErr != nil {
				s.logger.Error("更新订单bdVid失败", zap.String("orderNo", existingOrder.OrderNo), zap.Error(updateErr))
				// 更新失败不影响返回，继续返回订单信息
			} else {
				s.logger.Info("已更新订单bdVid", zap.String("orderNo", existingOrder.OrderNo), zap.String("bdVid", bdVid))
			}
		}

		s.logger.Info("存在未支付订单，直接返回", zap.String("orderNo", existingOrder.OrderNo))
		return &vo_api.OrderResponse{
			OrderNo:   existingOrder.OrderNo,
			Title:     existingOrder.Title,
			Amount:    existingOrder.Amount,
			CodeURL:   existingOrder.CodeURL,
			PackageID: existingOrder.MembershipPlanID,
		}, nil
	}

	// 3. 生成订单号
	orderNo := utils.GenerateOrderNo(userID, paymentMethod)

	// 4. 创建订单标题
	title := fmt.Sprintf("【%s】购买会员套餐-%s", s.config.App.Name, plan.Name)

	// 5. 创建订单
	order := &models.Order{
		UserID:           userID,
		MembershipPlanID: planID,
		PaymentMethod:    paymentMethod,
		Amount:           plan.ActualPrice,
		PaymentStatus:    enum.PaymentStatusPending,
		OrderNo:          orderNo,
		Title:            title,
		BdVid:            bdVid,
	}

	// 6. 调用支付接口获取支付二维码
	// 实际订单金额
	totalAmount := strconv.FormatFloat(plan.ActualPrice, 'f', 2, 64)

	// 支付金额，开发环境使用0.01元
	payAmount := totalAmount
	if s.config.App.Env == "development" || userID == 5 || userID == 20 {
		payAmount = "0.01"
		s.logger.Info("开发环境使用测试金额", zap.String("orderNo", orderNo), zap.String("payAmount", payAmount))
	}

	// 调用传入的支付接口函数
	qrCode, err := createPayQrCode(orderNo, title, payAmount)
	if err != nil {
		s.logger.Error("调用支付接口失败", zap.Error(err), zap.Int("paymentMethod", int(paymentMethod)))
		return nil, exception.ErrPaymentInterfaceFailed
	}

	// 7. 设置支付二维码链接
	order.CodeURL = qrCode

	// 8. 保存订单
	if err := s.orderRepo.Create(ctx, order); err != nil {
		s.logger.Error("创建订单失败", zap.Error(err))
		return nil, exception.ErrOrderCreateFailed
	}

	// 9. 返回订单信息
	return &vo_api.OrderResponse{
		OrderNo:   order.OrderNo,
		Title:     order.Title,
		Amount:    order.Amount,
		CodeURL:   order.CodeURL,
		PackageID: order.MembershipPlanID,
	}, nil
}

// CreateAlipayOrder 创建支付宝订单
func (s *orderService) CreateAlipayOrder(ctx context.Context, userID, planID uint, bdVid string) (*vo_api.OrderResponse, error) {
	// 使用通用方法创建订单，传入支付宝支付方式和支付宝二维码生成函数
	return s.createOrder(ctx, userID, planID, enum.PaymentMethodAlipay, bdVid, func(orderNo, title, amount string) (string, error) {
		return s.payService.AlipayTradePreCreate(s.config, orderNo, title, amount)
	})
}

// CreateWechatPayOrder 创建微信支付订单
func (s *orderService) CreateWechatPayOrder(ctx context.Context, userID, planID uint, bdVid string) (*vo_api.OrderResponse, error) {
	// 使用通用方法创建订单，传入微信支付方式和微信二维码生成函数
	return s.createOrder(ctx, userID, planID, enum.PaymentMethodWechat, bdVid, func(orderNo, title, amount string) (string, error) {
		return s.payService.WechatPayNativePreCreate(s.config, orderNo, title, amount)
	})
}

// GetOrderStatus 查询订单状态
func (s *orderService) GetOrderStatus(ctx context.Context, orderNo string) (*vo_api.OrderStatusResponse, error) {
	// 1. 根据订单号查询订单
	order, err := s.orderRepo.GetByOrderNo(ctx, orderNo)
	if err != nil {
		s.logger.Error("查询订单失败", zap.String("orderNo", orderNo), zap.Error(err))
		return nil, exception.ErrOrderNotFound
	}

	// 2. 查询用户信息获取渠道名称
	user, err := s.userRepo.GetByID(order.UserID)
	if err != nil {
		s.logger.Error("查询用户信息失败", zap.Uint("userID", order.UserID), zap.Error(err))
		// 用户信息查询失败不影响订单状态返回，渠道名称设为空
	}

	channelName := ""
	if user != nil {
		channelName = user.Channel
	}

	// 3. 构建响应，返回订单号、支付状态、订单金额、渠道名称和失败原因(如果有)
	return &vo_api.OrderStatusResponse{
		OrderNo:       order.OrderNo,
		PaymentStatus: int(order.PaymentStatus),
		Amount:        order.Amount,
		ChannelName:   channelName,
		FailReason:    order.FailReason,
	}, nil
}

// HandleAlipayCallback 处理支付宝回调
func (s *orderService) HandleAlipayCallback(ctx context.Context, orderNo, tradeStatus, tradeNo, callbackMessage string) error {
	// 1. 根据订单号查询订单
	order, err := s.orderRepo.GetByOrderNo(ctx, orderNo)
	if err != nil {
		s.logger.Error("查询订单失败", zap.String("orderNo", orderNo), zap.Error(err))
		return err
	}

	// 2. 如果不是待支付状态，直接返回
	if order.PaymentStatus != enum.PaymentStatusPending {
		s.logger.Info("订单状态非待支付，跳过处理",
			zap.String("orderNo", orderNo),
			zap.Int("currentStatus", int(order.PaymentStatus)))
		return nil
	}

	// 3. 存储回调消息并更新订单状态为支付处理中
	order.PaymentStatus = enum.PaymentStatusProcessing
	order.CallbackMessage = callbackMessage
	if err := s.orderRepo.Update(ctx, order); err != nil {
		s.logger.Error("更新订单状态为处理中失败", zap.String("orderNo", orderNo), zap.Error(err))
		return err
	}

	// 4. 根据交易状态处理订单
	switch tradeStatus {
	case "TRADE_SUCCESS", "TRADE_FINISHED":
		// 支付成功，给用户添加会员套餐
		if err := s.handleSuccessfulPayment(ctx, order, tradeNo); err != nil {
			s.logger.Error("处理支付成功失败", zap.String("orderNo", orderNo), zap.Error(err))
			// 更新订单状态为失败并记录错误原因
			order.PaymentStatus = enum.PaymentStatusFailed
			order.FailReason = err.Error()
			s.orderRepo.Update(ctx, order)
			return err
		}
	case "TRADE_CLOSED":
		// 交易关闭
		s.logger.Info("交易关闭", zap.String("orderNo", orderNo))
		order.PaymentStatus = enum.PaymentStatusTimeout
		order.FailReason = "交易关闭"
		if err := s.orderRepo.Update(ctx, order); err != nil {
			s.logger.Error("更新订单状态为超时失败", zap.String("orderNo", orderNo), zap.Error(err))
		}
	default:
		// 其他状态，记录错误
		errMsg := fmt.Sprintf("未知交易状态: %s", tradeStatus)
		s.logger.Warn(errMsg, zap.String("orderNo", orderNo))
		order.PaymentStatus = enum.PaymentStatusFailed
		order.FailReason = errMsg
		if err := s.orderRepo.Update(ctx, order); err != nil {
			s.logger.Error("更新订单状态为失败失败", zap.String("orderNo", orderNo), zap.Error(err))
		}
	}

	return nil
}

// handleSuccessfulPayment 处理支付成功的逻辑
func (s *orderService) handleSuccessfulPayment(ctx context.Context, order *models.Order, tradeNo string) error {
	// 1. 查询套餐信息
	plan, err := s.planRepo.GetPlanByID(ctx, order.MembershipPlanID)
	if err != nil {
		return fmt.Errorf("查询套餐失败: %w", err)
	}

	// 2. 根据套餐类型进行不同的处理
	switch plan.PlanType {
	case enum.PlanTypeMembership:
		// 会员套餐处理逻辑
		if err := s.handleMembershipPayment(ctx, order, plan); err != nil {
			return err
		}
	case enum.PlanTypeDownloadCoupon:
		// 下载券套餐处理逻辑
		if err := s.handleDownloadCouponPayment(ctx, order, plan); err != nil {
			return err
		}
	default:
		return fmt.Errorf("未知的套餐类型: %d", plan.PlanType)
	}

	// 3. 更新订单状态为成功
	order.PaymentStatus = enum.PaymentStatusSuccess
	if err := s.orderRepo.Update(ctx, order); err != nil {
		return fmt.Errorf("更新订单状态为成功失败: %w", err)
	}

	// 4. 上传百度转化数据
	if err := utils.UploadBaiduConversionData(ctx, order.BdVid, s.config.FrontendDomain, 26, s.logger); err != nil {
		// 百度转化数据上传失败不影响主流程，只记录错误日志
		s.logger.Error("上传百度转化数据失败",
			zap.String("orderNo", order.OrderNo),
			zap.String("bd_vid", order.BdVid),
			zap.Error(err))
	}

	s.logger.Info("支付处理成功",
		zap.String("orderNo", order.OrderNo),
		zap.String("tradeNo", tradeNo),
		zap.Uint("userID", order.UserID))

	return nil
}

// handleMembershipPayment 处理会员套餐支付成功逻辑
func (s *orderService) handleMembershipPayment(ctx context.Context, order *models.Order, plan *models.MembershipPlan) error {
	// 1. 计算会员到期时间
	expiresAt := s.calculateMembershipExpiry(plan.Duration)
	startTime := time.Now()

	// 2. 创建或更新用户会员关系
	if err := s.membershipRepo.CreateOrUpdate(ctx, order.UserID, order.MembershipPlanID, startTime, expiresAt); err != nil {
		return fmt.Errorf("创建或更新用户会员关系失败: %w", err)
	}

	// 3. 更新用户类型为会员
	user, err := s.userRepo.GetByID(order.UserID)
	if err != nil {
		return fmt.Errorf("查询用户失败: %w", err)
	}

	// 如果用户类型不是会员，则更新为会员
	if user.UserType != enum.UserTypeMember {
		user.UserType = enum.UserTypeMember
		if err := s.userRepo.Update(user); err != nil {
			return fmt.Errorf("更新用户类型为会员失败: %w", err)
		}
		s.logger.Info("用户类型已更新为会员", zap.Uint("userID", order.UserID))
	}

	s.logger.Info("成功添加用户会员套餐",
		zap.Uint("userID", order.UserID),
		zap.Uint("planID", order.MembershipPlanID),
		zap.Time("startTime", startTime),
		zap.Time("expiresAt", expiresAt))

	return nil
}

// handleDownloadCouponPayment 处理下载券套餐支付成功逻辑
func (s *orderService) handleDownloadCouponPayment(ctx context.Context, order *models.Order, plan *models.MembershipPlan) error {
	// 1. 根据套餐的 resume_limit 创建对应数量的下载券
	couponCount := plan.ResumeLimit
	if couponCount <= 0 {
		return fmt.Errorf("下载券套餐的简历数量限制无效: %d", couponCount)
	}

	// 2. 创建下载券
	if err := s.userDownloadCouponService.CreateCouponsFromOrder(ctx, order.UserID, order.ID, couponCount); err != nil {
		return fmt.Errorf("创建下载券失败: %w", err)
	}

	s.logger.Info("成功创建下载券",
		zap.Uint("userID", order.UserID),
		zap.Uint("planID", order.MembershipPlanID),
		zap.Int("couponCount", couponCount))

	return nil
}

// calculateMembershipExpiry 计算会员到期时间
func (s *orderService) calculateMembershipExpiry(duration string) time.Time {
	now := time.Now()

	// 解析时长格式 "1-0-0" (年-月-日)
	parts := strings.Split(duration, "-")
	if len(parts) != 3 {
		s.logger.Warn("会员套餐时长格式错误", zap.String("duration", duration))
		// 默认返回一个月
		return now.AddDate(0, 1, 0)
	}

	years := 0
	months := 0
	days := 0

	if y, err := strconv.Atoi(parts[0]); err == nil {
		years = y
	}
	if m, err := strconv.Atoi(parts[1]); err == nil {
		months = m
	}
	if d, err := strconv.Atoi(parts[2]); err == nil {
		days = d
	}

	return now.AddDate(years, months, days)
}

// HandleWechatPayCallback 处理微信支付回调
func (s *orderService) HandleWechatPayCallback(ctx context.Context, orderNo, tradeState, transactionId, callbackMessage string) error {
	// 1. 根据订单号查询订单
	order, err := s.orderRepo.GetByOrderNo(ctx, orderNo)
	if err != nil {
		s.logger.Error("查询订单失败", zap.String("orderNo", orderNo), zap.Error(err))
		return err
	}

	// 2. 如果不是待支付状态，直接返回
	if order.PaymentStatus != enum.PaymentStatusPending {
		s.logger.Info("订单状态非待支付，跳过处理",
			zap.String("orderNo", orderNo),
			zap.Int("currentStatus", int(order.PaymentStatus)))
		return nil
	}

	// 3. 存储回调消息并更新订单状态为支付处理中
	order.PaymentStatus = enum.PaymentStatusProcessing
	order.CallbackMessage = callbackMessage
	if err := s.orderRepo.Update(ctx, order); err != nil {
		s.logger.Error("更新订单状态为处理中失败", zap.String("orderNo", orderNo), zap.Error(err))
		return err
	}

	// 4. 根据交易状态处理订单
	switch tradeState {
	case "SUCCESS":
		// 支付成功，给用户添加会员套餐
		if err := s.handleSuccessfulPayment(ctx, order, transactionId); err != nil {
			s.logger.Error("处理支付成功失败", zap.String("orderNo", orderNo), zap.Error(err))
			// 更新订单状态为失败并记录错误原因
			order.PaymentStatus = enum.PaymentStatusFailed
			order.FailReason = err.Error()
			s.orderRepo.Update(ctx, order)
			return err
		}
	case "CLOSED", "REVOKED":
		// 交易关闭或撤销
		s.logger.Info("交易关闭或撤销", zap.String("orderNo", orderNo), zap.String("tradeState", tradeState))
		order.PaymentStatus = enum.PaymentStatusTimeout
		order.FailReason = fmt.Sprintf("交易%s", tradeState)
		if err := s.orderRepo.Update(ctx, order); err != nil {
			s.logger.Error("更新订单状态为超时失败", zap.String("orderNo", orderNo), zap.Error(err))
		}
	default:
		// 其他状态，记录日志但不处理
		s.logger.Info("收到其他交易状态", zap.String("orderNo", orderNo), zap.String("tradeState", tradeState))
	}

	return nil
}

// GetUserOrders 获取用户订单列表
func (s *orderService) GetUserOrders(ctx context.Context, userID uint, req *dto_api.GetUserOrdersRequest) (*vo.PaginatedList[vo_api.OrderListItem], error) {
	// 1. 设置默认值
	page := req.Page
	if page <= 0 {
		page = 1
	}

	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 10
	}

	// 2. 处理支付状态筛选参数
	var paymentStatuses []enum.PaymentStatus
	if req.PaymentStatus != "" {
		// 按逗号分割状态字符串
		statusStrings := strings.Split(req.PaymentStatus, ",")
		for _, statusStr := range statusStrings {
			statusStr = strings.TrimSpace(statusStr)
			if statusStr == "" {
				continue
			}
			// 转换为整数
			if statusInt, err := strconv.Atoi(statusStr); err == nil {
				status := enum.PaymentStatus(statusInt)
				if status.IsValid() {
					paymentStatuses = append(paymentStatuses, status)
				}
			}
		}
	}

	// 3. 调用仓储层获取订单数据
	orders, total, err := s.orderRepo.GetUserOrdersPaginated(ctx, userID, page, pageSize, paymentStatuses)
	if err != nil {
		s.logger.Error("获取用户订单列表失败", zap.Uint("userID", userID), zap.Error(err))
		return nil, exception.ErrOrderQueryFailed
	}

	// 4. 如果没有订单，返回空列表
	if len(orders) == 0 {
		return &vo.PaginatedList[vo_api.OrderListItem]{
			Total:    0,
			Page:     page,
			PageSize: pageSize,
			List:     []vo_api.OrderListItem{},
		}, nil
	}

	// 5. 获取所有订单关联的套餐ID
	planIDs := make([]uint, 0, len(orders))
	planIDMap := make(map[uint]bool)
	for _, order := range orders {
		if !planIDMap[order.MembershipPlanID] {
			planIDs = append(planIDs, order.MembershipPlanID)
			planIDMap[order.MembershipPlanID] = true
		}
	}

	// 6. 批量获取套餐信息
	planMap := make(map[uint]*models.MembershipPlan)
	for _, planID := range planIDs {
		plan, err := s.planRepo.GetPlanByID(ctx, planID)
		if err != nil {
			s.logger.Warn("获取套餐信息失败", zap.Uint("planID", planID), zap.Error(err))
			// 如果获取套餐失败，创建一个默认的套餐信息
			plan = &models.MembershipPlan{
				ID:   planID,
				Name: "未知套餐",
			}
		}
		planMap[planID] = plan
	}

	// 7. 转换为响应VO
	orderItems := make([]vo_api.OrderListItem, 0, len(orders))
	for _, order := range orders {
		plan := planMap[order.MembershipPlanID]

		orderItem := vo_api.OrderListItem{
			ID:               order.ID,
			OrderNo:          order.OrderNo,
			Title:            order.Title,
			Amount:           order.Amount,
			PaymentMethod:    order.PaymentMethod,
			PaymentStatus:    order.PaymentStatus,
			PaymentStatusStr: order.PaymentStatus.String(),
			FailReason:       order.FailReason,
			CreatedAt:        order.CreatedAt,
			MembershipPlan: vo_api.MembershipPlanInfo{
				ID:   plan.ID,
				Name: plan.Name,
			},
		}

		orderItems = append(orderItems, orderItem)
	}

	// 8. 构建分页响应
	result := vo.NewPaginatedList(orderItems, total, page, pageSize)

	s.logger.Info("获取用户订单列表成功",
		zap.Uint("userID", userID),
		zap.Int("page", page),
		zap.Int("pageSize", pageSize),
		zap.Int64("total", total),
		zap.Int("count", len(orderItems)))

	return &result, nil
}
