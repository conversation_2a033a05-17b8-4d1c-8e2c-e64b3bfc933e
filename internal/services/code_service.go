package services

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"resume-server/config"
	"resume-server/internal/exception"
	"resume-server/internal/pkg"
	"resume-server/internal/repository"
	"resume-server/internal/utils"
)

// SMSCodeService 短信验证码服务接口
// 该接口用于解决循环依赖问题，只包含 UserService 需要的方法
type SMSCodeService interface {
	// VerifySMSCode 验证短信验证码
	VerifySMSCode(ctx context.Context, phone, code string) (bool, error)
}

// CodeService 验证码服务接口
type CodeService interface {
	SMSCodeService

	// SendSMSCode 发送短信验证码
	SendSMSCode(ctx context.Context, phone string) error
}

// codeService 验证码服务实现
type codeService struct {
	redisClient   *redis.Client
	smsService    pkg.SMSService
	wechatService *pkg.WeChatService
	config        *config.Config
	userRepo      repository.UserRepository
	jwtService    pkg.JWTService
}

// NewCodeService 创建验证码服务
func NewCodeService(
	redisClient *redis.Client,
	smsService pkg.SMSService,
	wechatService *pkg.WeChatService,
	cfg *config.Config,
	userRepo repository.UserRepository,
	jwtService pkg.JWTService,
) CodeService {
	return &codeService{
		redisClient:   redisClient,
		smsService:    smsService,
		wechatService: wechatService,
		config:        cfg,
		userRepo:      userRepo,
		jwtService:    jwtService,
	}
}

// SendSMSCode 发送短信验证码
func (s *codeService) SendSMSCode(ctx context.Context, phone string) error {
	// 验证手机号格式
	if !isValidPhoneNumber(phone) {
		return exception.ErrPhoneInvalid
	}

	// 检查发送频率限制
	rateLimitKey := fmt.Sprintf("sms:ratelimit:%s", phone)
	exists, err := s.redisClient.Exists(ctx, rateLimitKey).Result()
	if err != nil {
		pkg.Error("检查短信发送频率失败", zap.Error(err))
		return exception.ErrInternalServer
	}

	if exists == 1 {
		remainingTime, err := s.redisClient.TTL(ctx, rateLimitKey).Result()
		if err != nil {
			pkg.Error("获取短信发送频率限制剩余时间失败", zap.Error(err))
			return exception.ErrInternalServer
		}

		seconds := int(remainingTime.Seconds())
		return exception.ErrSMSCodeRateLimit.WithDetail(fmt.Sprintf("请%d秒后再试", seconds))
	}

	// 生成6位随机验证码
	code := utils.GenerateRandomCode(6)
	pkg.Info("生成验证码", zap.String("code", code))

	// 将验证码保存到Redis，有效期5分钟
	codeKey := fmt.Sprintf("sms:code:%s", phone)
	err = s.redisClient.Set(ctx, codeKey, code, 5*time.Minute).Err()
	if err != nil {
		pkg.Error("保存验证码到Redis失败", zap.Error(err))
		return exception.ErrInternalServer
	}

	// 设置发送频率限制，1分钟内不能再次发送
	err = s.redisClient.Set(ctx, rateLimitKey, 1, 1*time.Minute).Err()
	if err != nil {
		pkg.Error("设置短信发送频率限制失败", zap.Error(err))
		// 继续执行，不影响主流程
	}

	// 发送短信验证码
	err = s.smsService.SendLoginCode(phone, code)
	if err != nil {
		pkg.Error("发送短信验证码失败", zap.String("phone", phone), zap.Error(err))
		return exception.ErrSMSCodeSendFailed
	}

	pkg.Info("短信验证码发送成功", zap.String("phone", phone))
	return nil
}

// VerifySMSCode 验证短信验证码
func (s *codeService) VerifySMSCode(ctx context.Context, phone, code string) (bool, error) {
	codeKey := fmt.Sprintf("sms:code:%s", phone)

	// 从Redis获取验证码
	savedCode, err := s.redisClient.Get(ctx, codeKey).Result()
	if err != nil {
		if err == redis.Nil {
			// 验证码不存在或已过期
			return false, nil
		}
		return false, err
	}

	// 验证码比对
	isValid := savedCode == code

	// 如果验证成功，删除验证码，防止重复使用
	if isValid {
		s.redisClient.Del(ctx, codeKey)
	}

	return isValid, nil
}

// 验证手机号格式（简单验证）
func isValidPhoneNumber(phone string) bool {
	// 中国大陆手机号格式: 1开头，总共11位
	if len(phone) != 11 || phone[0] != '1' {
		return false
	}

	// 检查是否全为数字
	for _, ch := range phone {
		if ch < '0' || ch > '9' {
			return false
		}
	}

	return true
}
