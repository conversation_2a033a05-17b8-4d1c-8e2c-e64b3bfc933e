package services

import (
	"time"
	"go.uber.org/zap"

	"resume-server/internal/dto/dto_admin"
	"resume-server/internal/enum"
	"resume-server/internal/exception"
	"resume-server/internal/models"
	"resume-server/internal/pkg"
	"resume-server/internal/repository"
	"resume-server/internal/vo/vo_admin"
)

// ChannelService 渠道服务接口
type ChannelService interface {
	GetChannelUsers(adminChannels []string, isSuperAdmin bool, req *dto_admin.GetChannelUsersRequest) (*vo_admin.ChannelUsersListResponse, error)
	GetChannelPayments(adminChannels []string, isSuperAdmin bool, req *dto_admin.GetChannelPaymentsRequest) (*vo_admin.ChannelPaymentsListResponse, error)
}

// channelService 渠道服务实现
type channelService struct {
	userRepo  repository.UserRepository
	orderRepo repository.OrderRepository
}

// NewChannelService 创建渠道服务
func NewChannelService(userRepo repository.UserRepository, orderRepo repository.OrderRepository) ChannelService {
	return &channelService{
		userRepo:  userRepo,
		orderRepo: orderRepo,
	}
}

// GetChannelUsers 获取渠道用户列表
func (s *channelService) GetChannelUsers(adminChannels []string, isSuperAdmin bool, req *dto_admin.GetChannelUsersRequest) (*vo_admin.ChannelUsersListResponse, error) {
	// 设置默认分页参数
	page := 1
	pageSize := 10
	if req.Page > 0 {
		page = req.Page
	}
	if req.PageSize > 0 {
		pageSize = req.PageSize
	}

	// 构建查询条件
	query := s.userRepo.GetDB().Model(&models.User{})

	// 渠道权限限制
	if !isSuperAdmin {
		if len(adminChannels) == 0 {
			pkg.Warn("非超级管理员没有渠道权限")
			return &vo_admin.ChannelUsersListResponse{
				Total:    0,
				Page:     page,
				PageSize: pageSize,
				List:     []vo_admin.ChannelUserResponse{},
			}, nil
		}
		// 限制只能查看管理员有权限的渠道
		query = query.Where("channel IN ?", adminChannels)
	}

	// 渠道号查询
	if req.Channel != "" {
		// 如果不是超级管理员，需要检查是否有该渠道权限
		if !isSuperAdmin {
			hasPermission := false
			for _, channel := range adminChannels {
				if channel == req.Channel {
					hasPermission = true
					break
				}
			}
			if !hasPermission {
				pkg.Warn("管理员没有该渠道权限", zap.String("channel", req.Channel))
				return nil, exception.ErrAdminPermissionDenied.WithMessage("没有该渠道的查看权限")
			}
		}
		query = query.Where("channel = ?", req.Channel)
	}

	// 时间范围查询
	if req.StartTime != nil {
		query = query.Where("created_at >= ?", req.StartTime)
	}
	if req.EndTime != nil {
		query = query.Where("created_at <= ?", req.EndTime)
	}

	// 查询总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		pkg.Error("查询渠道用户总数失败", zap.Error(err))
		return nil, exception.ErrInternalServer.WithMessage("查询用户列表失败")
	}

	// 分页查询
	var users []models.User
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&users).Error; err != nil {
		pkg.Error("查询渠道用户列表失败", zap.Error(err))
		return nil, exception.ErrInternalServer.WithMessage("查询用户列表失败")
	}

	// 转换为响应格式
	userResponses := make([]vo_admin.ChannelUserResponse, len(users))
	for i, user := range users {
		userResponses[i] = vo_admin.ChannelUserResponse{
			ID:        user.ID,
			Username:  user.Username,
			Email:     user.Email,
			Phone:     user.Phone,
			Channel:   user.Channel,
			UserType:  user.UserType,
			Status:    user.Status,
			CreatedAt: user.CreatedAt,
		}
	}

	return &vo_admin.ChannelUsersListResponse{
		Total:    total,
		Page:     page,
		PageSize: pageSize,
		List:     userResponses,
	}, nil
}

// GetChannelPayments 获取渠道付费列表
func (s *channelService) GetChannelPayments(adminChannels []string, isSuperAdmin bool, req *dto_admin.GetChannelPaymentsRequest) (*vo_admin.ChannelPaymentsListResponse, error) {
	// 设置默认分页参数
	page := 1
	pageSize := 10
	if req.Page > 0 {
		page = req.Page
	}
	if req.PageSize > 0 {
		pageSize = req.PageSize
	}

	// 渠道权限检查
	if !isSuperAdmin {
		if len(adminChannels) == 0 {
			pkg.Warn("非超级管理员没有渠道权限")
			return &vo_admin.ChannelPaymentsListResponse{
				Total:       0,
				Page:        page,
				PageSize:    pageSize,
				TotalAmount: 0,
				List:        []vo_admin.ChannelPaymentResponse{},
			}, nil
		}

		// 如果查询特定渠道，需要检查是否有该渠道权限
		if req.Channel != "" {
			hasPermission := false
			for _, channel := range adminChannels {
				if channel == req.Channel {
					hasPermission = true
					break
				}
			}
			if !hasPermission {
				pkg.Warn("管理员没有该渠道权限", zap.String("channel", req.Channel))
				return nil, exception.ErrAdminPermissionDenied.WithMessage("没有该渠道的查看权限")
			}
		}
	}

	// 构建统计查询的基础SQL
	baseSQL := `
		FROM ` + "`order`" + `
		LEFT JOIN user ON order.user_id = user.id
		WHERE order.payment_status = ?
	`

	// 构建查询参数 - 只查询支付成功的订单
	baseArgs := []interface{}{enum.PaymentStatusSuccess}
	if !isSuperAdmin && len(adminChannels) > 0 {
		baseSQL += " AND user.channel IN ?"
		baseArgs = append(baseArgs, adminChannels)
	}

	if req.Channel != "" {
		baseSQL += " AND user.channel = ?"
		baseArgs = append(baseArgs, req.Channel)
	}

	if req.StartTime != nil {
		baseSQL += " AND order.created_at >= ?"
		baseArgs = append(baseArgs, req.StartTime)
	}
	if req.EndTime != nil {
		baseSQL += " AND order.created_at <= ?"
		baseArgs = append(baseArgs, req.EndTime)
	}

	// 查询总数
	var total int64
	countSQL := "SELECT COUNT(*) " + baseSQL
	if err := s.orderRepo.GetDB().Raw(countSQL, baseArgs...).Scan(&total).Error; err != nil {
		pkg.Error("查询渠道付费总数失败", zap.Error(err))
		return nil, exception.ErrInternalServer.WithMessage("查询付费列表失败")
	}

	// 查询总金额
	var totalAmount float64
	sumSQL := "SELECT COALESCE(SUM(order.amount), 0) " + baseSQL
	if err := s.orderRepo.GetDB().Raw(sumSQL, baseArgs...).Scan(&totalAmount).Error; err != nil {
		pkg.Error("查询渠道付费总金额失败", zap.Error(err))
		return nil, exception.ErrInternalServer.WithMessage("查询付费总金额失败")
	}

	// 分页查询 - 使用原生SQL查询避免GORM结构体映射问题
	offset := (page - 1) * pageSize

	// 构建完整的SQL查询
	sqlQuery := `
		SELECT
			order.id,
			order.order_no,
			order.user_id,
			user.username,
			user.channel,
			order.amount,
			order.payment_method,
			order.payment_status,
			order.created_at,
			user.created_at as user_created_at
		FROM ` + "`order`" + `
		LEFT JOIN user ON order.user_id = user.id
		WHERE order.payment_status = ?
	`

	// 添加渠道权限限制 - 只查询支付成功的订单
	args := []interface{}{enum.PaymentStatusSuccess}
	if !isSuperAdmin && len(adminChannels) > 0 {
		sqlQuery += " AND user.channel IN ?"
		args = append(args, adminChannels)
	}

	// 添加渠道查询条件
	if req.Channel != "" {
		sqlQuery += " AND user.channel = ?"
		args = append(args, req.Channel)
	}

	// 添加时间范围查询
	if req.StartTime != nil {
		sqlQuery += " AND order.created_at >= ?"
		args = append(args, req.StartTime)
	}
	if req.EndTime != nil {
		sqlQuery += " AND order.created_at <= ?"
		args = append(args, req.EndTime)
	}

	// 添加排序和分页
	sqlQuery += " ORDER BY order.created_at DESC LIMIT ? OFFSET ?"
	args = append(args, pageSize, offset)

	// 执行查询
	rows, err := s.orderRepo.GetDB().Raw(sqlQuery, args...).Rows()
	if err != nil {
		pkg.Error("查询渠道付费列表失败", zap.Error(err))
		return nil, exception.ErrInternalServer.WithMessage("查询付费列表失败")
	}
	defer rows.Close()

	var results []struct {
		ID              uint      `json:"id"`
		OrderNo         string    `json:"order_no"`
		UserID          uint      `json:"user_id"`
		Username        string    `json:"username"`
		Channel         string    `json:"channel"`
		Amount          float64   `json:"amount"`
		PaymentMethod   int       `json:"payment_method"`
		PaymentStatus   int       `json:"payment_status"`
		CreatedAt       time.Time `json:"created_at"`
		UserCreatedAt   time.Time `json:"user_created_at"`
	}

	for rows.Next() {
		var result struct {
			ID              uint      `json:"id"`
			OrderNo         string    `json:"order_no"`
			UserID          uint      `json:"user_id"`
			Username        string    `json:"username"`
			Channel         string    `json:"channel"`
			Amount          float64   `json:"amount"`
			PaymentMethod   int       `json:"payment_method"`
			PaymentStatus   int       `json:"payment_status"`
			CreatedAt       time.Time `json:"created_at"`
			UserCreatedAt   time.Time `json:"user_created_at"`
		}

		if err := rows.Scan(
			&result.ID,
			&result.OrderNo,
			&result.UserID,
			&result.Username,
			&result.Channel,
			&result.Amount,
			&result.PaymentMethod,
			&result.PaymentStatus,
			&result.CreatedAt,
			&result.UserCreatedAt,
		); err != nil {
			pkg.Error("扫描查询结果失败", zap.Error(err))
			return nil, exception.ErrInternalServer.WithMessage("查询付费列表失败")
		}

		results = append(results, result)
	}

	// 转换为响应格式
	paymentResponses := make([]vo_admin.ChannelPaymentResponse, len(results))
	for i, result := range results {
		paymentResponses[i] = vo_admin.ChannelPaymentResponse{
			ID:               result.ID,
			OrderNo:          result.OrderNo,
			UserID:           result.UserID,
			Username:         result.Username,
			Channel:          result.Channel,
			Amount:           result.Amount,
			PaymentMethod:    enum.PaymentMethod(result.PaymentMethod),
			PaymentStatus:    enum.PaymentStatus(result.PaymentStatus),
			OrderCreatedAt:   result.CreatedAt,      // 订单创建时间
			UserCreatedAt:    result.UserCreatedAt,  // 用户创建时间
		}
	}

	return &vo_admin.ChannelPaymentsListResponse{
		Total:       total,
		Page:        page,
		PageSize:    pageSize,
		TotalAmount: totalAmount,
		List:        paymentResponses,
	}, nil
}
