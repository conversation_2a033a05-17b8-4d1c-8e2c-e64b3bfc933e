package services

import (
	"context"
	"errors"
	"fmt"
	"time"

	"resume-server/internal/enum"
	"resume-server/internal/repository"
	"resume-server/internal/vo/vo_api"
)

// MembershipValidationService 会员校验服务接口
type MembershipValidationService interface {
	// ValidatePrivilege 校验用户是否可以使用某项权益
	ValidatePrivilege(ctx context.Context, userID uint, isLoggedIn bool, privilegeType enum.PrivilegeType) (*vo_api.ValidationResult, error)
}

// membershipValidationService 会员校验服务实现
type membershipValidationService struct {
	userRepo           repository.UserRepository
	userMembershipRepo repository.UserMembershipRepository
	membershipPlanRepo repository.MembershipPlanRepository
	resumeRepo         repository.ResumeRepository
	aiCallRecordRepo   repository.AICallRecordRepository
}

// NewMembershipValidationService 创建会员校验服务
func NewMembershipValidationService(
	userRepo repository.UserRepository,
	userMembershipRepo repository.UserMembershipRepository,
	membershipPlanRepo repository.MembershipPlanRepository,
	resumeRepo repository.ResumeRepository,
	aiCallRecordRepo repository.AICallRecordRepository,
) MembershipValidationService {
	return &membershipValidationService{
		userRepo:           userRepo,
		userMembershipRepo: userMembershipRepo,
		membershipPlanRepo: membershipPlanRepo,
		resumeRepo:         resumeRepo,
		aiCallRecordRepo:   aiCallRecordRepo,
	}
}

// 硬编码的未登录用户权益配置
var guestPrivilegeConfig = map[enum.PrivilegeType]int{
	enum.PrivilegeResumeDownload: 0,  // 无下载权限
	enum.PrivilegeResumeCreate:   5,  // 5份简历
	enum.PrivilegeAIGenerate:     10, // 10次AI生成
	enum.PrivilegeAIRewrite:      10, // 10次AI改写
	enum.PrivilegeAIOptimize:     1,  // 1次AI优化
	enum.PrivilegeAIDiagnose:     1,  // 1次AI诊断
	enum.PrivilegeAIOneClick:     1,  // 1次AI一键生成
}

// 硬编码的已登录普通用户权益配置
var regularUserPrivilegeConfig = map[enum.PrivilegeType]int{
	enum.PrivilegeResumeDownload: 0,  // 无下载权限
	enum.PrivilegeResumeCreate:   10, // 10份简历
	enum.PrivilegeAIGenerate:     20, // 20次AI生成
	enum.PrivilegeAIRewrite:      20, // 20次AI改写
	enum.PrivilegeAIOptimize:     2,  // 2次AI优化
	enum.PrivilegeAIDiagnose:     2,  // 2次AI诊断
	enum.PrivilegeAIOneClick:     2,  // 2次AI一键生成
}

// ValidatePrivilege 校验用户是否可以使用某项权益
func (s *membershipValidationService) ValidatePrivilege(ctx context.Context, userID uint, isLoggedIn bool, privilegeType enum.PrivilegeType) (*vo_api.ValidationResult, error) {
	// 1. 获取会员信息
	membershipInfo, err := s.getMembershipInfo(ctx, userID, isLoggedIn)
	if err != nil {
		return nil, err
	}

	// 2. 获取权益限制
	var limit int
	var prohibited bool

	switch membershipInfo.UserType {
	case enum.UserTypeGuest:
		limit = guestPrivilegeConfig[privilegeType]
		prohibited = (privilegeType == enum.PrivilegeResumeDownload) // 未登录用户禁止下载

	case enum.UserTypeRegular:
		limit = regularUserPrivilegeConfig[privilegeType]
		prohibited = (privilegeType == enum.PrivilegeResumeDownload) // 普通用户禁止下载

	case enum.UserTypeMember:
		if membershipInfo.MembershipPlan == nil {
			return nil, errors.New("会员信息异常")
		}

		// 从套餐表获取限制
		switch privilegeType {
		case enum.PrivilegeResumeDownload:
			limit = -1 // 会员无限制下载
			prohibited = false
		case enum.PrivilegeResumeCreate:
			limit = membershipInfo.MembershipPlan.ResumeLimit
		case enum.PrivilegeAIGenerate:
			limit = membershipInfo.MembershipPlan.AiGenerateLimit
		case enum.PrivilegeAIRewrite:
			limit = membershipInfo.MembershipPlan.AiRewriteLimit
		case enum.PrivilegeAIOptimize:
			limit = membershipInfo.MembershipPlan.AiOptimizeLimit
		case enum.PrivilegeAIDiagnose:
			limit = membershipInfo.MembershipPlan.AiDiagnoseLimit
		case enum.PrivilegeAIOneClick:
			limit = membershipInfo.MembershipPlan.AiOneClickLimit
		}

		// 0表示无限制
		if limit == 0 {
			limit = -1
		}
	}

	// 3. 检查是否完全禁止
	if prohibited {
		return &vo_api.ValidationResult{
			Allowed: false,
			Reason:  "当前用户等级不支持此功能",
			Limit:   0,
		}, nil
	}

	// 4. 检查无限制情况
	if limit == -1 {
		return &vo_api.ValidationResult{
			Allowed:   true,
			Limit:     -1,
			Remaining: -1,
		}, nil
	}

	// 5. 查询当前使用情况
	currentUsage, err := s.getCurrentUsage(ctx, userID, privilegeType, membershipInfo)
	if err != nil {
		return nil, err
	}

	// 6. 判断是否超出限制
	remaining := limit - currentUsage
	allowed := remaining > 0

	result := &vo_api.ValidationResult{
		Allowed:      allowed,
		CurrentUsage: currentUsage,
		Limit:        limit,
		Remaining:    remaining,
	}

	if !allowed {
		if membershipInfo.UserType == enum.UserTypeGuest {
			result.Reason = "请先登录后使用此功能"
		} else if membershipInfo.UserType == enum.UserTypeRegular {
			result.Reason = "已达到使用上限，请升级会员"
		} else {
			result.Reason = fmt.Sprintf("已达到会员使用上限(%d次)", limit)
		}
	}

	return result, nil
}

// getMembershipInfo 获取用户会员信息
func (s *membershipValidationService) getMembershipInfo(ctx context.Context, userID uint, isLoggedIn bool) (*vo_api.MembershipInfo, error) {

	info := &vo_api.MembershipInfo{
		IsLoggedIn: isLoggedIn,
	}

	if !isLoggedIn {
		// 未登录用户（指纹认证）
		info.UserType = enum.UserTypeGuest
		return info, nil
	}

	// 2. 获取用户信息
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, err
	}

	info.UserType = user.UserType

	// 3. 如果是会员，查询会员信息
	if user.UserType == enum.UserTypeMember {
		membership, err := s.userMembershipRepo.GetActiveByUserID(ctx, userID)
		if err != nil {
			// 查询失败或没有有效会员，降级为普通用户
			info.UserType = enum.UserTypeRegular
			return info, nil
		}

		// 检查会员是否过期
		membership.CheckAndUpdateStatus()
		if membership.MembershipStatus == enum.MembershipStatusExpired {
			// 会员已过期，降级为普通用户
			info.UserType = enum.UserTypeRegular
			return info, nil
		}

		// 获取会员套餐信息
		plan, err := s.membershipPlanRepo.GetPlanByID(ctx, membership.MembershipPlanID)
		if err != nil {
			info.UserType = enum.UserTypeRegular
			return info, nil
		}

		info.MembershipPlan = plan
		info.StartTime = &membership.StartTime
		info.EndTime = &membership.EndTime
		info.IsValid = true
	}

	return info, nil
}

// getCurrentUsage 获取当前使用情况
func (s *membershipValidationService) getCurrentUsage(ctx context.Context, userID uint, privilegeType enum.PrivilegeType, membershipInfo *vo_api.MembershipInfo) (int, error) {
	switch privilegeType {
	case enum.PrivilegeResumeCreate:
		// 简历创建数：查询实际简历数量
		return s.resumeRepo.CountByUserID(ctx, userID)

	case enum.PrivilegeAIGenerate:
		// AI生成：查询ai_call_records表，prompt_type为generate或continue
		promptTypes := []enum.PromptType{enum.PromptTypeGenerate, enum.PromptTypeContinue}
		return s.getAIUsageCount(ctx, userID, promptTypes, membershipInfo)

	case enum.PrivilegeAIRewrite:
		// AI改写：查询ai_call_records表，prompt_type为professional、concise、detailed
		promptTypes := []enum.PromptType{enum.PromptTypeProfessional, enum.PromptTypeConcise, enum.PromptTypeDetailed}
		return s.getAIUsageCount(ctx, userID, promptTypes, membershipInfo)

	case enum.PrivilegeAIOneClick:
		// AI一键生成简历：查询ai_call_records表，prompt_type为generate_resume
		promptTypes := []enum.PromptType{enum.PromptTypeGenerateResume}
		return s.getAIUsageCount(ctx, userID, promptTypes, membershipInfo)

	case enum.PrivilegeAIOptimize:
		// AI优化：查询ai_call_records表，prompt_type为optimize
		promptTypes := []enum.PromptType{enum.PromptTypeOptimize}
		return s.getAIUsageCount(ctx, userID, promptTypes, membershipInfo)

	case enum.PrivilegeAIDiagnose:
		// AI简历打分：查询ai_call_records表，prompt_type为score
		promptTypes := []enum.PromptType{enum.PromptTypeScore}
		return s.getAIUsageCount(ctx, userID, promptTypes, membershipInfo)

	default:
		return 0, nil
	}
}

// getAIUsageCount 获取AI使用次数
func (s *membershipValidationService) getAIUsageCount(ctx context.Context, userID uint, promptTypes []enum.PromptType, membershipInfo *vo_api.MembershipInfo) (int, error) {
	var startTime, endTime *time.Time

	// 如果是会员，按会员有效期计算
	if membershipInfo.UserType == enum.UserTypeMember && membershipInfo.IsValid {
		startTime = membershipInfo.StartTime
		endTime = membershipInfo.EndTime
	}

	return s.aiCallRecordRepo.CountByUserIDAndPromptTypes(ctx, userID, promptTypes, startTime, endTime)
}
