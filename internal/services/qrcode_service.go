package services

import (
	"context"
	"encoding/json"
	"errors"
	"strconv"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"resume-server/config"
	"resume-server/internal/enum"
	"resume-server/internal/exception"
	"resume-server/internal/models"
	"resume-server/internal/pkg"
	"resume-server/internal/repository"
	"resume-server/internal/vo/vo_api"
)

// QrCodeStatus 二维码状态类型
type QrCodeStatus string

const (
	// QrCodeStatusPending 待扫描状态
	QrCodeStatusPending QrCodeStatus = "PENDING"
	// QrCodeStatusScanned 已扫描状态
	QrCodeStatusScanned QrCodeStatus = "SCANNED"
	// QrCodeStatusExpired 已过期状态
	QrCodeStatusExpired QrCodeStatus = "EXPIRED"
)

// QrCodeInfo 二维码信息结构体
type QrCodeInfo struct {
	// UserID 用户ID
	UserID uint `json:"user_id"`
	// Status 二维码状态
	Status QrCodeStatus `json:"status"`
	// OpenID 微信用户OpenID，扫码后才有值
	OpenID string `json:"open_id"`
	// CreatedAt 创建时间
	CreatedAt int64 `json:"created_at"`
	// UpdatedAt 最后更新时间
	UpdatedAt int64 `json:"updated_at"`
}

// QrCodeService 二维码服务接口
type QrCodeService interface {
	// GenerateLoginQrCode 生成登录二维码
	// userID 用户ID，如果传入则将关联到二维码信息中
	GenerateLoginQrCode(ctx context.Context, userID uint) (*vo_api.QrCodeLoginResponse, error)

	// GenerateBindWechatQrCode 生成绑定微信二维码
	// userID 用户ID，必须传入，用于绑定微信账号
	GenerateBindWechatQrCode(ctx context.Context, userID uint) (*vo_api.QrCodeLoginResponse, error)

	// CheckQrCodeStatus 检查二维码状态
	// sceneId 场景值ID
	// userService 用户服务，用于获取和更新用户信息
	// 返回二维码状态响应和错误信息
	CheckQrCodeStatus(ctx context.Context, sceneId string, userService UserService) (*vo_api.QrCodeStatusResponse, error)
}

// qrCodeService 二维码服务实现
type qrCodeService struct {
	redisClient   *redis.Client
	wechatService *pkg.WeChatService
	config        *config.Config
	userRepo      repository.UserRepository
	jwtService    pkg.JWTService
}

// NewQrCodeService 创建二维码服务
func NewQrCodeService(
	redisClient *redis.Client,
	wechatService *pkg.WeChatService,
	cfg *config.Config,
	userRepo repository.UserRepository,
	jwtService pkg.JWTService,
) QrCodeService {
	return &qrCodeService{
		redisClient:   redisClient,
		wechatService: wechatService,
		config:        cfg,
		userRepo:      userRepo,
		jwtService:    jwtService,
	}
}

// GenerateLoginQrCode 生成登录二维码
func (s *qrCodeService) GenerateLoginQrCode(ctx context.Context, userID uint) (*vo_api.QrCodeLoginResponse, error) {
	// 生成唯一的场景值ID，格式为login_timestamp_随机数
	sceneId := "login_" + strconv.FormatInt(time.Now().Unix(), 10) + "_" + strconv.Itoa(time.Now().Nanosecond())

	// 固定使用30分钟过期时间生成二维码
	expireSeconds := 1800

	// 调用微信服务生成临时二维码
	qrCodeUrl, err := s.wechatService.CreateTempQrCodeAndGetUrl(ctx, sceneId, expireSeconds)
	if err != nil {
		pkg.Error("生成登录二维码失败", zap.Error(err))
		return nil, exception.ErrQrCodeGenFailed
	}

	// 将sceneId存储到Redis中，设置10分钟过期时间
	redisKey := "qrcode:login:" + sceneId
	redisExpirationTime := 10 * time.Minute // 10分钟过期

	// 创建二维码信息对象
	now := time.Now().Unix()
	qrCodeInfo := QrCodeInfo{
		UserID:    userID, // 设置用户ID
		Status:    QrCodeStatusPending,
		OpenID:    "", // 初始为空，等待扫码后更新
		CreatedAt: now,
		UpdatedAt: now,
	}

	// 将对象转为JSON字符串
	qrCodeInfoJSON, err := json.Marshal(qrCodeInfo)
	if err != nil {
		pkg.Error("序列化二维码信息失败", zap.Error(err))
		return nil, exception.ErrQrCodeGenFailed
	}

	if err := s.redisClient.Set(ctx, redisKey, string(qrCodeInfoJSON), redisExpirationTime).Err(); err != nil {
		pkg.Error("保存二维码场景值到Redis失败", zap.String("sceneId", sceneId), zap.Error(err))
		return nil, exception.ErrQrCodeGenFailed
	}

	pkg.Info("登录二维码生成成功",
		zap.String("sceneId", sceneId),
		zap.String("redisKey", redisKey),
		zap.Duration("expiration", redisExpirationTime),
		zap.String("status", string(qrCodeInfo.Status)),
	)

	// 创建响应对象
	qrCodeResponse := &vo_api.QrCodeLoginResponse{
		QrCodeUrl: qrCodeUrl,
		SceneId:   sceneId,
	}

	return qrCodeResponse, nil
}

// GenerateBindWechatQrCode 生成绑定微信二维码
func (s *qrCodeService) GenerateBindWechatQrCode(ctx context.Context, userID uint) (*vo_api.QrCodeLoginResponse, error) {
	// 生成唯一的场景值ID，格式为bind_timestamp_随机数
	sceneId := "bind_" + strconv.FormatInt(time.Now().Unix(), 10) + "_" + strconv.Itoa(time.Now().Nanosecond())

	// 固定使用30分钟过期时间生成二维码
	expireSeconds := 1800

	// 调用微信服务生成临时二维码
	qrCodeUrl, err := s.wechatService.CreateTempQrCodeAndGetUrl(ctx, sceneId, expireSeconds)
	if err != nil {
		pkg.Error("生成绑定微信二维码失败", zap.Error(err))
		return nil, exception.ErrQrCodeGenFailed
	}

	// 将sceneId存储到Redis中，设置10分钟过期时间
	redisKey := "qrcode:bind:" + sceneId
	redisExpirationTime := 10 * time.Minute // 10分钟过期

	// 创建二维码信息对象
	now := time.Now().Unix()
	qrCodeInfo := QrCodeInfo{
		UserID:    userID, // 设置用户ID
		Status:    QrCodeStatusPending,
		OpenID:    "", // 初始为空，等待扫码后更新
		CreatedAt: now,
		UpdatedAt: now,
	}

	// 将对象转为JSON字符串
	qrCodeInfoJSON, err := json.Marshal(qrCodeInfo)
	if err != nil {
		pkg.Error("序列化绑定微信二维码信息失败", zap.Error(err))
		return nil, exception.ErrQrCodeGenFailed
	}

	if err := s.redisClient.Set(ctx, redisKey, string(qrCodeInfoJSON), redisExpirationTime).Err(); err != nil {
		pkg.Error("保存绑定微信二维码场景值到Redis失败", zap.String("sceneId", sceneId), zap.Error(err))
		return nil, exception.ErrQrCodeGenFailed
	}

	pkg.Info("绑定微信二维码生成成功",
		zap.String("sceneId", sceneId),
		zap.String("redisKey", redisKey),
		zap.Duration("expiration", redisExpirationTime),
		zap.String("status", string(qrCodeInfo.Status)),
		zap.Uint("userID", userID),
	)

	// 创建响应对象
	qrCodeResponse := &vo_api.QrCodeLoginResponse{
		QrCodeUrl: qrCodeUrl,
		SceneId:   sceneId,
	}

	return qrCodeResponse, nil
}

// CheckQrCodeStatus 检查二维码状态
func (s *qrCodeService) CheckQrCodeStatus(ctx context.Context, sceneId string, userService UserService) (*vo_api.QrCodeStatusResponse, error) {
	// 构建Redis键
	redisKey := "qrcode:login:" + sceneId

	// 获取Redis中存储的二维码信息
	qrCodeInfoStr, err := s.redisClient.Get(ctx, redisKey).Result()
	if err != nil {
		// 如果键不存在，则认为二维码已过期
		if err == redis.Nil {
			pkg.Info("二维码不存在或已过期", zap.String("sceneId", sceneId))
			return &vo_api.QrCodeStatusResponse{
				Status: string(QrCodeStatusExpired),
			}, nil
		}

		// 其他错误
		pkg.Error("获取二维码信息失败", zap.String("sceneId", sceneId), zap.Error(err))
		return nil, exception.ErrQrCodeExpired
	}

	// 解析二维码信息
	var qrCodeInfo QrCodeInfo
	if err := json.Unmarshal([]byte(qrCodeInfoStr), &qrCodeInfo); err != nil {
		pkg.Error("解析二维码信息失败", zap.String("sceneId", sceneId), zap.Error(err))
		return nil, exception.ErrQrCodeParseFailed
	}

	// 根据状态处理
	statusResponse := &vo_api.QrCodeStatusResponse{
		Status: string(qrCodeInfo.Status),
	}

	// 如果状态为已扫码，获取用户信息并生成token
	if qrCodeInfo.Status == QrCodeStatusScanned && qrCodeInfo.OpenID != "" {
		var user *models.User
		var err error

		// 先通过OpenID查询用户，如果存在则直接使用
		user, err = s.userRepo.GetByOpenID(qrCodeInfo.OpenID)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果是非"记录不存在"的错误，则返回错误
			pkg.Error("通过OpenID查询用户失败",
				zap.String("sceneId", sceneId),
				zap.String("openID", qrCodeInfo.OpenID),
				zap.Error(err),
			)
			return nil, exception.ErrUserLoginFailed
		}

		// 如果通过OpenID没有找到用户，并且二维码信息中有用户ID
		if user == nil && qrCodeInfo.UserID > 0 {
			// 使用二维码中关联的用户ID查询用户
			user, err = userService.GetUserByID(qrCodeInfo.UserID)
			if err != nil {
				pkg.Error("通过ID获取用户失败",
					zap.String("sceneId", sceneId),
					zap.Uint("userID", qrCodeInfo.UserID),
					zap.Error(err),
				)
				// 如果获取失败，则继续处理，后面会创建新用户
			} else {
				// 如果找到了用户，检查该用户是否已经绑定了OpenID
				if user.OpenID != "" {
					// 如果用户已经绑定了OpenID，则创建一个新用户
					pkg.Info("用户已经绑定了OpenID，创建新用户",
						zap.Uint("userID", user.ID),
						zap.String("existingOpenID", user.OpenID),
						zap.String("newOpenID", qrCodeInfo.OpenID),
					)

					// 使用LoginByOpenID创建新用户
					newUser, err := userService.LoginByOpenID(qrCodeInfo.OpenID)
					if err != nil {
						pkg.Error("创建新用户失败",
							zap.String("sceneId", sceneId),
							zap.String("openID", qrCodeInfo.OpenID),
							zap.Error(err),
						)
						return nil, exception.ErrQrCodeBindFailed
					}

					// 使用新创建的用户
					user = newUser
				} else {
					// 如果用户没有绑定 OpenID，则绑定当前 OpenID
					user.OpenID = qrCodeInfo.OpenID

					// 如果用户类型为游客，则升级为普通用户
					if user.UserType == enum.UserTypeGuest {
						user.UserType = enum.UserTypeRegular
						pkg.Info("游客用户升级为普通用户",
							zap.Uint("userID", user.ID),
							zap.String("openID", qrCodeInfo.OpenID),
						)
					}

					err = userService.UpdateUser(user)
					if err != nil {
						pkg.Error("更新用户失败",
							zap.String("sceneId", sceneId),
							zap.Uint("userID", user.ID),
							zap.String("openID", qrCodeInfo.OpenID),
							zap.Error(err),
						)
						// 即使更新失败，仍然继续使用该用户
					}
				}
			}
		}

		// 如果仍然没有找到用户，则返回错误
		if user == nil {
			pkg.Error("无法找到或创建用户",
				zap.String("sceneId", sceneId),
				zap.String("openID", qrCodeInfo.OpenID),
				zap.Uint("userID", qrCodeInfo.UserID),
			)
			return nil, exception.ErrQrCodeBindFailed
		}

		// 生成JWT token
		token, err := s.jwtService.GenerateToken(user.ID)
		if err != nil {
			pkg.Error("生成JWT Token失败", zap.Error(err))
			return nil, exception.ErrQrCodeBindFailed
		}

		// 设置token到响应中
		statusResponse.Token = token

		pkg.Info("用户微信扫码登录成功",
			zap.String("sceneId", sceneId),
			zap.String("openID", qrCodeInfo.OpenID),
			zap.Uint("userId", user.ID),
		)
	}

	return statusResponse, nil
}
