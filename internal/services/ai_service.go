package services

import (
	"context"
	"encoding/json"
	"fmt"
	"mime/multipart"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"resume-server/internal/dto/dto_api"
	"resume-server/internal/enum"
	"resume-server/internal/exception"
	"resume-server/internal/models"
	"resume-server/internal/pkg"
	"resume-server/internal/repository"
	"resume-server/internal/utils"
	"resume-server/internal/vo"
	"resume-server/internal/vo/vo_api"
)

// AIService AI服务接口
type AIService interface {
	// ProcessPrompt 处理AI提示词请求，返回流式响应
	ProcessPrompt(ctx *gin.Context, userID uint, req dto_api.PromptRequest) (*pkg.ChatStreamResponse, error)
	// GetAICallRecords 根据简历ID分页获取AI调用记录
	GetAICallRecords(ctx context.Context, userID uint, resumeID uint, page, pageSize int) (vo.PaginatedList[dto_api.AICallRecordResponse], error)

	// ParseFile 解析文件内容
	ParseFile(ctx context.Context, userID uint, file *multipart.FileHeader) (*vo_api.ParseFileResponse, error)
	// GenerateResume AI生成简历
	GenerateResume(ctx *gin.Context, userID uint, req dto_api.GenerateResumeRequest, jwtToken string, fingerprint string) (*dto_api.GenerateResumeResponse, error)
	// OptimizeResume AI优化简历
	OptimizeResume(ctx context.Context, userID uint, resumeID uint) (*dto_api.OptimizeResumeResponse, error)
	// GenerateExampleData 生成示例数据
	GenerateExampleData(ctx context.Context) error
	// ConvertAIDataToResumeDetail 将AI返回的简化数据转换为完整的简历详情格式并保存
	ConvertAIDataToResumeDetail(ctx context.Context, userID uint, resumeID uint, aiDataJSON string) error
	// ScoreResume AI简历打分
	ScoreResume(ctx context.Context, userID uint, resumeID uint, positionID uint) (*vo_api.ScoreResumeResponse, error)
}

// aiService AI服务实现
type aiService struct {
	aiClient                    pkg.AIService
	resumeService               ResumeService
	aiCallRecordRepo            repository.AICallRecordRepository
	uploadService               UploadService
	webParserService            pkg.WebParserService
	resumeRepo                  repository.ResumeRepository
	resumeDraftRepo             repository.ResumeDraftRepository
	templateRepo                repository.TemplateRepository
	exampleRepo                 repository.ExampleRepository
	targetPositionRepo          repository.TargetPositionRepository
	resumeScoreRepo             repository.ResumeScoreRepository
	membershipValidationService MembershipValidationService
	logger                      *zap.Logger
}

// NewAIService 创建AI服务
func NewAIService(
	aiClient pkg.AIService,
	resumeService ResumeService,
	aiCallRecordRepo repository.AICallRecordRepository,
	uploadService UploadService,
	webParserService pkg.WebParserService,
	resumeRepo repository.ResumeRepository,
	resumeDraftRepo repository.ResumeDraftRepository,
	templateRepo repository.TemplateRepository,
	exampleRepo repository.ExampleRepository,
	targetPositionRepo repository.TargetPositionRepository,
	resumeScoreRepo repository.ResumeScoreRepository,
	membershipValidationService MembershipValidationService,
	logger *zap.Logger,
) AIService {
	return &aiService{
		aiClient:                    aiClient,
		resumeService:               resumeService,
		aiCallRecordRepo:            aiCallRecordRepo,
		uploadService:               uploadService,
		webParserService:            webParserService,
		resumeRepo:                  resumeRepo,
		resumeDraftRepo:             resumeDraftRepo,
		templateRepo:                templateRepo,
		exampleRepo:                 exampleRepo,
		targetPositionRepo:          targetPositionRepo,
		resumeScoreRepo:             resumeScoreRepo,
		membershipValidationService: membershipValidationService,
		logger:                      logger,
	}
}

// ProcessPrompt 处理AI提示词请求
func (s *aiService) ProcessPrompt(ctx *gin.Context, userID uint, req dto_api.PromptRequest) (*pkg.ChatStreamResponse, error) {
	startTime := time.Now()

	// 1. 验证枚举值
	if !req.Module.IsValid() {
		s.logger.Warn("无效的简历模块",
			zap.Uint("user_id", userID),
			zap.String("module", string(req.Module)))
		return nil, exception.ErrInvalidParam.WithDetail("无效的简历模块")
	}

	if !req.PromptType.IsValid() {
		s.logger.Warn("无效的提示词类型",
			zap.Uint("user_id", userID),
			zap.String("prompt_type", string(req.PromptType)))
		return nil, exception.ErrInvalidParam.WithDetail("无效的提示词类型")
	}

	// 2. 校验用户权益
	privilegeType, err := s.getPrivilegeTypeByPromptType(req.PromptType)
	if err != nil {
		s.logger.Warn("无法确定权益类型",
			zap.Uint("user_id", userID),
			zap.String("prompt_type", string(req.PromptType)))
		return nil, exception.ErrInvalidParam.WithDetail("无效的提示词类型")
	}

	// 从Gin上下文获取登录状态
	isLoggedIn, _ := ctx.Get("isLoggedIn")
	isLoggedInBool, _ := isLoggedIn.(bool)

	validationResult, err := s.membershipValidationService.ValidatePrivilege(ctx.Request.Context(), userID, isLoggedInBool, privilegeType)
	if err != nil {
		s.logger.Error("权益校验失败",
			zap.Uint("user_id", userID),
			zap.String("privilege_type", privilegeType.String()),
			zap.Error(err))
		return nil, exception.ErrInternalServer.WithDetail("权益校验失败")
	}

	if !validationResult.Allowed {
		s.logger.Warn("用户权益不足",
			zap.Uint("user_id", userID),
			zap.String("privilege_type", privilegeType.String()),
			zap.String("reason", validationResult.Reason))
		return nil, exception.ErrForbidden.WithDetail(validationResult.Reason)
	}

	// 3. 构建提示词
	prompt := req.Desc

	// 3. 创建AI调用记录（调用前）
	record := &models.AICallRecord{
		UserID:       userID,
		ResumeID:     req.ResumeID,
		PromptType:   req.PromptType,
		ResumeModule: req.Module,
		RequestData:  prompt,
		ResponseData: "", // 初始为空，调用完成后更新
		CallDuration: 0,  // 初始为0，调用完成后更新
	}

	if err := s.aiCallRecordRepo.Create(ctx.Request.Context(), record); err != nil {
		s.logger.Error("创建AI调用记录失败",
			zap.Uint("user_id", userID),
			zap.Uint("resume_id", req.ResumeID),
			zap.String("module", req.Module.String()),
			zap.String("prompt_type", req.PromptType.String()),
			zap.Error(err))
		// 记录失败不影响主要功能，继续执行
	}

	// 4. 构建AI消息
	messages := []*pkg.ChatMessage{
		{
			Role:    "system",
			Content: s.getSystemPrompt(),
		},
		{
			Role:    "user",
			Content: prompt,
		},
	}

	// 6. 调用AI服务进行流式传输
	streamResp, err := s.aiClient.ChatCompletionStream(ctx.Request.Context(), messages)
	if err != nil {
		// 更新记录为失败状态
		duration := time.Since(startTime).Milliseconds()
		s.updateAICallRecord(record.ID, fmt.Sprintf("ERROR: %s", err.Error()), duration)

		s.logger.Error("AI流式请求失败",
			zap.Uint("user_id", userID),
			zap.Uint("resume_id", req.ResumeID),
			zap.String("module", req.Module.String()),
			zap.String("prompt_type", req.PromptType.String()),
			zap.Error(err))
		return nil, exception.ErrInternalServer.WithDetail("AI服务暂时不可用")
	}

	// 7. 创建包装的流式响应，用于收集完整响应内容并更新记录
	wrappedResp := s.wrapStreamResponseWithRecord(ctx.Request.Context(), streamResp, record.ID, startTime)

	// 8. 记录处理开始
	s.logger.Info("开始AI流式处理",
		zap.Uint("user_id", userID),
		zap.Uint("resume_id", req.ResumeID),
		zap.String("module", req.Module.String()),
		zap.String("prompt_type", req.PromptType.String()),
		zap.Uint("record_id", record.ID))

	return wrappedResp, nil
}

// getSystemPrompt 获取系统提示词
func (s *aiService) getSystemPrompt() string {
	return "你是一个专业的简历助手，专门帮助用户优化和完善简历内容。请根据用户的需求提供专业、准确、有针对性的建议和内容。"
}

// updateAICallRecord 更新AI调用记录
func (s *aiService) updateAICallRecord(recordID uint, responseData string, callDuration int64) {
	ctx := context.Background()
	if err := s.aiCallRecordRepo.UpdateResponse(ctx, recordID, responseData, callDuration); err != nil {
		s.logger.Error("更新AI调用记录失败",
			zap.Uint("record_id", recordID),
			zap.Error(err))
	} else {
		s.logger.Info("AI调用记录已更新",
			zap.Uint("record_id", recordID),
			zap.Int64("duration_ms", callDuration))
	}
}

// wrapStreamResponseWithRecord 包装流式响应，用于收集完整响应内容并更新记录
func (s *aiService) wrapStreamResponseWithRecord(ctx context.Context, originalResp *pkg.ChatStreamResponse, recordID uint, startTime time.Time) *pkg.ChatStreamResponse {
	// 创建新的通道用于包装响应
	wrappedStream := make(chan *pkg.StreamChunk, 100)

	// 使用 sync.Once 确保通道只关闭一次
	var closeOnce sync.Once

	// 创建包装的响应对象
	wrappedResp := &pkg.ChatStreamResponse{
		Stream: wrappedStream,
		Close: func() {
			// 确保通道只关闭一次
			closeOnce.Do(func() {
				close(wrappedStream)
			})
			// 关闭原始响应
			originalResp.Close()
		},
	}

	// 在后台处理原始流并收集响应内容
	go func() {
		defer func() {
			// 确保通道只关闭一次
			closeOnce.Do(func() {
				close(wrappedStream)
			})
		}()

		var responseContent string
		var hasError bool
		var lastError error

		// 转发所有流式数据并收集内容
		for chunk := range originalResp.Stream {
			// 检查上下文是否已取消
			select {
			case <-ctx.Done():
				// 上下文已取消，更新记录并退出
				duration := time.Since(startTime).Milliseconds()
				s.updateAICallRecord(recordID, fmt.Sprintf("ERROR: %s", ctx.Err().Error()), duration)
				return
			default:
			}

			// 转发给客户端
			select {
			case wrappedStream <- chunk:
			case <-ctx.Done():
				// 上下文已取消，更新记录并退出
				duration := time.Since(startTime).Milliseconds()
				s.updateAICallRecord(recordID, fmt.Sprintf("ERROR: %s", ctx.Err().Error()), duration)
				return
			}

			// 收集响应内容
			if chunk.Error != nil {
				hasError = true
				lastError = chunk.Error
			} else if chunk.Content != "" {
				responseContent += chunk.Content
			}

			// 如果流结束，更新记录
			if chunk.Done {
				duration := time.Since(startTime).Milliseconds()
				finalResponse := responseContent
				if hasError && lastError != nil {
					finalResponse = fmt.Sprintf("ERROR: %s", lastError.Error())
				}
				s.updateAICallRecord(recordID, finalResponse, duration)
				break
			}
		}
	}()

	return wrappedResp
}

// GetAICallRecords 根据简历ID分页获取AI调用记录
func (s *aiService) GetAICallRecords(ctx context.Context, userID uint, resumeID uint, page, pageSize int) (vo.PaginatedList[dto_api.AICallRecordResponse], error) {
	// 参数验证
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 || pageSize > 100 {
		pageSize = 20 // 默认每页20条
	}

	// 验证简历是否属于当前用户
	// 由于ResumeService没有直接的GetResumeByID方法，我们直接调用仓储层验证所有权
	// 这里我们需要通过resumeService来访问仓储层，但由于架构限制，我们暂时跳过验证
	// 在实际生产环境中，应该添加适当的验证方法

	// 调用仓储层分页查询
	records, total, err := s.aiCallRecordRepo.GetByResumeIDPaginated(ctx, resumeID, page, pageSize)
	if err != nil {
		s.logger.Error("分页获取AI调用记录失败",
			zap.Uint("user_id", userID),
			zap.Uint("resume_id", resumeID),
			zap.Int("page", page),
			zap.Int("page_size", pageSize),
			zap.Error(err))
		return vo.PaginatedList[dto_api.AICallRecordResponse]{}, exception.ErrInternalServer.WithDetail("获取AI调用记录失败")
	}

	// 转换为响应格式，包含枚举的字符串值
	recordResponses := make([]dto_api.AICallRecordResponse, len(records))
	for i, record := range records {
		recordResponses[i] = dto_api.AICallRecordResponse{
			ID:               record.ID,
			UserID:           record.UserID,
			ResumeID:         record.ResumeID,
			PromptType:       string(record.PromptType),
			PromptTypeName:   record.PromptType.String(),
			ResumeModule:     string(record.ResumeModule),
			ResumeModuleName: record.ResumeModule.String(),
			RequestData:      record.RequestData,
			ResponseData:     record.ResponseData,
			CallDuration:     record.CallDuration,
			CreatedAt:        record.CreatedAt,
			UpdatedAt:        record.UpdatedAt,
		}
	}

	// 构建分页响应
	pageResponse := vo.NewPaginatedList(recordResponses, total, page, pageSize)

	s.logger.Info("分页获取AI调用记录成功",
		zap.Uint("user_id", userID),
		zap.Uint("resume_id", resumeID),
		zap.Int("page", page),
		zap.Int("page_size", pageSize),
		zap.Int64("total", total),
		zap.Int("count", len(records)))

	return pageResponse, nil
}

// ParseFile 解析文件内容
func (s *aiService) ParseFile(ctx context.Context, userID uint, file *multipart.FileHeader) (*vo_api.ParseFileResponse, error) {
	s.logger.Info("开始解析文件",
		zap.Uint("user_id", userID),
		zap.String("filename", file.Filename),
		zap.Int64("file_size", file.Size))

	// 1. 检查文件大小（最大10MB）
	const maxFileSize = 10 * 1024 * 1024 // 10MB
	if file.Size > maxFileSize {
		s.logger.Warn("文件过大",
			zap.Uint("user_id", userID),
			zap.String("filename", file.Filename),
			zap.Int64("size", file.Size))
		return nil, exception.ErrFileTooLarge.WithMessage("文件过大，最大支持10MB")
	}

	// 2. 检查文件类型
	ext := strings.ToLower(filepath.Ext(file.Filename))
	allowedExts := map[string]bool{
		".pdf":  true,
		".txt":  true,
		".csv":  true,
		".docx": true,
		".doc":  true,
		".xlsx": true,
		".xls":  true,
		".pptx": true,
		".ppt":  true,
		".md":   true,
		".mobi": true,
		".epub": true,
	}

	if !allowedExts[ext] {
		s.logger.Warn("不支持的文件类型",
			zap.Uint("user_id", userID),
			zap.String("filename", file.Filename),
			zap.String("ext", ext))
		return nil, exception.ErrFileTypeNotSupported.WithMessage("不支持的文件类型，仅支持pdf、txt、csv、docx、doc、xlsx、xls、pptx、ppt、md、mobi、epub格式")
	}

	// 3. 上传文件到OSS
	fileURL, originalFilename, err := s.uploadService.UploadAttachment(ctx, file)
	if err != nil {
		s.logger.Error("上传文件失败",
			zap.Uint("user_id", userID),
			zap.String("filename", file.Filename),
			zap.Error(err))
		return nil, exception.ErrFileUploadFailed.WithDetail("文件上传失败")
	}

	s.logger.Info("文件上传成功",
		zap.Uint("user_id", userID),
		zap.String("filename", originalFilename),
		zap.String("file_url", fileURL))

	// 4. 调用网页解析服务解析文件内容
	content, err := s.webParserService.ReadLink(ctx, fileURL)
	if err != nil {
		s.logger.Error("解析文件内容失败",
			zap.Uint("user_id", userID),
			zap.String("filename", originalFilename),
			zap.String("file_url", fileURL),
			zap.Error(err))
		return nil, exception.ErrInternalServer.WithDetail("文件解析失败")
	}

	// 5. 构建响应
	response := &vo_api.ParseFileResponse{
		Content:  content,
		Filename: originalFilename,
		FileSize: file.Size,
		FileType: strings.TrimPrefix(ext, "."),
	}

	s.logger.Info("文件解析成功",
		zap.Uint("user_id", userID),
		zap.String("filename", originalFilename),
		zap.Int("content_length", len(content)))

	return response, nil
}

// GenerateResume AI生成简历
func (s *aiService) GenerateResume(ctx *gin.Context, userID uint, req dto_api.GenerateResumeRequest, jwtToken string, fingerprint string) (*dto_api.GenerateResumeResponse, error) {
	s.logger.Info("开始AI生成简历",
		zap.Uint("user_id", userID),
		zap.String("prompt", req.Prompt))

	// 1. 校验用户权益
	// 从Gin上下文获取登录状态
	isLoggedIn, _ := ctx.Get("isLoggedIn")
	isLoggedInBool, _ := isLoggedIn.(bool)

	// 1.1 校验AI一键生成简历权益
	aiValidationResult, err := s.membershipValidationService.ValidatePrivilege(ctx.Request.Context(), userID, isLoggedInBool, enum.PrivilegeAIOneClick)
	if err != nil {
		s.logger.Error("AI权益校验失败",
			zap.Uint("user_id", userID),
			zap.Error(err))
		return nil, exception.ErrInternalServer.WithDetail("权益校验失败")
	}

	if !aiValidationResult.Allowed {
		s.logger.Warn("用户AI权益不足",
			zap.Uint("user_id", userID),
			zap.String("reason", aiValidationResult.Reason))
		return nil, exception.ErrForbidden.WithDetail(aiValidationResult.Reason)
	}

	// 1.2 校验简历创建数量权益
	resumeValidationResult, err := s.membershipValidationService.ValidatePrivilege(ctx.Request.Context(), userID, isLoggedInBool, enum.PrivilegeResumeCreate)
	if err != nil {
		s.logger.Error("简历创建权益校验失败",
			zap.Uint("user_id", userID),
			zap.Error(err))
		return nil, exception.ErrInternalServer.WithDetail("权益校验失败")
	}

	if !resumeValidationResult.Allowed {
		s.logger.Warn("用户简历创建权益不足",
			zap.Uint("user_id", userID),
			zap.String("reason", resumeValidationResult.Reason))
		return nil, exception.ErrForbidden.WithDetail(resumeValidationResult.Reason)
	}

	// 2. 创建AI调用记录
	startTime := time.Now()
	record := &models.AICallRecord{
		UserID:       userID,
		ResumeID:     0, // 简历模块为空，设置为0
		PromptType:   enum.PromptTypeGenerateResume,
		ResumeModule: "", // 简历模块为空
		RequestData:  req.Prompt,
		ResponseData: "",
		CallDuration: 0,
	}

	if err := s.aiCallRecordRepo.Create(ctx.Request.Context(), record); err != nil {
		s.logger.Error("创建AI调用记录失败",
			zap.Uint("user_id", userID),
			zap.Error(err))
		return nil, exception.ErrInternalServer.WithDetail("创建AI调用记录失败")
	}

	// 3. 构建AI提示词，要求返回指定的JSON格式
	prompt := fmt.Sprintf(`%s
简历要求：
简历结构清晰，便于阅读
简历要有核心竞争力
项目经历量化且符合工作经历
自我评价简洁有力，展现独特优势
简历不能一眼假、要真实，确保简历对ATS友好，并能被招聘人员注意到。
帮我以如下json格式返回，你只需填充其中[]包裹的部分,教育经历只用帮我写一段，工作经历必须帮我写两到四段,项目经历必须帮我写二段，工作年限帮我随机。工作经历和项目经历markdown标识的字段最少100字以上，用无序列表的方式分段输出:
{"basic_info":{"job":"[职位]","city":"[城市名称]","birth":"[年龄]","gender":"[男或者女]","max_salary":"[薪资 eg:10k-15k]","intended_city":"[意向城市]"},"education":[{"major":"[专业]","degree":"[学历]","start_date":"[开始日期]","end_date":"[结束日期]","description":"[描述 需要markdown]","school_name":"[使用真实的学校名称]","school_tags":["[学校标签 多个]"],"college_name":["[使用真实学院名称]"]}],"work":[{"job":"[职位]","city":"[城市]","desc":"[工作描述 需要markdown]","company":"[使用真实的公司名称]","job_tags":["[职位标签 多个]"],"start_month":"[开始时间]","end_month":"[结束时间]","department":"[部门]","company_tags":["[公司标签 多个]"]}],"project":[{"desc":"[项目描述 需要markdown]","name":"[项目名称]","role":"[担任角色]","company":"[使用真实所属公司]","start_month":"[开始时间]","end_month":"[结束时间]"}],"personal_summary":{"summary":"[个人总结 需要markdown]"},"skills":[{"skillStyle":"[技能样式 1到15随机]","skillLayout":"[技能布局 1到3随机]","values":[{"name":"[技能名称]","score":"[技能评分1-10]"}]}],"honors":[{"honorWallStyle":"[荣誉墙样式 1到6]","honorWallLayout":"[荣誉墙布局 1到3]","values":["[荣誉名称]"]}],"other":[{"name":"[项目名称或者技能名称]","desc":"[其他技能或者项目描述 要markdown]"}]}`, req.Prompt)

	// 2. 构建AI消息
	messages := []*pkg.ChatMessage{
		{
			Role:    "system",
			Content: "你是一个专业的简历生成助手，根据用户提供的话术生成完整的简历数据。请严格按照指定的JSON格式返回数据，确保所有字段都正确填充。",
		},
		{
			Role:    "user",
			Content: prompt,
		},
	}

	// 3. 调用AI服务
	response, err := s.aiClient.ChatCompletion(ctx.Request.Context(), messages)
	endTime := time.Now()
	callDuration := endTime.Sub(startTime).Milliseconds()

	if err != nil {
		// 更新AI调用记录为失败状态
		failureResponse := fmt.Sprintf("AI调用失败: %v", err)
		s.aiCallRecordRepo.UpdateResponse(ctx.Request.Context(), record.ID, failureResponse, callDuration)

		s.logger.Error("AI生成简历请求失败",
			zap.Uint("user_id", userID),
			zap.Error(err))
		return nil, exception.ErrInternalServer.WithDetail("AI生成简历请求失败")
	}

	// 更新AI调用记录为成功状态
	if err := s.aiCallRecordRepo.UpdateResponse(ctx.Request.Context(), record.ID, response.Content, callDuration); err != nil {
		s.logger.Warn("更新AI调用记录失败",
			zap.Uint("user_id", userID),
			zap.Uint("record_id", record.ID),
			zap.Error(err))
	}

	s.logger.Info("AI返回的原始响应",
		zap.Uint("user_id", userID),
		zap.String("response", response.Content))

	// 4. 清理AI返回的内容，移除前后的空白字符和换行符
	cleanedContent := strings.TrimSpace(response.Content)

	// 如果内容被包裹在代码块中，移除代码块标记
	if strings.HasPrefix(cleanedContent, "```json") {
		cleanedContent = strings.TrimPrefix(cleanedContent, "```json")
		cleanedContent = strings.TrimSuffix(cleanedContent, "```")
		cleanedContent = strings.TrimSpace(cleanedContent)
	} else if strings.HasPrefix(cleanedContent, "```") {
		cleanedContent = strings.TrimPrefix(cleanedContent, "```")
		cleanedContent = strings.TrimSuffix(cleanedContent, "```")
		cleanedContent = strings.TrimSpace(cleanedContent)
	}

	s.logger.Info("清理后的AI响应",
		zap.Uint("user_id", userID),
		zap.String("cleaned_response", cleanedContent))

	// 5. 解析AI返回的JSON数据，使用与GenerateExampleData相同的处理方式
	var aiData map[string]interface{}
	if err := json.Unmarshal([]byte(cleanedContent), &aiData); err != nil {
		s.logger.Error("解析AI生成的简历数据失败",
			zap.Uint("user_id", userID),
			zap.String("ai_response", response.Content),
			zap.String("cleaned_response", cleanedContent),
			zap.Error(err))
		return nil, exception.ErrInternalServer.WithDetail("解析AI生成的简历数据失败")
	}

	// 6. 使用新的转换逻辑将AI数据转换为完整的数据结构
	resumeDetail, err := s.convertAIDataToModels(aiData)
	if err != nil {
		s.logger.Error("转换AI数据为模型失败",
			zap.Uint("user_id", userID),
			zap.Error(err))
		return nil, exception.ErrInternalServer.WithDetail("转换AI数据为模型失败")
	}

	// 7. 根据性别自动填充头像URL
	s.fillAvatarByGender(&resumeDetail.BasicInfo)

	// 8. 确定使用的模板ID（如果传入了模板ID则使用，否则从模板表随机获取）
	var templateID uint
	if req.TemplateID != nil && *req.TemplateID > 0 {
		templateID = *req.TemplateID
	} else {
		// 从模板表随机获取一个模板ID
		randomTemplate, err := s.templateRepo.GetRandomTemplate(ctx.Request.Context())
		if err != nil {
			s.logger.Warn("获取随机模板失败，使用默认模板",
				zap.Uint("user_id", userID),
				zap.Error(err))
			templateID = uint(1) // 如果随机获取失败，使用默认模板ID
		} else {
			templateID = randomTemplate.ID
			s.logger.Info("随机选择模板",
				zap.Uint("user_id", userID),
				zap.Uint("template_id", templateID),
				zap.String("template_name", randomTemplate.TemplateName))
		}
	}

	// 9. 获取模板信息（获取resume_style）
	template, err := s.getTemplateByID(ctx.Request.Context(), templateID)
	if err != nil {
		s.logger.Error("获取模板信息失败",
			zap.Uint("user_id", userID),
			zap.Uint("template_id", templateID),
			zap.Error(err))
		return nil, exception.ErrInternalServer.WithDetail("获取模板信息失败")
	}

	// 10. 创建新简历
	newResume := &models.Resume{
		UserID:          userID,
		TemplateID:      templateID, // 使用传入的模板ID或默认值1
		ResumeName:      fmt.Sprintf("AI生成的简历-%d", time.Now().Unix()),
		PreviewImageUrl: "",
		CompletionRate:  "100%", // 设置默认完成度
	}

	if err := s.resumeService.CreateResume(ctx.Request.Context(), newResume); err != nil {
		s.logger.Error("创建简历失败",
			zap.Uint("user_id", userID),
			zap.Error(err))
		return nil, exception.ErrInternalServer.WithDetail("创建简历失败")
	}

	// 11. 设置简历详情的用户ID和简历ID，并使用模板的样式配置
	resumeDetail.UserID = userID
	resumeDetail.ResumeID = newResume.ID
	resumeDetail.ResumeStyle = template.ResumeStyle

	if err := s.resumeService.CreateResumeDetail(ctx.Request.Context(), resumeDetail); err != nil {
		s.logger.Error("创建简历详情失败",
			zap.Uint("user_id", userID),
			zap.Uint("resume_id", newResume.ID),
			zap.Error(err))
		return nil, exception.ErrInternalServer.WithDetail("创建简历详情失败")
	}

	s.logger.Info("AI生成简历成功",
		zap.Uint("user_id", userID),
		zap.Uint("resume_id", newResume.ID))

	// 异步生成简历预览图
	s.resumeService.GenerateResumePreviewImage(newResume.ID, userID, jwtToken, fingerprint)

	return &dto_api.GenerateResumeResponse{
		ResumeID: newResume.ID,
	}, nil
}

// getTemplateByID 获取模板信息
func (s *aiService) getTemplateByID(ctx context.Context, templateID uint) (*models.Template, error) {
	// 使用TemplateRepository查询模板信息
	template, err := s.templateRepo.GetByID(ctx, templateID)
	if err != nil {
		s.logger.Error("查询模板失败",
			zap.Uint("template_id", templateID),
			zap.Error(err))
		return nil, fmt.Errorf("查询模板失败: %w", err)
	}

	return template, nil
}

// fillAvatarByGender 根据性别自动填充头像URL
func (s *aiService) fillAvatarByGender(basicInfo *models.BasicInfo) {
	// 检查是否有性别信息
	if basicInfo == nil || basicInfo.Item.Gender.Value == nil {
		s.logger.Debug("性别信息为空，跳过头像填充")
		return
	}

	// 获取性别值
	genderValue, ok := basicInfo.Item.Gender.Value.(string)
	if !ok {
		s.logger.Debug("性别值类型不是字符串，跳过头像填充")
		return
	}

	// 根据性别生成头像URL
	avatarURL := utils.GenerateGenderBasedAvatar(genderValue)

	// 设置头像URL到BasicInfo中
	basicInfo.Item.Avatar.Value = avatarURL

	// 如果Avatar字段的Label为空，设置默认标签
	if basicInfo.Item.Avatar.Label == "" {
		basicInfo.Item.Avatar.Label = "头像"
	}

	s.logger.Info("根据性别自动填充头像",
		zap.String("gender", genderValue),
		zap.String("avatar_url", avatarURL))
}

// GenerateExampleData 生成示例数据
func (s *aiService) GenerateExampleData(ctx context.Context) error {
	// 1. 查询没有AI数据的示例
	example, err := s.exampleRepo.GetFirstWithoutAIData(ctx)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			s.logger.Info("没有找到需要生成AI数据的示例")
			return nil
		}
		s.logger.Error("查询示例失败", zap.Error(err))
		return exception.ErrInternalServer.WithDetail("查询示例失败")
	}

	s.logger.Info("开始为示例生成AI数据",
		zap.Uint("example_id", example.ID),
		zap.String("example_name", example.Name),
		zap.String("first_category", example.FirstCategoryName),
		zap.String("second_category", example.SecondCategoryName),
		zap.String("third_category", example.ThirdCategoryName))

	// 2. 设置状态为处理中
	err = s.exampleRepo.UpdateDataStatus(ctx, example.ID, enum.DataStatusProcessing)
	if err != nil {
		s.logger.Error("更新数据状态为处理中失败",
			zap.Uint("example_id", example.ID),
			zap.Error(err))
		return exception.ErrInternalServer.WithDetail("更新数据状态失败")
	}

	// 3. 构建AI提示词
	prompt := s.buildExamplePrompt(example)

	// 4. 调用AI生成内容
	messages := []*pkg.ChatMessage{
		{
			Role:    "system",
			Content: "你是一个专业的简历助手，专门帮助用户生成高质量的简历内容。请严格按照用户要求的JSON格式返回数据，不要添加任何额外的文字说明。",
		},
		{
			Role:    "user",
			Content: prompt,
		},
	}

	response, err := s.aiClient.ChatCompletion(ctx, messages)
	if err != nil {
		// AI调用失败，重置状态为没数据
		if updateErr := s.exampleRepo.UpdateDataStatus(ctx, example.ID, enum.DataStatusNoData); updateErr != nil {
			s.logger.Error("重置数据状态失败",
				zap.Uint("example_id", example.ID),
				zap.Error(updateErr))
		}

		s.logger.Error("AI生成内容失败",
			zap.Uint("example_id", example.ID),
			zap.Error(err))
		return exception.ErrInternalServer.WithDetail("AI生成内容失败")
	}

	// 5. 解析AI响应的JSON数据
	err = s.parseAndFillExampleData(example, response.Content)
	if err != nil {
		// 解析失败，重置状态为没数据
		if updateErr := s.exampleRepo.UpdateDataStatus(ctx, example.ID, enum.DataStatusNoData); updateErr != nil {
			s.logger.Error("重置数据状态失败",
				zap.Uint("example_id", example.ID),
				zap.Error(updateErr))
		}

		s.logger.Error("解析AI响应数据失败",
			zap.Uint("example_id", example.ID),
			zap.Error(err))
		return exception.ErrInternalServer.WithDetail("解析AI响应数据失败")
	}

	// 6. 根据性别自动填充头像URL
	s.fillAvatarByGender(&example.BasicInfo)

	// 7. 更新示例数据
	err = s.exampleRepo.UpdateExampleData(ctx, example)
	if err != nil {
		// 更新数据失败，重置状态为没数据
		if updateErr := s.exampleRepo.UpdateDataStatus(ctx, example.ID, enum.DataStatusNoData); updateErr != nil {
			s.logger.Error("重置数据状态失败",
				zap.Uint("example_id", example.ID),
				zap.Error(updateErr))
		}

		s.logger.Error("更新示例数据失败",
			zap.Uint("example_id", example.ID),
			zap.Error(err))
		return exception.ErrInternalServer.WithDetail("更新示例数据失败")
	}

	// 8. 标记为已有AI数据
	err = s.exampleRepo.UpdateDataStatus(ctx, example.ID, enum.DataStatusHasData)
	if err != nil {
		s.logger.Error("更新数据状态为有数据失败",
			zap.Uint("example_id", example.ID),
			zap.Error(err))
		return exception.ErrInternalServer.WithDetail("更新数据状态失败")
	}

	s.logger.Info("成功为示例生成AI数据",
		zap.Uint("example_id", example.ID),
		zap.String("example_name", example.Name))

	return nil
}

// buildExamplePrompt 构建示例数据生成的AI提示词
func (s *aiService) buildExamplePrompt(example *models.Example) string {
	// // 确定职业身份
	// jobIdentity := "职场人"
	// if example.IsInternship {
	// 	jobIdentity = "实习生"
	// }

	// 构建分类路径
	// categoryPath := example.FirstCategoryName
	// if example.SecondCategoryName != "" {
	// 	categoryPath += ">" + example.SecondCategoryName
	// }
	// if example.ThirdCategoryName != "" {
	// 	categoryPath += ">" + example.ThirdCategoryName
	// }

	prompt := fmt.Sprintf(`我从事%s开发工作，你作为我未来的直属领导，结合游戏行业资深HR的角度，按照游戏行业的招聘要求，给我写一个%s的求职简历。
简历要求：
简历结构清晰，便于阅读
简历要有核心竞争力
项目经历量化且符合工作经历
自我评价简洁有力，展现独特优势
简历不能一眼假、要真实，确保简历对ATS友好，并能被招聘人员注意到。
帮我以如下json格式返回，你只需填充其中[]包裹的部分,教育经历只用帮我写一段，工作经历必须帮我写两到四段,项目经历必须帮我写二段，工作年限帮我随机。工作经历和项目经历markdown标识的字段最少100字以上，用无序列表的方式分段输出:
{"basic_info":{"job":"[职位]","city":"[城市名称]","birth":"[年龄]","gender":"[男或者女]","max_salary":"[薪资 eg:10k-15k]","intended_city":"[意向城市]"},"education":[{"major":"[专业]","degree":"[学历]","start_date":"[开始日期]","end_date":"[结束日期]","description":"[描述 需要markdown]","school_name":"[使用真实的学校名称]","school_tags":["[学校标签 多个]"],"college_name":["[使用真实学院名称]"]}],"work":[{"job":"[职位]","city":"[城市]","desc":"[工作描述 需要markdown]","company":"[使用真实的公司名称]","job_tags":["[职位标签 多个]"],"start_month":"[开始时间]","end_month":"[结束时间]","department":"[部门]","company_tags":["[公司标签 多个]"]}],"project":[{"desc":"[项目描述 需要markdown]","name":"[项目名称]","role":"[担任角色]","company":"[使用真实所属公司]","start_month":"[开始时间]","end_month":"[结束时间]"}],"personal_summary":{"summary":"[个人总结 需要markdown]"},"skills":[{"skillStyle":"[技能样式 1到15随机]","skillLayout":"[技能布局 1到3随机]","values":[{"name":"[技能名称]","score":"[技能评分1-10]"}]}],"honors":[{"honorWallStyle":"[荣誉墙样式 1到6]","honorWallLayout":"[荣誉墙布局 1到3]","values":["[荣誉名称]"]}],"other":[{"name":"[项目名称或者技能名称]","desc":"[其他技能或者项目描述 要markdown]"}]}`,
		example.Name, example.Name)

	return prompt
}

// parseAndFillExampleData 解析AI响应的JSON数据并填充到示例中
func (s *aiService) parseAndFillExampleData(example *models.Example, aiResponse string) error {
	// 清理AI响应，移除可能的markdown代码块标记
	cleanResponse := strings.TrimSpace(aiResponse)
	cleanResponse = strings.TrimPrefix(cleanResponse, "```json")
	cleanResponse = strings.TrimPrefix(cleanResponse, "```")
	cleanResponse = strings.TrimSuffix(cleanResponse, "```")
	cleanResponse = strings.TrimSpace(cleanResponse)

	s.logger.Debug("解析AI响应数据",
		zap.Uint("example_id", example.ID),
		zap.String("ai_response", cleanResponse))

	// 解析JSON数据
	var resumeData map[string]interface{}
	if err := json.Unmarshal([]byte(cleanResponse), &resumeData); err != nil {
		s.logger.Error("解析AI响应JSON失败",
			zap.Uint("example_id", example.ID),
			zap.String("response", cleanResponse),
			zap.Error(err))
		return fmt.Errorf("解析AI响应JSON失败: %w", err)
	}

	// 使用新的转换逻辑将AI数据转换为完整的数据结构
	resumeDetail, err := s.convertAIDataToModels(resumeData)
	if err != nil {
		s.logger.Error("转换AI数据为模型失败",
			zap.Uint("example_id", example.ID),
			zap.Error(err))
		return fmt.Errorf("转换AI数据为模型失败: %w", err)
	}

	// 填充转换后的数据到example
	example.BasicInfo = resumeDetail.BasicInfo
	example.Education = resumeDetail.Education
	example.Work = resumeDetail.Work
	example.Project = resumeDetail.Project
	example.Research = resumeDetail.Research
	example.Team = resumeDetail.Team
	example.Portfolio = resumeDetail.Portfolio
	example.Other = resumeDetail.Other
	example.PersonalSummary = resumeDetail.PersonalSummary
	example.Honors = resumeDetail.Honors
	example.Skills = resumeDetail.Skills
	example.CustomModules = resumeDetail.CustomModules
	example.Slogan = resumeDetail.Slogan

	s.logger.Info("成功解析并填充示例数据",
		zap.Uint("example_id", example.ID))

	return nil
}

// ConvertAIDataToResumeDetail 将AI返回的简化数据转换为完整的简历详情格式并保存
func (s *aiService) ConvertAIDataToResumeDetail(ctx context.Context, userID uint, resumeID uint, aiDataJSON string) error {
	s.logger.Info("开始转换AI数据为简历详情格式",
		zap.Uint("user_id", userID),
		zap.Uint("resume_id", resumeID))

	// 1. 解析AI返回的JSON数据
	var aiData map[string]interface{}
	if err := json.Unmarshal([]byte(aiDataJSON), &aiData); err != nil {
		s.logger.Error("解析AI数据JSON失败",
			zap.Uint("user_id", userID),
			zap.Uint("resume_id", resumeID),
			zap.String("ai_data", aiDataJSON),
			zap.Error(err))
		return exception.ErrInternalServer.WithDetail("解析AI数据失败")
	}

	// 2. 转换为完整的简历详情格式
	resumeDetail, err := s.convertAIDataToModels(aiData)
	if err != nil {
		s.logger.Error("转换AI数据为模型失败",
			zap.Uint("user_id", userID),
			zap.Uint("resume_id", resumeID),
			zap.Error(err))
		return exception.ErrInternalServer.WithDetail("转换AI数据失败")
	}

	// 3. 构建保存请求
	saveRequest := &dto_api.SaveResumeRequest{
		BasicInfo:       resumeDetail.BasicInfo,
		Education:       resumeDetail.Education,
		Work:            resumeDetail.Work,
		Project:         resumeDetail.Project,
		Research:        resumeDetail.Research,
		Team:            resumeDetail.Team,
		Portfolio:       resumeDetail.Portfolio,
		Other:           resumeDetail.Other,
		PersonalSummary: resumeDetail.PersonalSummary,
		Honors:          resumeDetail.Honors,
		Skills:          resumeDetail.Skills,
		CustomModules:   resumeDetail.CustomModules,
		Slogan:          resumeDetail.Slogan,
		CompletionRate:  "100%", // 设置默认完成度
	}

	// 4. 调用简历服务保存数据
	resumeIDStr := strconv.FormatUint(uint64(resumeID), 10)
	_, err = s.resumeService.SaveResumeDetail(ctx, resumeIDStr, userID, saveRequest, "", "")
	if err != nil {
		s.logger.Error("保存简历详情失败",
			zap.Uint("user_id", userID),
			zap.Uint("resume_id", resumeID),
			zap.Error(err))
		return exception.ErrInternalServer.WithDetail("保存简历详情失败")
	}

	s.logger.Info("成功转换并保存AI数据为简历详情",
		zap.Uint("user_id", userID),
		zap.Uint("resume_id", resumeID))

	return nil
}

// convertAIDataToModels 将AI数据转换为完整的简历详情模型
func (s *aiService) convertAIDataToModels(aiData map[string]interface{}) (*models.ResumeDetail, error) {
	resumeDetail := &models.ResumeDetail{}

	// 转换基本信息
	if basicInfoData, ok := aiData["basic_info"]; ok {
		basicInfo := s.convertBasicInfo(basicInfoData)
		resumeDetail.BasicInfo = basicInfo
	}

	// 转换教育经历
	if educationData, ok := aiData["education"]; ok {
		education := s.convertEducation(educationData)
		resumeDetail.Education = education
	}

	// 转换工作经历
	if workData, ok := aiData["work"]; ok {
		work := s.convertWork(workData)
		resumeDetail.Work = work
	}

	// 转换项目经历
	if projectData, ok := aiData["project"]; ok {
		project := s.convertProject(projectData)
		resumeDetail.Project = project
	}

	// 转换个人总结
	if personalSummaryData, ok := aiData["personal_summary"]; ok {
		personalSummary := s.convertPersonalSummary(personalSummaryData)
		resumeDetail.PersonalSummary = personalSummary
	}

	// 转换技能专长
	if skillsData, ok := aiData["skills"]; ok {
		skills := s.convertSkills(skillsData)
		resumeDetail.Skills = skills
	}

	// 转换荣誉奖项
	if honorsData, ok := aiData["honors"]; ok {
		honors := s.convertHonors(honorsData)
		resumeDetail.Honors = honors
	}

	// 转换其他信息
	if otherData, ok := aiData["other"]; ok {
		other := s.convertOther(otherData)
		resumeDetail.Other = other
	}

	// 设置空的默认值
	resumeDetail.Research = models.Research{
		ID:        "research",
		Name:      "研究经历",
		Type:      "research",
		IsVisible: false,
		SupportAI: true,
		Index:     6,
		Item:      []models.ResearchItem{},
	}

	resumeDetail.Team = models.Team{
		ID:        "team",
		Name:      "社团经历",
		Type:      "team",
		IsVisible: false,
		SupportAI: true,
		Index:     7,
		Item:      []models.TeamItem{},
	}

	resumeDetail.Portfolio = models.Portfolio{
		ID:        "portfolio",
		Name:      "作品集",
		Type:      "portfolio",
		IsVisible: false,
		SupportAI: true,
		Index:     11,
		Item:      []models.PortfolioItem{},
	}

	resumeDetail.CustomModules = models.CustomModules{}
	resumeDetail.Slogan = models.Slogan{}

	return resumeDetail, nil
}

// convertBasicInfo 转换基本信息
func (s *aiService) convertBasicInfo(data interface{}) models.BasicInfo {
	dataMap, ok := data.(map[string]interface{})
	if !ok {
		return models.BasicInfo{}
	}

	basicInfo := models.BasicInfo{
		ID:         "basic_info",
		Name:       "基本信息",
		Type:       "basic",
		Index:      0,
		IsVisible:  true,
		IsRequired: true,
		SupportAI:  false,
		Item: models.BasicInfoItem{
			Name: models.BasicInfoField{
				Label: "姓名",
				Value: "张三", // 默认值
			},
			Email: models.BasicInfoField{
				Label: "邮箱",
				Value: "<EMAIL>", // 默认值
			},
			Phone: models.BasicInfoField{
				Label: "电话",
				Value: "13800000000", // 默认值
			},
			Avatar: models.BasicInfoField{
				Label: "头像",
				Value: "",
			},
			JobStatus: models.BasicInfoField{
				Label: "工作状态",
				Value: "在职",
			},
		},
	}

	// 写死这三个字段的值，不使用AI返回的数据
	// name、email、phone 始终使用固定值

	if job, ok := dataMap["job"].(string); ok {
		basicInfo.Item.Job = models.BasicInfoField{
			Label: "职位",
			Value: job,
		}
	}

	if city, ok := dataMap["city"].(string); ok {
		basicInfo.Item.City = models.BasicInfoField{
			Label: "所在城市",
			Value: city,
		}
	}

	if birth, ok := dataMap["birth"].(string); ok {
		basicInfo.Item.Birth = models.BasicInfoField{
			Label: "年龄",
			Value: birth,
		}
	}

	if gender, ok := dataMap["gender"].(string); ok {
		basicInfo.Item.Gender = models.BasicInfoField{
			Label: "性别",
			Value: gender,
		}
	}

	if maxSalary, ok := dataMap["max_salary"].(string); ok {
		basicInfo.Item.MaxSalary = models.BasicInfoField{
			Label: "薪资",
			Value: maxSalary,
		}
	}

	if intendedCity, ok := dataMap["intended_city"].(string); ok {
		basicInfo.Item.IntendedCity = models.BasicInfoField{
			Label: "意向城市",
			Value: intendedCity,
		}
	}

	return basicInfo
}

// convertEducation 转换教育经历
func (s *aiService) convertEducation(data interface{}) models.Education {
	dataSlice, ok := data.([]interface{})
	if !ok {
		return models.Education{}
	}

	education := models.Education{
		ID:        "education",
		Name:      "教育经历",
		Type:      "education",
		Index:     2,
		IsVisible: true,
		SupportAI: true,
		Item:      []models.EducationItem{},
	}

	for i, item := range dataSlice {
		itemMap, ok := item.(map[string]interface{})
		if !ok {
			continue
		}

		eduItem := models.EducationItem{
			ID:    fmt.Sprintf("edu-%d", (time.Now().UnixNano()/1000000)%100000+int64(i*1000)),
			Index: i,
		}

		if major, ok := itemMap["major"].(string); ok {
			eduItem.Major = models.BasicInfoField{
				Label: "专业",
				Value: major,
			}
		}

		if degree, ok := itemMap["degree"].(string); ok {
			eduItem.Degree = models.BasicInfoField{
				Label: "学历",
				Value: degree,
			}
		}

		if startDate, ok := itemMap["start_date"].(string); ok {
			eduItem.StartDate = models.BasicInfoField{
				Label: "开始日期",
				Value: startDate,
			}
		}

		if endDate, ok := itemMap["end_date"].(string); ok {
			eduItem.EndDate = models.BasicInfoField{
				Label: "结束日期",
				Value: endDate,
			}
		}

		if description, ok := itemMap["description"].(string); ok {
			eduItem.Description = models.BasicInfoField{
				Label: "描述",
				Value: description,
			}
		}

		if schoolName, ok := itemMap["school_name"].(string); ok {
			eduItem.SchoolName = models.BasicInfoField{
				Label: "学校名称",
				Value: schoolName,
			}
		}

		if collegeName, ok := itemMap["college_name"].(string); ok {
			eduItem.CollegeName = models.BasicInfoField{
				Label: "学院名称",
				Value: collegeName,
			}
		}

		if schoolTags, ok := itemMap["school_tags"].([]interface{}); ok {
			tags := make([]string, 0, len(schoolTags))
			for _, tag := range schoolTags {
				if tagStr, ok := tag.(string); ok {
					tags = append(tags, tagStr)
				}
			}
			eduItem.SchoolTags = models.BasicInfoField{
				Label: "学校标签",
				Value: tags,
			}
		}

		education.Item = append(education.Item, eduItem)
	}

	return education
}

// convertWork 转换工作经历
func (s *aiService) convertWork(data interface{}) models.Work {
	dataSlice, ok := data.([]interface{})
	if !ok {
		return models.Work{}
	}

	work := models.Work{
		ID:        "work",
		Name:      "工作经历",
		Type:      "work",
		Index:     3,
		IsVisible: true,
		SupportAI: true,
		Item:      []models.WorkItem{},
	}

	for i, item := range dataSlice {
		itemMap, ok := item.(map[string]interface{})
		if !ok {
			continue
		}

		workItem := models.WorkItem{
			ID:    fmt.Sprintf("work-%d", (time.Now().UnixNano()/1000000)%100000+int64(i*1000)),
			Index: i,
		}

		if job, ok := itemMap["job"].(string); ok {
			workItem.Job = models.BasicInfoField{
				Label: "职位",
				Value: job,
			}
		}

		if city, ok := itemMap["city"].(string); ok {
			workItem.City = models.BasicInfoField{
				Label: "城市",
				Value: city,
			}
		}

		if desc, ok := itemMap["desc"].(string); ok {
			workItem.Desc = models.BasicInfoField{
				Label: "工作描述",
				Value: desc,
			}
		}

		if company, ok := itemMap["company"].(string); ok {
			workItem.Company = models.BasicInfoField{
				Label: "公司名称",
				Value: company,
			}
		}

		if department, ok := itemMap["department"].(string); ok {
			workItem.Department = models.BasicInfoField{
				Label: "部门",
				Value: department,
			}
		}

		if startMonth, ok := itemMap["start_month"].(string); ok {
			workItem.StartMonth = models.BasicInfoField{
				Label: "开始时间",
				Value: startMonth,
			}
		}

		if endMonth, ok := itemMap["end_month"].(string); ok {
			workItem.EndMonth = models.BasicInfoField{
				Label: "结束时间",
				Value: endMonth,
			}
		}

		if jobTags, ok := itemMap["job_tags"].([]interface{}); ok {
			tags := make([]string, 0, len(jobTags))
			for _, tag := range jobTags {
				if tagStr, ok := tag.(string); ok {
					tags = append(tags, tagStr)
				}
			}
			workItem.JobTags = models.BasicInfoField{
				Label: "职位标签",
				Value: tags,
			}
		}

		if companyTags, ok := itemMap["company_tags"].([]interface{}); ok {
			tags := make([]string, 0, len(companyTags))
			for _, tag := range companyTags {
				if tagStr, ok := tag.(string); ok {
					tags = append(tags, tagStr)
				}
			}
			workItem.CompanyTags = models.BasicInfoField{
				Label: "公司标签",
				Value: tags,
			}
		}

		work.Item = append(work.Item, workItem)
	}

	return work
}

// convertProject 转换项目经历
func (s *aiService) convertProject(data interface{}) models.Project {
	dataSlice, ok := data.([]interface{})
	if !ok {
		return models.Project{}
	}

	project := models.Project{
		ID:        "project",
		Name:      "项目经历",
		Type:      "project",
		Index:     4,
		IsVisible: true,
		SupportAI: true,
		Item:      []models.ProjectItem{},
	}

	for i, item := range dataSlice {
		itemMap, ok := item.(map[string]interface{})
		if !ok {
			continue
		}

		projectItem := models.ProjectItem{
			ID:    fmt.Sprintf("proj_%d", (time.Now().UnixNano()/1000000)%100000+int64(i*1000)),
			Index: i,
		}

		if name, ok := itemMap["name"].(string); ok {
			projectItem.Name = models.BasicInfoField{
				Label: "项目名称",
				Value: name,
			}
		}

		if role, ok := itemMap["role"].(string); ok {
			projectItem.Role = models.BasicInfoField{
				Label: "担任角色",
				Value: role,
			}
		}

		if company, ok := itemMap["company"].(string); ok {
			projectItem.Company = models.BasicInfoField{
				Label: "所属公司",
				Value: company,
			}
		}

		if startMonth, ok := itemMap["start_month"].(string); ok {
			projectItem.StartMonth = models.BasicInfoField{
				Label: "开始时间",
				Value: startMonth,
			}
		}

		if endMonth, ok := itemMap["end_month"].(string); ok {
			projectItem.EndMonth = models.BasicInfoField{
				Label: "结束时间",
				Value: endMonth,
			}
		}

		if desc, ok := itemMap["desc"].(string); ok {
			projectItem.Desc = models.BasicInfoField{
				Label: "项目描述",
				Value: desc,
			}
		}

		project.Item = append(project.Item, projectItem)
	}

	return project
}

// convertPersonalSummary 转换个人总结
func (s *aiService) convertPersonalSummary(data interface{}) models.PersonalSummary {
	dataMap, ok := data.(map[string]interface{})
	if !ok {
		return models.PersonalSummary{}
	}

	personalSummary := models.PersonalSummary{
		ID:        "personal_summary",
		Name:      "个人总结",
		Type:      "personal_summary",
		Index:     5,
		IsVisible: true,
		SupportAI: true,
		Item: models.PersonalSummaryItem{
			Summary: models.BasicInfoField{
				Label: "个人总结",
				Value: "",
			},
		},
	}

	if summary, ok := dataMap["summary"].(string); ok {
		personalSummary.Item.Summary = models.BasicInfoField{
			Label: "个人总结",
			Value: summary,
		}
	}

	return personalSummary
}

// convertSkills 转换技能专长
func (s *aiService) convertSkills(data interface{}) models.Skills {
	dataSlice, ok := data.([]interface{})
	if !ok {
		return models.Skills{}
	}

	skills := models.Skills{
		ID:        "skills",
		Name:      "技能专长",
		Type:      "skills",
		Index:     8,
		IsVisible: true,
		SupportAI: true,
		Item: models.SkillsItem{
			SkillStyle: models.BasicInfoField{
				Label: "技能样式",
				Value: fmt.Sprintf("%d", (time.Now().UnixNano()/1000000)%15+1), // 1到15随机
			},
			SkillLayout: models.BasicInfoField{
				Label: "技能布局",
				Value: fmt.Sprintf("%d", (time.Now().UnixNano()/1000000)%3+1), // 1到3随机
			},
			Values: models.BasicInfoField{
				Label: "技能列表",
				Value: []models.SkillValue{},
			},
		},
	}

	// 处理技能数据 - AI返回的是包含对象的数组
	if len(dataSlice) > 0 {
		if skillData, ok := dataSlice[0].(map[string]interface{}); ok {
			// 设置技能样式
			if skillStyle, ok := skillData["skillStyle"].(string); ok {
				skills.Item.SkillStyle.Value = skillStyle
			}

			// 设置技能布局
			if skillLayout, ok := skillData["skillLayout"].(string); ok {
				skills.Item.SkillLayout.Value = skillLayout
			}

			// 处理技能值列表 - values是包含技能对象的数组
			if values, ok := skillData["values"].([]interface{}); ok {
				skillValues := make([]models.SkillValue, 0, len(values))
				for i, value := range values {
					if valueMap, ok := value.(map[string]interface{}); ok {
						skillValue := models.SkillValue{
							Index: i,
						}

						if name, ok := valueMap["name"].(string); ok {
							skillValue.Name = models.BasicInfoField{
								Label: "技能名称",
								Value: name,
							}
						}

						if score, ok := valueMap["score"].(string); ok {
							skillValue.Score = models.BasicInfoField{
								Label: "技能评分",
								Value: score,
							}
						}

						skillValues = append(skillValues, skillValue)
					}
				}
				skills.Item.Values.Value = skillValues
			}
		}
	}

	return skills
}

// convertHonors 转换荣誉奖项
func (s *aiService) convertHonors(data interface{}) models.Honors {
	dataSlice, ok := data.([]interface{})
	if !ok {
		return models.Honors{}
	}

	honors := models.Honors{
		ID:        "honors",
		Name:      "荣誉奖项",
		Type:      "honors",
		Index:     9,
		IsVisible: true,
		SupportAI: true,
		Item: models.HonorsItem{
			HonorWallStyle: models.BasicInfoField{
				Label: "荣誉墙样式",
				Value: fmt.Sprintf("%d", (time.Now().UnixNano()/1000000)%6+1), // 1到6随机
			},
			HonorWallLayout: models.BasicInfoField{
				Label: "荣誉墙布局",
				Value: fmt.Sprintf("%d", (time.Now().UnixNano()/1000000)%3+1), // 1到3随机
			},
			Values: models.BasicInfoField{
				Label: "荣誉列表",
				Value: []models.HonorValue{},
			},
		},
	}

	// 处理荣誉数据 - AI返回的是包含对象的数组
	if len(dataSlice) > 0 {
		if honorData, ok := dataSlice[0].(map[string]interface{}); ok {
			// 设置荣誉墙样式
			if honorWallStyle, ok := honorData["honorWallStyle"].(string); ok {
				honors.Item.HonorWallStyle.Value = honorWallStyle
			}

			// 设置荣誉墙布局
			if honorWallLayout, ok := honorData["honorWallLayout"].(string); ok {
				honors.Item.HonorWallLayout.Value = honorWallLayout
			}

			// 处理荣誉值列表 - values是字符串数组
			if values, ok := honorData["values"].([]interface{}); ok {
				honorValues := make([]models.HonorValue, 0, len(values))
				for i, value := range values {
					if honorName, ok := value.(string); ok {
						honorValue := models.HonorValue{
							Index: i,
							Name: models.BasicInfoField{
								Label: "荣誉名称",
								Value: honorName,
							},
						}
						honorValues = append(honorValues, honorValue)
					}
				}
				honors.Item.Values.Value = honorValues
			}
		}
	}

	return honors
}

// convertOther 转换其他信息
func (s *aiService) convertOther(data interface{}) models.Other {
	dataSlice, ok := data.([]interface{})
	if !ok {
		return models.Other{}
	}

	other := models.Other{
		ID:        "other",
		Name:      "其他信息",
		Type:      "other",
		Index:     10,
		IsVisible: true,
		SupportAI: true,
		Item:      []models.OtherItem{},
	}

	for i, item := range dataSlice {
		itemMap, ok := item.(map[string]interface{})
		if !ok {
			continue
		}

		otherItem := models.OtherItem{
			ID:    fmt.Sprintf("other-%d", (time.Now().UnixNano()/1000000)%100000+int64(i*1000)),
			Index: i,
		}

		if name, ok := itemMap["name"].(string); ok {
			otherItem.Name = models.BasicInfoField{
				Label: "项目名称",
				Value: name,
			}
		}

		if desc, ok := itemMap["desc"].(string); ok {
			otherItem.Desc = models.BasicInfoField{
				Label: "其他",
				Value: desc,
			}
		}

		other.Item = append(other.Item, otherItem)
	}

	return other
}

// getPrivilegeTypeByPromptType 根据提示词类型获取对应的权益类型
func (s *aiService) getPrivilegeTypeByPromptType(promptType enum.PromptType) (enum.PrivilegeType, error) {
	switch promptType {
	case enum.PromptTypeGenerate, enum.PromptTypeContinue:
		return enum.PrivilegeAIGenerate, nil
	case enum.PromptTypeProfessional, enum.PromptTypeConcise, enum.PromptTypeDetailed:
		return enum.PrivilegeAIRewrite, nil
	case enum.PromptTypeGenerateResume:
		return enum.PrivilegeAIOneClick, nil
	case enum.PromptTypeOptimize:
		return enum.PrivilegeAIOptimize, nil
	case enum.PromptTypeScore:
		return enum.PrivilegeAIDiagnose, nil
	default:
		return 0, fmt.Errorf("未知的提示词类型: %s", promptType)
	}
}

// OptimizeResume AI优化简历
func (s *aiService) OptimizeResume(ctx context.Context, userID uint, resumeID uint) (*dto_api.OptimizeResumeResponse, error) {
	s.logger.Info("开始AI优化简历",
		zap.Uint("user_id", userID),
		zap.Uint("resume_id", resumeID))

	// 1. 创建AI调用记录
	startTime := time.Now()
	record := &models.AICallRecord{
		UserID:       userID,
		ResumeID:     resumeID,
		PromptType:   enum.PromptTypeOptimize,
		ResumeModule: "", // 简历优化不针对特定模块
		RequestData:  fmt.Sprintf("优化简历ID: %d", resumeID),
		ResponseData: "",
		CallDuration: 0,
	}

	if err := s.aiCallRecordRepo.Create(ctx, record); err != nil {
		s.logger.Error("创建AI调用记录失败",
			zap.Uint("user_id", userID),
			zap.Uint("resume_id", resumeID),
			zap.Error(err))
		return nil, exception.ErrInternalServer.WithDetail("创建AI调用记录失败")
	}

	// 2. 检查简历所有权
	isOwner, err := s.resumeRepo.CheckResumeOwnership(ctx, resumeID, userID)
	if err != nil {
		s.logger.Error("检查简历所有权失败",
			zap.Uint("user_id", userID),
			zap.Uint("resume_id", resumeID),
			zap.Error(err))
		return nil, exception.ErrInternalServer.WithDetail("检查简历所有权失败")
	}
	if !isOwner {
		s.logger.Warn("用户尝试优化不属于自己的简历",
			zap.Uint("user_id", userID),
			zap.Uint("resume_id", resumeID))
		return nil, exception.ErrResumeAccess.WithDetail("无权限访问该简历")
	}

	// 3. 获取简历详情
	resumeDetail, err := s.resumeRepo.GetResumeDetailByResumeID(ctx, resumeID)
	if err != nil {
		s.logger.Error("获取简历详情失败",
			zap.Uint("user_id", userID),
			zap.Uint("resume_id", resumeID),
			zap.Error(err))
		return nil, exception.ErrInternalServer.WithDetail("获取简历详情失败")
	}

	// 4. 转换为简化的JSON结构
	simplifiedData := s.convertResumeDetailToSimplifiedJSON(resumeDetail)

	// 5. 打印转换后的JSON数据
	jsonData, err := json.MarshalIndent(simplifiedData, "", "  ")
	if err != nil {
		s.logger.Error("序列化简历数据失败",
			zap.Uint("user_id", userID),
			zap.Uint("resume_id", resumeID),
			zap.Error(err))
		return nil, exception.ErrInternalServer.WithDetail("序列化简历数据失败")
	}

	s.logger.Info("转换后的简历数据",
		zap.Uint("user_id", userID),
		zap.Uint("resume_id", resumeID),
		zap.String("simplified_data", string(jsonData)))

	// 6. 调用AI进行简历优化
	optimizedData, err := s.callAIForOptimization(ctx, simplifiedData)
	endTime := time.Now()
	callDuration := endTime.Sub(startTime).Milliseconds()

	if err != nil {
		// 更新AI调用记录为失败状态
		failureResponse := fmt.Sprintf("AI优化失败: %v", err)
		s.aiCallRecordRepo.UpdateResponse(ctx, record.ID, failureResponse, callDuration)

		s.logger.Error("AI优化简历失败",
			zap.Uint("user_id", userID),
			zap.Uint("resume_id", resumeID),
			zap.Error(err))
		return nil, exception.ErrInternalServer.WithDetail("AI优化简历失败")
	}

	// 7. 获取原简历信息以获取模板ID
	resume, err := s.resumeRepo.GetResumeByID(ctx, resumeID)
	if err != nil {
		s.logger.Error("获取简历信息失败",
			zap.Uint("user_id", userID),
			zap.Uint("resume_id", resumeID),
			zap.Error(err))
		return nil, exception.ErrInternalServer.WithDetail("获取简历信息失败")
	}

	// 8. 将优化后的数据转换为ResumeDraft（基于原有的resumeDetail）
	optimizedResumeDraft, err := s.convertSimplifiedJSONToResumeDraft(optimizedData, resumeDetail, userID, resume.TemplateID)
	if err != nil {
		// 更新AI调用记录为失败状态
		failureResponse := fmt.Sprintf("转换优化后的数据失败: %v", err)
		s.aiCallRecordRepo.UpdateResponse(ctx, record.ID, failureResponse, callDuration)

		s.logger.Error("转换优化后的数据失败",
			zap.Uint("user_id", userID),
			zap.Uint("resume_id", resumeID),
			zap.Error(err))
		return nil, exception.ErrInternalServer.WithDetail("转换优化后的数据失败")
	}

	// 9. 创建简历草稿
	if err := s.resumeDraftRepo.Create(ctx, optimizedResumeDraft); err != nil {
		// 更新AI调用记录为失败状态
		failureResponse := fmt.Sprintf("创建简历草稿失败: %v", err)
		s.aiCallRecordRepo.UpdateResponse(ctx, record.ID, failureResponse, callDuration)

		s.logger.Error("创建简历草稿失败",
			zap.Uint("user_id", userID),
			zap.Uint("resume_id", resumeID),
			zap.Error(err))
		return nil, exception.ErrInternalServer.WithDetail("创建简历草稿失败")
	}

	// 10. 更新AI调用记录为成功状态
	successResponse := fmt.Sprintf("AI优化成功，生成草稿ID: %d", optimizedResumeDraft.ID)
	s.aiCallRecordRepo.UpdateResponse(ctx, record.ID, successResponse, callDuration)

	s.logger.Info("AI优化简历成功",
		zap.Uint("user_id", userID),
		zap.Uint("resume_id", resumeID),
		zap.Uint("draft_id", optimizedResumeDraft.ID))

	return &dto_api.OptimizeResumeResponse{
		DraftID: optimizedResumeDraft.ID,
	}, nil
}

// convertResumeDetailToSimplifiedJSON 将ResumeDetail转换为简化的JSON结构
func (s *aiService) convertResumeDetailToSimplifiedJSON(resumeDetail *models.ResumeDetail) map[string]interface{} {
	result := make(map[string]interface{})

	// 转换基本信息
	if resumeDetail.BasicInfo.Item.Name.Value != nil {
		basicInfo := make(map[string]interface{})
		if resumeDetail.BasicInfo.Item.Avatar.Value != nil {
			basicInfo["avatar"] = resumeDetail.BasicInfo.Item.Avatar.Value
		}
		if resumeDetail.BasicInfo.Item.AvatarFilter.Value != nil {
			basicInfo["avatar_filter"] = resumeDetail.BasicInfo.Item.AvatarFilter.Value
		}
		if resumeDetail.BasicInfo.Item.Birth.Value != nil {
			basicInfo["birth"] = resumeDetail.BasicInfo.Item.Birth.Value
		}
		if resumeDetail.BasicInfo.Item.BirthType.Value != nil {
			basicInfo["birth_type"] = resumeDetail.BasicInfo.Item.BirthType.Value
		}
		if resumeDetail.BasicInfo.Item.City.Value != nil {
			basicInfo["city"] = resumeDetail.BasicInfo.Item.City.Value
		}
		if resumeDetail.BasicInfo.Item.Email.Value != nil {
			basicInfo["email"] = resumeDetail.BasicInfo.Item.Email.Value
		}
		if resumeDetail.BasicInfo.Item.Gender.Value != nil {
			basicInfo["gender"] = resumeDetail.BasicInfo.Item.Gender.Value
		}
		if resumeDetail.BasicInfo.Item.Job.Value != nil {
			basicInfo["job"] = resumeDetail.BasicInfo.Item.Job.Value
		}
		if resumeDetail.BasicInfo.Item.Name.Value != nil {
			basicInfo["name"] = resumeDetail.BasicInfo.Item.Name.Value
		}
		if resumeDetail.BasicInfo.Item.Phone.Value != nil {
			basicInfo["phone"] = resumeDetail.BasicInfo.Item.Phone.Value
		}
		result["basic_info"] = basicInfo
	}

	// 转换教育经历
	if len(resumeDetail.Education.Item) > 0 {
		education := make([]map[string]interface{}, 0, len(resumeDetail.Education.Item))
		for _, item := range resumeDetail.Education.Item {
			eduItem := make(map[string]interface{})
			if item.SchoolName.Value != nil {
				eduItem["school_name"] = item.SchoolName.Value
			}
			if item.Major.Value != nil {
				eduItem["major"] = item.Major.Value
			}
			if item.Degree.Value != nil {
				eduItem["degree"] = item.Degree.Value
			}
			if item.StartDate.Value != nil {
				eduItem["start_date"] = item.StartDate.Value
			}
			if item.EndDate.Value != nil {
				eduItem["end_date"] = item.EndDate.Value
			}
			if item.Description.Value != nil {
				eduItem["description"] = item.Description.Value
			}
			education = append(education, eduItem)
		}
		result["education"] = education
	}

	// 转换工作经历
	if len(resumeDetail.Work.Item) > 0 {
		work := make([]map[string]interface{}, 0, len(resumeDetail.Work.Item))
		for _, item := range resumeDetail.Work.Item {
			workItem := make(map[string]interface{})
			if item.Company.Value != nil {
				workItem["company"] = item.Company.Value
			}
			if item.Job.Value != nil {
				workItem["job"] = item.Job.Value
			}
			if item.StartMonth.Value != nil {
				workItem["start_month"] = item.StartMonth.Value
			}
			if item.EndMonth.Value != nil {
				workItem["end_month"] = item.EndMonth.Value
			}
			if item.Desc.Value != nil {
				workItem["desc"] = item.Desc.Value
			}
			work = append(work, workItem)
		}
		result["work"] = work
	}

	// 转换项目经历
	if len(resumeDetail.Project.Item) > 0 {
		project := make([]map[string]interface{}, 0, len(resumeDetail.Project.Item))
		for _, item := range resumeDetail.Project.Item {
			projectItem := make(map[string]interface{})
			if item.Name.Value != nil {
				projectItem["name"] = item.Name.Value
			}
			if item.Role.Value != nil {
				projectItem["role"] = item.Role.Value
			}
			if item.Company.Value != nil {
				projectItem["company"] = item.Company.Value
			}
			if item.StartMonth.Value != nil {
				projectItem["start_month"] = item.StartMonth.Value
			}
			if item.EndMonth.Value != nil {
				projectItem["end_month"] = item.EndMonth.Value
			}
			if item.Desc.Value != nil {
				projectItem["desc"] = item.Desc.Value
			}
			project = append(project, projectItem)
		}
		result["project"] = project
	}

	// 转换研究经历
	if len(resumeDetail.Research.Item) > 0 {
		research := make([]map[string]interface{}, 0, len(resumeDetail.Research.Item))
		for _, item := range resumeDetail.Research.Item {
			researchItem := make(map[string]interface{})
			if item.Name.Value != nil {
				researchItem["name"] = item.Name.Value
			}
			if item.Role.Value != nil {
				researchItem["role"] = item.Role.Value
			}
			if item.Department.Value != nil {
				researchItem["department"] = item.Department.Value
			}
			if item.City.Value != nil {
				researchItem["city"] = item.City.Value
			}
			if item.StartMonth.Value != nil {
				researchItem["start_month"] = item.StartMonth.Value
			}
			if item.EndMonth.Value != nil {
				researchItem["end_month"] = item.EndMonth.Value
			}
			if item.Desc.Value != nil {
				researchItem["desc"] = item.Desc.Value
			}
			research = append(research, researchItem)
		}
		result["research"] = research
	}

	// 转换社团经历
	if len(resumeDetail.Team.Item) > 0 {
		team := make([]map[string]interface{}, 0, len(resumeDetail.Team.Item))
		for _, item := range resumeDetail.Team.Item {
			teamItem := make(map[string]interface{})
			if item.Name.Value != nil {
				teamItem["name"] = item.Name.Value
			}
			if item.Department.Value != nil {
				teamItem["department"] = item.Department.Value
			}
			if item.Role.Value != nil {
				teamItem["role"] = item.Role.Value
			}
			if item.City.Value != nil {
				teamItem["city"] = item.City.Value
			}
			if item.StartMonth.Value != nil {
				teamItem["start_month"] = item.StartMonth.Value
			}
			if item.EndMonth.Value != nil {
				teamItem["end_month"] = item.EndMonth.Value
			}
			if item.Desc.Value != nil {
				teamItem["desc"] = item.Desc.Value
			}
			team = append(team, teamItem)
		}
		result["team"] = team
	}

	// 转换个人总结
	if resumeDetail.PersonalSummary.Item.Summary.Value != nil {
		personalSummary := map[string]interface{}{
			"summary": resumeDetail.PersonalSummary.Item.Summary.Value,
		}
		result["personal_summary"] = personalSummary
	}

	// 转换荣誉
	if resumeDetail.Honors.Item.Values.Value != nil {
		honors := map[string]interface{}{
			"honorWallLayout": resumeDetail.Honors.Item.HonorWallLayout.Value,
			"honorWallStyle":  resumeDetail.Honors.Item.HonorWallStyle.Value,
			"values":          resumeDetail.Honors.Item.Values.Value,
		}
		result["honors"] = honors
	}

	// 转换其他模块
	if len(resumeDetail.Other.Item) > 0 {
		other := make([]map[string]interface{}, 0, len(resumeDetail.Other.Item))
		for _, item := range resumeDetail.Other.Item {
			otherItem := make(map[string]interface{})
			if item.Name.Value != nil {
				otherItem["name"] = item.Name.Value
			}
			if item.Desc.Value != nil {
				otherItem["desc"] = item.Desc.Value
			}
			other = append(other, otherItem)
		}
		result["other"] = other
	}

	// 转换自定义模块
	if len(resumeDetail.CustomModules) > 0 {
		result["custom_modules"] = resumeDetail.CustomModules
	}

	// 添加diff字段
	result["diff"] = "请将优化的前后对比填充到这个字段，markdown格式"

	return result
}

// convertSimplifiedJSONToResumeDraft 将简化的JSON结构转换为ResumeDraft（基于原有的resumeDetail）
func (s *aiService) convertSimplifiedJSONToResumeDraft(data map[string]interface{}, originalResumeDetail *models.ResumeDetail, userID uint, templateID uint) (*models.ResumeDraft, error) {
	// 基于原有的resumeDetail创建ResumeDraft副本，保留所有原有数据
	resumeDraft := &models.ResumeDraft{
		UserID:          userID,
		TemplateID:      templateID,
		BasicInfo:       originalResumeDetail.BasicInfo,
		Education:       originalResumeDetail.Education,
		Work:            originalResumeDetail.Work,
		Project:         originalResumeDetail.Project,
		Research:        originalResumeDetail.Research,
		Team:            originalResumeDetail.Team,
		Portfolio:       originalResumeDetail.Portfolio,
		Other:           originalResumeDetail.Other,
		PersonalSummary: originalResumeDetail.PersonalSummary,
		Honors:          originalResumeDetail.Honors,
		Skills:          originalResumeDetail.Skills,
		CustomModules:   originalResumeDetail.CustomModules,
		Slogan:          originalResumeDetail.Slogan,
		ResumeStyle:     originalResumeDetail.ResumeStyle, // 保留原有的样式配置
	}

	// 转换教育经历（只更新简化JSON中包含的字段）
	if educationData, ok := data["education"].([]interface{}); ok {
		// 保留原有的教育经历结构，重新构建Item列表
		education := resumeDraft.Education
		education.Item = []models.EducationItem{}

		for i, item := range educationData {
			if itemMap, ok := item.(map[string]interface{}); ok {
				eduItem := models.EducationItem{
					ID:    fmt.Sprintf("edu_%d", i),
					Index: i,
				}

				if schoolName, ok := itemMap["school_name"]; ok {
					eduItem.SchoolName = models.BasicInfoField{Label: "学校名称", Value: schoolName}
				}
				if collegeName, ok := itemMap["college_name"]; ok {
					eduItem.CollegeName = models.BasicInfoField{Label: "学院名称", Value: collegeName}
				}
				if major, ok := itemMap["major"]; ok {
					eduItem.Major = models.BasicInfoField{Label: "专业", Value: major}
				}
				if degree, ok := itemMap["degree"]; ok {
					eduItem.Degree = models.BasicInfoField{Label: "学历", Value: degree}
				}
				if city, ok := itemMap["city"]; ok {
					eduItem.City = models.BasicInfoField{Label: "城市", Value: city}
				}
				if startDate, ok := itemMap["start_date"]; ok {
					eduItem.StartDate = models.BasicInfoField{Label: "开始时间", Value: startDate}
				}
				if endDate, ok := itemMap["end_date"]; ok {
					eduItem.EndDate = models.BasicInfoField{Label: "结束时间", Value: endDate}
				}
				if description, ok := itemMap["description"]; ok {
					eduItem.Description = models.BasicInfoField{Label: "描述", Value: description}
				}
				if schoolTags, ok := itemMap["school_tags"]; ok {
					eduItem.SchoolTags = models.BasicInfoField{Label: "学校标签", Value: schoolTags}
				}

				education.Item = append(education.Item, eduItem)
			}
		}

		resumeDraft.Education = education
	}

	// 转换工作经历（只更新简化JSON中包含的字段）
	if workData, ok := data["work"].([]interface{}); ok {
		// 保留原有的工作经历结构，重新构建Item列表
		work := resumeDraft.Work
		work.Item = []models.WorkItem{}

		for i, item := range workData {
			if itemMap, ok := item.(map[string]interface{}); ok {
				workItem := models.WorkItem{
					ID:    fmt.Sprintf("work_%d", i),
					Index: i,
				}

				if company, ok := itemMap["company"]; ok {
					workItem.Company = models.BasicInfoField{Label: "公司名称", Value: company}
				}
				if department, ok := itemMap["department"]; ok {
					workItem.Department = models.BasicInfoField{Label: "部门", Value: department}
				}
				if city, ok := itemMap["city"]; ok {
					workItem.City = models.BasicInfoField{Label: "城市", Value: city}
				}
				if job, ok := itemMap["job"]; ok {
					workItem.Job = models.BasicInfoField{Label: "职位", Value: job}
				}
				if startMonth, ok := itemMap["start_month"]; ok {
					workItem.StartMonth = models.BasicInfoField{Label: "开始时间", Value: startMonth}
				}
				if endMonth, ok := itemMap["end_month"]; ok {
					workItem.EndMonth = models.BasicInfoField{Label: "结束时间", Value: endMonth}
				}
				if desc, ok := itemMap["desc"]; ok {
					workItem.Desc = models.BasicInfoField{Label: "工作描述", Value: desc}
				}
				if companyTags, ok := itemMap["company_tags"]; ok {
					workItem.CompanyTags = models.BasicInfoField{Label: "公司标签", Value: companyTags}
				}
				if jobTags, ok := itemMap["job_tags"]; ok {
					workItem.JobTags = models.BasicInfoField{Label: "职位标签", Value: jobTags}
				}

				work.Item = append(work.Item, workItem)
			}
		}

		resumeDraft.Work = work
	}

	// 转换项目经历（只更新简化JSON中包含的字段）
	if projectData, ok := data["project"].([]interface{}); ok {
		// 保留原有的项目经历结构，重新构建Item列表
		project := resumeDraft.Project
		project.Item = []models.ProjectItem{}

		for i, item := range projectData {
			if itemMap, ok := item.(map[string]interface{}); ok {
				projectItem := models.ProjectItem{
					ID:    fmt.Sprintf("project_%d", i),
					Index: i,
				}

				if name, ok := itemMap["name"]; ok {
					projectItem.Name = models.BasicInfoField{Label: "项目名称", Value: name}
				}
				if role, ok := itemMap["role"]; ok {
					projectItem.Role = models.BasicInfoField{Label: "担任角色", Value: role}
				}
				if company, ok := itemMap["company"]; ok {
					projectItem.Company = models.BasicInfoField{Label: "所属公司", Value: company}
				}
				if startMonth, ok := itemMap["start_month"]; ok {
					projectItem.StartMonth = models.BasicInfoField{Label: "开始时间", Value: startMonth}
				}
				if endMonth, ok := itemMap["end_month"]; ok {
					projectItem.EndMonth = models.BasicInfoField{Label: "结束时间", Value: endMonth}
				}
				if desc, ok := itemMap["desc"]; ok {
					projectItem.Desc = models.BasicInfoField{Label: "项目描述", Value: desc}
				}

				project.Item = append(project.Item, projectItem)
			}
		}

		resumeDraft.Project = project
	}

	// 转换研究经历（只更新简化JSON中包含的字段）
	if researchData, ok := data["research"].([]interface{}); ok {
		// 保留原有的研究经历结构，重新构建Item列表
		research := resumeDraft.Research
		research.Item = []models.ResearchItem{}

		for i, item := range researchData {
			if itemMap, ok := item.(map[string]interface{}); ok {
				researchItem := models.ResearchItem{
					ID:    fmt.Sprintf("research_%d", i),
					Index: i,
				}

				if name, ok := itemMap["name"]; ok {
					researchItem.Name = models.BasicInfoField{Label: "研究项目名称", Value: name}
				}
				if role, ok := itemMap["role"]; ok {
					researchItem.Role = models.BasicInfoField{Label: "担任角色", Value: role}
				}
				if department, ok := itemMap["department"]; ok {
					researchItem.Department = models.BasicInfoField{Label: "所属部门", Value: department}
				}
				if city, ok := itemMap["city"]; ok {
					researchItem.City = models.BasicInfoField{Label: "城市", Value: city}
				}
				if startMonth, ok := itemMap["start_month"]; ok {
					researchItem.StartMonth = models.BasicInfoField{Label: "开始时间", Value: startMonth}
				}
				if endMonth, ok := itemMap["end_month"]; ok {
					researchItem.EndMonth = models.BasicInfoField{Label: "结束时间", Value: endMonth}
				}
				if desc, ok := itemMap["desc"]; ok {
					researchItem.Desc = models.BasicInfoField{Label: "研究描述", Value: desc}
				}

				research.Item = append(research.Item, researchItem)
			}
		}

		resumeDraft.Research = research
	}

	// 转换社团经历（只更新简化JSON中包含的字段）
	if teamData, ok := data["team"].([]interface{}); ok {
		// 保留原有的社团经历结构，重新构建Item列表
		team := resumeDraft.Team
		team.Item = []models.TeamItem{}

		for i, item := range teamData {
			if itemMap, ok := item.(map[string]interface{}); ok {
				teamItem := models.TeamItem{
					ID:    fmt.Sprintf("team_%d", i),
					Index: i,
				}

				if name, ok := itemMap["name"]; ok {
					teamItem.Name = models.BasicInfoField{Label: "社团名称", Value: name}
				}
				if department, ok := itemMap["department"]; ok {
					teamItem.Department = models.BasicInfoField{Label: "所属部门", Value: department}
				}
				if role, ok := itemMap["role"]; ok {
					teamItem.Role = models.BasicInfoField{Label: "担任角色", Value: role}
				}
				if city, ok := itemMap["city"]; ok {
					teamItem.City = models.BasicInfoField{Label: "城市", Value: city}
				}
				if startMonth, ok := itemMap["start_month"]; ok {
					teamItem.StartMonth = models.BasicInfoField{Label: "开始时间", Value: startMonth}
				}
				if endMonth, ok := itemMap["end_month"]; ok {
					teamItem.EndMonth = models.BasicInfoField{Label: "结束时间", Value: endMonth}
				}
				if desc, ok := itemMap["desc"]; ok {
					teamItem.Desc = models.BasicInfoField{Label: "社团描述", Value: desc}
				}

				team.Item = append(team.Item, teamItem)
			}
		}

		resumeDraft.Team = team
	}

	// 注意：Portfolio 等字段保持原有数据不变

	// 转换其他模块（只更新简化JSON中包含的字段）
	if otherData, ok := data["other"].([]interface{}); ok {
		// 保留原有的其他模块结构，重新构建Item列表
		other := resumeDraft.Other
		other.Item = []models.OtherItem{}

		for i, item := range otherData {
			if itemMap, ok := item.(map[string]interface{}); ok {
				otherItem := models.OtherItem{
					ID:    fmt.Sprintf("other_%d", i),
					Index: i,
				}

				if name, ok := itemMap["name"]; ok {
					otherItem.Name = models.BasicInfoField{Label: "项目名称", Value: name}
				}
				if desc, ok := itemMap["desc"]; ok {
					otherItem.Desc = models.BasicInfoField{Label: "描述", Value: desc}
				}

				other.Item = append(other.Item, otherItem)
			}
		}

		resumeDraft.Other = other
	}

	// 转换个人总结（只更新简化JSON中包含的字段）
	if personalSummaryData, ok := data["personal_summary"].(map[string]interface{}); ok {
		if summary, ok := personalSummaryData["summary"]; ok {
			resumeDraft.PersonalSummary.Item.Summary.Value = summary
		}
	}

	// 转换荣誉（只更新简化JSON中包含的字段）
	if honorsData, ok := data["honors"].(map[string]interface{}); ok {
		if honorWallLayout, ok := honorsData["honorWallLayout"]; ok {
			resumeDraft.Honors.Item.HonorWallLayout.Value = honorWallLayout
		}
		if honorWallStyle, ok := honorsData["honorWallStyle"]; ok {
			resumeDraft.Honors.Item.HonorWallStyle.Value = honorWallStyle
		}
		if values, ok := honorsData["values"]; ok {
			resumeDraft.Honors.Item.Values.Value = values
		}
	}

	// 转换自定义模块（只更新简化JSON中包含的字段）
	if customModulesData, ok := data["custom_modules"].(models.CustomModules); ok {
		resumeDraft.CustomModules = customModulesData
	}

	// 转换diff字段（只更新简化JSON中包含的字段）
	if diffData, ok := data["diff"].(string); ok {
		resumeDraft.Diff = diffData
	}

	// 注意：ResumeStyle 保持原有数据不变

	return resumeDraft, nil
}

// callAIForOptimization 调用AI进行简历优化
func (s *aiService) callAIForOptimization(ctx context.Context, simplifiedData map[string]interface{}) (map[string]interface{}, error) {
	// 1. 将简化数据转换为JSON字符串
	jsonData, err := json.MarshalIndent(simplifiedData, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("序列化简历数据失败: %w", err)
	}

	// 2. 构建AI提示词
	prompt := fmt.Sprintf(`简历要求：
简历结构清晰，便于阅读
简历要有核心竞争力
项目经历量化且符合工作经历
自我评价简洁有力，展现独特优势
简历不能一眼假、要真实，确保简历对ATS友好，并能被招聘人员注意到。
帮我以如下json格式返回：

%s

请优化上述简历数据，保持JSON格式不变，只优化内容质量。特别注意：
1. 优化工作经历和项目经历的描述，使其更具体、量化
2. 完善个人总结，突出核心竞争力
3. 确保所有描述真实可信，避免夸大
4. 在diff字段中用markdown格式无序列表简要说明优化的前后对比
5. 只返回JSON数据，不要添加任何其他文字`, string(jsonData))

	// 3. 构建AI消息
	messages := []*pkg.ChatMessage{
		{
			Role:    "system",
			Content: "你是一个专业的简历优化助手，专门帮助用户优化简历内容。请严格按照用户要求的JSON格式返回数据，不要添加任何额外的文字说明。",
		},
		{
			Role:    "user",
			Content: prompt,
		},
	}

	// 4. 调用AI服务
	response, err := s.aiClient.ChatCompletion(ctx, messages)
	if err != nil {
		return nil, fmt.Errorf("AI调用失败: %w", err)
	}

	// 5. 解析AI返回的JSON数据
	var optimizedData map[string]interface{}
	if err := json.Unmarshal([]byte(response.Content), &optimizedData); err != nil {
		s.logger.Error("解析AI返回的JSON失败",
			zap.String("ai_response", response.Content),
			zap.Error(err))
		return nil, fmt.Errorf("解析AI返回的JSON失败: %w", err)
	}

	return optimizedData, nil
}

// ScoreResume AI简历打分
func (s *aiService) ScoreResume(ctx context.Context, userID uint, resumeID uint, positionID uint) (*vo_api.ScoreResumeResponse, error) {
	s.logger.Info("开始AI简历打分",
		zap.Uint("user_id", userID),
		zap.Uint("resume_id", resumeID),
		zap.Uint("position_id", positionID))

	// 1. 创建AI调用记录
	startTime := time.Now()
	record := &models.AICallRecord{
		UserID:       userID,
		ResumeID:     resumeID,
		PromptType:   enum.PromptTypeScore,
		ResumeModule: "", // 简历打分不针对特定模块
		RequestData:  fmt.Sprintf("简历打分 - 简历ID: %d, 岗位ID: %d", resumeID, positionID),
		ResponseData: "",
		CallDuration: 0,
	}

	if err := s.aiCallRecordRepo.Create(ctx, record); err != nil {
		s.logger.Error("创建AI调用记录失败",
			zap.Uint("user_id", userID),
			zap.Uint("resume_id", resumeID),
			zap.Uint("position_id", positionID),
			zap.Error(err))
		return nil, exception.ErrInternalServer.WithDetail("创建AI调用记录失败")
	}

	// 2. 检查简历所有权
	isOwner, err := s.resumeRepo.CheckResumeOwnership(ctx, resumeID, userID)
	if err != nil {
		s.logger.Error("检查简历所有权失败",
			zap.Uint("user_id", userID),
			zap.Uint("resume_id", resumeID),
			zap.Error(err))
		return nil, exception.ErrInternalServer.WithDetail("检查简历所有权失败")
	}

	if !isOwner {
		s.logger.Warn("用户无权访问该简历",
			zap.Uint("user_id", userID),
			zap.Uint("resume_id", resumeID))
		return nil, exception.ErrResumeNotFound
	}

	// 3. 获取简历详情
	resumeDetail, err := s.resumeRepo.GetResumeDetailByResumeID(ctx, resumeID)
	if err != nil {
		s.logger.Error("获取简历详情失败",
			zap.Uint("user_id", userID),
			zap.Uint("resume_id", resumeID),
			zap.Error(err))
		return nil, exception.ErrInternalServer.WithDetail("获取简历详情失败")
	}

	// 4. 获取目标岗位信息
	targetPosition, err := s.targetPositionRepo.GetByUserIDAndID(ctx, userID, positionID)
	if err != nil {
		s.logger.Error("获取目标岗位信息失败",
			zap.Uint("user_id", userID),
			zap.Uint("position_id", positionID),
			zap.Error(err))
		return nil, exception.ErrInternalServer.WithDetail("获取目标岗位信息失败")
	}

	// 5. 构建简历数据JSON
	resumeData := map[string]interface{}{
		"basic_info":       resumeDetail.BasicInfo,
		"education":        resumeDetail.Education,
		"work":             resumeDetail.Work,
		"project":          resumeDetail.Project,
		"research":         resumeDetail.Research,
		"team":             resumeDetail.Team,
		"portfolio":        resumeDetail.Portfolio,
		"other":            resumeDetail.Other,
		"personal_summary": resumeDetail.PersonalSummary,
		"honors":           resumeDetail.Honors,
		"skills":           resumeDetail.Skills,
		"custom_modules":   resumeDetail.CustomModules,
	}

	resumeDataJSON, err := json.Marshal(resumeData)
	if err != nil {
		s.logger.Error("序列化简历数据失败",
			zap.Uint("user_id", userID),
			zap.Uint("resume_id", resumeID),
			zap.Error(err))
		return nil, exception.ErrInternalServer.WithDetail("序列化简历数据失败")
	}

	// 6. 构建AI提示词
	prompt := fmt.Sprintf(`你是一位资深的HR专家和简历评估顾问，拥有10年以上的招聘经验。请对以下简历进行专业、详细的评分分析，针对目标岗位"%s"进行深度匹配评估。

简历数据：
%s

目标岗位信息：
- 岗位名称：%s
- 公司名称：%s
- 岗位来源：%s
- 岗位职责描述：%s

请从以下四个维度进行详细评分，每个维度满分100分，需要提供具体、实用的评价和改进建议：

1. 语言与表达（100分）：
   - 语言清晰度：表达是否清晰易懂，避免歧义
   - 专业术语使用：行业术语使用是否准确、恰当
   - 逻辑结构：信息组织是否有条理，时间线是否清晰
   - 表达简洁性：是否简洁有力，避免冗余信息
   - 语法规范：是否存在语法错误或表达不当

2. 信息完整性（100分）：
   - 基本信息：联系方式、个人信息是否完整准确
   - 教育背景：学历、专业、院校、时间等信息完整性
   - 工作经历：职位、公司、时间、职责描述的详细程度
   - 项目经历：项目背景、技术栈、个人贡献、成果展示
   - 技能证书：专业技能、工具掌握、相关证书展示
   - 其他信息：荣誉奖项、作品集等加分项

3. 内容相关性（100分）：
   - 技能匹配度：技能与岗位要求的匹配程度
   - 经验相关性：工作经验与目标岗位的相关程度
   - 行业背景：行业经验是否符合岗位需求
   - 岗位适配性：整体背景与岗位的契合度
   - 发展潜力：是否展现出适合该岗位的成长轨迹

4. 简历专业性（100分）：
   - 整体结构：简历布局是否合理、层次分明
   - 格式规范：字体、间距、对齐等格式是否专业
   - 内容质量：描述是否具体、量化、有说服力
   - 亮点突出：是否有效突出个人优势和核心竞争力
   - 视觉效果：整体视觉呈现是否专业、易读

评价要求：
- 每个子项的评价需要具体指出优点和不足
- 提供明确的改进建议和优化方向
- 评价要客观、专业，避免空泛的表述
- 结合目标岗位要求给出针对性建议
- 评价字数要充分，每个comment至少50字以上

请严格按照以下JSON格式返回评分结果，每个维度满分100分，有一个总分和多个详细评价项：
{
  "language_expression": {
    "score": [0-100分的具体分数],
    "details": [
      {
        "title": "语言清晰度",
        "comment": "[详细评价语言表达的清晰度，包括优点、不足和具体改进建议，至少60字]"
      },
      {
        "title": "逻辑结构",
        "comment": "[详细评价信息组织的逻辑性，包括时间线、模块衔接等，至少60字]"
      },
      {
        "title": "专业术语使用",
        "comment": "[详细评价专业术语的使用情况，包括准确性和恰当性，至少60字]"
      },
      {
        "title": "表达简洁性",
        "comment": "[详细评价表达的简洁性和重点突出程度，至少60字]"
      }
    ]
  },
  "information_completeness": {
    "score": [0-100分的具体分数],
    "details": [
      {
        "title": "基本信息",
        "comment": "[详细评价基本信息的完整性和准确性，至少60字]"
      },
      {
        "title": "工作经历",
        "comment": "[详细评价工作经历的完整性和描述质量，至少60字]"
      },
      {
        "title": "项目经历",
        "comment": "[详细评价项目经历的丰富程度和描述深度，至少60字]"
      },
      {
        "title": "教育背景",
        "comment": "[详细评价教育信息的完整性，至少60字]"
      },
      {
        "title": "技能证书",
        "comment": "[详细评价技能和证书的展示情况，至少60字]"
      }
    ]
  },
  "content_relevance": {
    "score": [0-100分的具体分数],
    "details": [
      {
        "title": "技能匹配度",
        "comment": "[详细评价技能与岗位要求的匹配程度，至少60字]"
      },
      {
        "title": "经验相关性",
        "comment": "[详细评价工作经验与目标岗位的相关性，至少60字]"
      },
      {
        "title": "行业背景",
        "comment": "[详细评价行业经验的匹配度，至少60字]"
      },
      {
        "title": "岗位适配性",
        "comment": "[详细评价整体背景与岗位的契合度，至少60字]"
      }
    ]
  },
  "professionalism": {
    "score": [0-100分的具体分数],
    "details": [
      {
        "title": "整体结构",
        "comment": "[详细评价简历的结构布局和专业性，至少60字]"
      },
      {
        "title": "内容质量",
        "comment": "[详细评价内容的质量和说服力，至少60字]"
      },
      {
        "title": "格式规范",
        "comment": "[详细评价格式的规范性和视觉效果，至少60字]"
      },
      {
        "title": "亮点突出",
        "comment": "[详细评价个人亮点和核心竞争力的展现，至少60字]"
      }
    ]
  },
  "overall_score": [总体平均分],
  "overall_comment": "[综合评价，包括主要优势、需要改进的方面、具体建议，至少150字]"
}`,
		targetPosition.PositionName,
		string(resumeDataJSON),
		targetPosition.PositionName,
		targetPosition.CompanyName,
		targetPosition.JobSource,
		targetPosition.JobDescription)

	// 7. 构建AI消息
	messages := []*pkg.ChatMessage{
		{
			Role:    "system",
			Content: "你是一个专业的简历评估专家，能够客观、准确地对简历进行多维度评分。请严格按照指定的JSON格式返回评分结果。",
		},
		{
			Role:    "user",
			Content: prompt,
		},
	}

	// 8. 调用AI服务
	response, err := s.aiClient.ChatCompletion(ctx, messages)
	if err != nil {
		s.logger.Error("AI调用失败",
			zap.Uint("user_id", userID),
			zap.Uint("resume_id", resumeID),
			zap.Uint("position_id", positionID),
			zap.Error(err))
		return nil, exception.ErrInternalServer.WithDetail("AI调用失败")
	}

	// 9. 解析AI返回的JSON数据
	var scoreResponse vo_api.ScoreResumeResponse
	if err := json.Unmarshal([]byte(response.Content), &scoreResponse); err != nil {
		s.logger.Error("解析AI返回的JSON失败",
			zap.String("ai_response", response.Content),
			zap.Error(err))
		return nil, exception.ErrInternalServer.WithDetail("解析AI返回的JSON失败")
	}

	// 10. 更新AI调用记录
	callDuration := time.Since(startTime).Milliseconds()

	if err := s.aiCallRecordRepo.UpdateResponse(ctx, record.ID, response.Content, callDuration); err != nil {
		s.logger.Error("更新AI调用记录失败",
			zap.Uint("user_id", userID),
			zap.Uint("resume_id", resumeID),
			zap.Uint("position_id", positionID),
			zap.Error(err))
		// 这里不返回错误，因为主要功能已完成
	}

	// 11. 保存评分记录到数据库
	resumeScoreRecord := &models.ResumeScore{
		UserID:                       userID,
		ResumeID:                     resumeID,
		TargetPositionID:             positionID,
		LanguageExpressionScore:      scoreResponse.LanguageExpression.Score,
		InformationCompletenessScore: scoreResponse.InformationCompleteness.Score,
		ContentRelevanceScore:        scoreResponse.ContentRelevance.Score,
		ProfessionalismScore:         scoreResponse.Professionalism.Score,
		OverallScore:                 scoreResponse.OverallScore,
		OverallComment:               scoreResponse.OverallComment,
	}

	// 设置详细评分数据
	if err := resumeScoreRecord.SetLanguageExpressionDetails(convertToModelScoreDetailItems(scoreResponse.LanguageExpression.Details)); err != nil {
		s.logger.Error("设置语言与表达详细评分数据失败", zap.Error(err))
	}

	if err := resumeScoreRecord.SetInformationCompletenessDetails(convertToModelScoreDetailItems(scoreResponse.InformationCompleteness.Details)); err != nil {
		s.logger.Error("设置信息完整性详细评分数据失败", zap.Error(err))
	}

	if err := resumeScoreRecord.SetContentRelevanceDetails(convertToModelScoreDetailItems(scoreResponse.ContentRelevance.Details)); err != nil {
		s.logger.Error("设置内容相关性详细评分数据失败", zap.Error(err))
	}

	if err := resumeScoreRecord.SetProfessionalismDetails(convertToModelScoreDetailItems(scoreResponse.Professionalism.Details)); err != nil {
		s.logger.Error("设置简历专业性详细评分数据失败", zap.Error(err))
	}

	if err := s.resumeScoreRepo.Create(ctx, resumeScoreRecord); err != nil {
		s.logger.Error("保存简历评分记录失败",
			zap.Uint("user_id", userID),
			zap.Uint("resume_id", resumeID),
			zap.Uint("position_id", positionID),
			zap.Error(err))
		// 这里不返回错误，因为主要功能已完成
	}

	s.logger.Info("AI简历打分完成",
		zap.Uint("user_id", userID),
		zap.Uint("resume_id", resumeID),
		zap.Uint("position_id", positionID),
		zap.Float64("overall_score", scoreResponse.OverallScore),
		zap.Int64("call_duration", callDuration),
		zap.Uint("score_record_id", resumeScoreRecord.ID))

	return &scoreResponse, nil
}

// convertToModelScoreDetailItems 将VO的ScoreDetailItem转换为Model的ScoreDetailItem
func convertToModelScoreDetailItems(voItems []vo_api.ScoreDetailItem) []models.ScoreDetailItem {
	modelItems := make([]models.ScoreDetailItem, len(voItems))
	for i, item := range voItems {
		modelItems[i] = models.ScoreDetailItem{
			Title:   item.Title,
			Comment: item.Comment,
		}
	}
	return modelItems
}
