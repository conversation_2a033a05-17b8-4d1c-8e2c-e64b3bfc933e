package services

import (
	"context"
	"errors"
	"fmt"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
	"time"

	"go.uber.org/zap"
	"resume-server/internal/enum"
	"resume-server/internal/exception"
	"resume-server/internal/models"
	"resume-server/internal/pkg"
	"resume-server/internal/repository"
	"resume-server/internal/utils"
	"resume-server/internal/vo/vo_admin"
)

// AdminService 管理员服务接口
type AdminService interface {
	GetAdminByID(id uint) (*models.Admin, error)
	GetAdminByUsername(username string) (*models.Admin, error)
	CreateAdmin(admin *models.Admin) error
	UpdateAdmin(admin *models.Admin) error
	DeleteAdmin(id uint) error
	ListAdmins(page, pageSize int) ([]*models.Admin, int64, error)
	ValidateAdminPassword(admin *models.Admin, password string) bool
	LoginByUsernamePassword(username, password string) (*models.Admin, error)
	LoginWithToken(username, password string) (*vo_admin.AdminTokenResponse, error)
	Logout(adminID uint) error
	LogoutWithToken(adminID uint, token string) error
	IsTokenBlacklisted(token string) (bool, error)
	ChangePassword(adminID uint, oldPassword, newPassword string) error
}

// adminService 管理员服务实现
type adminService struct {
	adminRepo       repository.AdminRepository
	adminJWTService pkg.AdminJWTService
	redisClient     *redis.Client
}

// NewAdminService 创建管理员服务
func NewAdminService(adminRepo repository.AdminRepository, adminJWTService pkg.AdminJWTService, redisClient *redis.Client) AdminService {
	return &adminService{
		adminRepo:       adminRepo,
		adminJWTService: adminJWTService,
		redisClient:     redisClient,
	}
}

// GetAdminByID 根据ID获取管理员
func (s *adminService) GetAdminByID(id uint) (*models.Admin, error) {
	return s.adminRepo.GetByID(id)
}

// GetAdminByUsername 根据用户名获取管理员
func (s *adminService) GetAdminByUsername(username string) (*models.Admin, error) {
	return s.adminRepo.GetByUsername(username)
}

// CreateAdmin 创建管理员
func (s *adminService) CreateAdmin(admin *models.Admin) error {
	// 加密密码
	if admin.Password != "" {
		hashedPassword, err := utils.HashPassword(admin.Password)
		if err != nil {
			pkg.Error("密码加密失败", zap.Error(err))
			return err
		}
		admin.Password = hashedPassword
	}

	return s.adminRepo.Create(admin)
}

// UpdateAdmin 更新管理员
func (s *adminService) UpdateAdmin(admin *models.Admin) error {
	// 如果密码不为空，则加密密码
	if admin.Password != "" {
		hashedPassword, err := utils.HashPassword(admin.Password)
		if err != nil {
			pkg.Error("密码加密失败", zap.Error(err))
			return err
		}
		admin.Password = hashedPassword
	}

	return s.adminRepo.Update(admin)
}

// DeleteAdmin 删除管理员
func (s *adminService) DeleteAdmin(id uint) error {
	return s.adminRepo.Delete(id)
}

// ListAdmins 获取管理员列表
func (s *adminService) ListAdmins(page, pageSize int) ([]*models.Admin, int64, error) {
	return s.adminRepo.List(page, pageSize)
}

// ValidateAdminPassword 验证管理员密码
func (s *adminService) ValidateAdminPassword(admin *models.Admin, password string) bool {
	return utils.CheckPassword(admin.Password, password)
}

// LoginByUsernamePassword 通过用户名和密码登录
func (s *adminService) LoginByUsernamePassword(username, password string) (*models.Admin, error) {
	// 根据用户名获取管理员
	admin, err := s.adminRepo.GetByUsername(username)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			pkg.Warn("管理员不存在", zap.String("username", username))
			return nil, exception.ErrAdminNotFound
		}
		pkg.Error("查询管理员失败", zap.String("username", username), zap.Error(err))
		return nil, exception.ErrAdminQueryFailed.WithDetail(err.Error())
	}

	// 验证密码
	if !s.ValidateAdminPassword(admin, password) {
		pkg.Warn("管理员密码错误", zap.String("username", username))
		return nil, exception.ErrAdminPasswordIncorrect
	}

	pkg.Info("管理员登录成功", zap.String("username", username), zap.Uint("adminId", admin.ID))
	return admin, nil
}

// LoginWithToken 通过用户名和密码登录并生成token
func (s *adminService) LoginWithToken(username, password string) (*vo_admin.AdminTokenResponse, error) {
	// 验证用户名和密码
	admin, err := s.LoginByUsernamePassword(username, password)
	if err != nil {
		// LoginByUsernamePassword已经返回了具体的错误，直接返回
		return nil, err
	}

	// 检查管理员状态
	if admin.Status != enum.AdminStatusEnabled {
		pkg.Warn("管理员账号已被禁用", zap.String("username", username), zap.Uint("adminId", admin.ID))
		return nil, exception.ErrAdminDisabled
	}

	// 生成JWT token
	token, err := s.adminJWTService.GenerateToken(admin.ID)
	if err != nil {
		pkg.Error("生成管理员JWT Token失败", zap.Error(err))
		return nil, exception.ErrAdminTokenGenFailed.WithDetail(err.Error())
	}

	// 构建响应数据
	tokenResponse := &vo_admin.AdminTokenResponse{
		Token: token,
	}

	return tokenResponse, nil
}

// Logout 管理员退出登录
func (s *adminService) Logout(adminID uint) error {
	pkg.Info("管理员退出登录", zap.Uint("adminId", adminID))
	return nil
}

// LogoutWithToken 管理员退出登录并将token加入黑名单
func (s *adminService) LogoutWithToken(adminID uint, token string) error {
	ctx := context.Background()

	// 解析token获取过期时间
	claims, err := s.adminJWTService.ParseToken(token)
	if err != nil {
		pkg.Error("解析管理员token失败", zap.Error(err))
		return exception.ErrAdminTokenGenFailed.WithDetail("无效的token")
	}

	// 计算token剩余有效时间
	expirationTime := s.adminJWTService.GetExpirationTime(claims)
	now := time.Now()

	// 如果token已经过期，无需加入黑名单
	if expirationTime.Before(now) {
		pkg.Info("管理员token已过期，无需加入黑名单", zap.Uint("adminId", adminID))
		return nil
	}

	// 计算剩余有效时间
	remainingTime := expirationTime.Sub(now)

	// 将token加入Redis黑名单，设置过期时间为token的剩余有效时间
	blacklistKey := fmt.Sprintf("admin_token_blacklist:%s", token)
	err = s.redisClient.Set(ctx, blacklistKey, adminID, remainingTime).Err()
	if err != nil {
		pkg.Error("将管理员token加入黑名单失败", zap.Error(err), zap.Uint("adminId", adminID))
		return exception.ErrInternalServer.WithDetail("退出登录失败")
	}

	pkg.Info("管理员退出登录成功，token已加入黑名单",
		zap.Uint("adminId", adminID),
		zap.Duration("remainingTime", remainingTime))

	return nil
}

// IsTokenBlacklisted 检查token是否在黑名单中
func (s *adminService) IsTokenBlacklisted(token string) (bool, error) {
	ctx := context.Background()
	blacklistKey := fmt.Sprintf("admin_token_blacklist:%s", token)

	exists, err := s.redisClient.Exists(ctx, blacklistKey).Result()
	if err != nil {
		pkg.Error("检查管理员token黑名单失败", zap.Error(err))
		return false, err
	}

	return exists > 0, nil
}

// ChangePassword 修改管理员密码
func (s *adminService) ChangePassword(adminID uint, oldPassword, newPassword string) error {
	// 获取管理员信息
	admin, err := s.adminRepo.GetByID(adminID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			pkg.Warn("管理员不存在", zap.Uint("adminId", adminID))
			return exception.ErrAdminNotFound
		}
		pkg.Error("查询管理员失败", zap.Uint("adminId", adminID), zap.Error(err))
		return exception.ErrAdminQueryFailed.WithDetail(err.Error())
	}

	// 验证旧密码
	if !s.ValidateAdminPassword(admin, oldPassword) {
		pkg.Warn("管理员旧密码错误", zap.Uint("adminId", adminID))
		return exception.ErrInvalidParam.WithMessage("旧密码错误")
	}

	// 检查新密码是否与旧密码相同
	if oldPassword == newPassword {
		pkg.Warn("新密码与旧密码相同", zap.Uint("adminId", adminID))
		return exception.ErrInvalidParam.WithMessage("新密码不能与旧密码相同")
	}

	// 加密新密码
	hashedNewPassword, err := utils.HashPassword(newPassword)
	if err != nil {
		pkg.Error("新密码加密失败", zap.Uint("adminId", adminID), zap.Error(err))
		return exception.ErrInternalServer.WithMessage("密码修改失败")
	}

	// 更新密码
	admin.Password = hashedNewPassword
	if err := s.adminRepo.Update(admin); err != nil {
		pkg.Error("更新管理员密码失败", zap.Uint("adminId", adminID), zap.Error(err))
		return exception.ErrAdminUpdateFailed.WithMessage("密码修改失败")
	}

	pkg.Info("管理员密码修改成功", zap.Uint("adminId", adminID))
	return nil
}
