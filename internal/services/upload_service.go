package services

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"io"
	"path/filepath"
	"strings"
	"time"

	"go.uber.org/zap"
	"mime/multipart"
	"resume-server/internal/exception"
	"resume-server/internal/pkg"
)

// UploadService 上传服务接口
type UploadService interface {
	// UploadAvatar 上传头像
	UploadAvatar(ctx context.Context, file *multipart.FileHeader) (fileURL string, originalFilename string, err error)
	// UploadAttachment 上传附件
	UploadAttachment(ctx context.Context, file *multipart.FileHeader) (fileURL string, originalFilename string, err error)
}

// uploadService 上传服务实现
type uploadService struct {
	ossService pkg.OSSService
	logger     *zap.Logger
}

// NewUploadService 创建上传服务
func NewUploadService(ossService pkg.OSSService, logger *zap.Logger) UploadService {
	return &uploadService{
		ossService: ossService,
		logger:     logger,
	}
}

// UploadAvatar 上传头像
func (s *uploadService) UploadAvatar(ctx context.Context, file *multipart.FileHeader) (fileURL string, originalFilename string, err error) {
	// 检查文件大小（最大7MB）
	if file.Size > 7*1024*1024 {
		s.logger.Warn("文件过大", zap.Int64("size", file.Size))
		return "", "", exception.ErrFileTooLarge.WithMessage("文件过大，最大支持7MB")
	}

	// 获取文件扩展名
	ext := strings.ToLower(filepath.Ext(file.Filename))

	// 检查文件类型
	allowedExts := map[string]bool{
		".jpg":  true,
		".jpeg": true,
		".png":  true,
		".webp": true,
	}

	if !allowedExts[ext] {
		s.logger.Warn("不支持的文件类型", zap.String("ext", ext))
		return "", "", exception.ErrFileTypeNotSupported.WithMessage("不支持的文件类型，仅支持jpg、png、webp格式")
	}

	// 处理文件上传
	fileURL, err = s.processFileUpload(ctx, file, "avatar")
	if err != nil {
		return "", "", err
	}

	// 返回原始文件名
	originalFilename = file.Filename

	return fileURL, originalFilename, nil
}

// UploadAttachment 上传附件
func (s *uploadService) UploadAttachment(ctx context.Context, file *multipart.FileHeader) (fileURL string, originalFilename string, err error) {
	// 检查文件大小（最大20MB）
	if file.Size > 20*1024*1024 {
		s.logger.Warn("文件过大", zap.Int64("size", file.Size))
		return "", "", exception.ErrFileTooLarge.WithMessage("文件过大，最大支持20MB")
	}

	// 获取文件扩展名
	ext := strings.ToLower(filepath.Ext(file.Filename))

	// 检查文件类型
	allowedExts := map[string]bool{
		".pdf":  true,
		".doc":  true,
		".docx": true,
		".jpg":  true,
		".jpeg": true,
		".png":  true,
		".zip":  true,
		".rar":  true,
	}

	if !allowedExts[ext] {
		s.logger.Warn("不支持的文件类型", zap.String("ext", ext))
		return "", "", exception.ErrFileTypeNotSupported.WithMessage("不支持的文件类型，仅支持PDF、DOC、DOCX、JPG、PNG、ZIP、RAR等格式")
	}

	// 处理文件上传
	fileURL, err = s.processFileUpload(ctx, file, "attachment")
	if err != nil {
		return "", "", err
	}

	// 返回原始文件名
	originalFilename = file.Filename

	return fileURL, originalFilename, nil
}

// processFileUpload 处理文件上传的通用逻辑
func (s *uploadService) processFileUpload(ctx context.Context, file *multipart.FileHeader, directory string) (fileURL string, err error) {
	// 打开文件
	src, err := file.Open()
	if err != nil {
		s.logger.Error("打开文件失败", zap.Error(err))
		return "", exception.ErrFileOpenFailed
	}
	defer src.Close()

	// 读取文件内容
	fileBytes, err := io.ReadAll(src)
	if err != nil {
		s.logger.Error("读取文件内容失败", zap.Error(err))
		return "", exception.ErrFileReadFailed
	}

	// 生成文件名，使用MD5哈希原始文件名并添加时间戳确保唯一性
	fileBase := filepath.Base(file.Filename)
	fileExt := filepath.Ext(fileBase)

	// 计算原始文件名的MD5哈希
	hasher := md5.New()
	hasher.Write([]byte(fileBase))
	md5FileName := hex.EncodeToString(hasher.Sum(nil))

	// 生成唯一文件名：md5(原始文件名)_时间戳.ext
	objectName := fmt.Sprintf("%s/%s_%d%s", directory, md5FileName, time.Now().UnixNano(), fileExt)

	// 获取内容类型
	contentType := file.Header.Get("Content-Type")

	// 上传文件到OSS
	fileURL, err = s.ossService.UploadBytes(ctx, fileBytes, objectName, contentType)
	if err != nil {
		s.logger.Error("上传文件失败", zap.Error(err))
		return "", exception.ErrFileUploadFailed
	}

	return fileURL, nil
}
