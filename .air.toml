# .air.toml 配置文件
root = "."
tmp_dir = "tmp"

[build]
# 只需要监听go文件的变化
include_ext = ["go", "swagger"]
# 二进制文件名
bin = "./tmp/main"
# 自定义编译命令，根据环境变量决定是否生成Swagger文档
cmd = "export SWAGGER_ENABLED=$(grep -E '^SWAGGER_ENABLE=(true|false)' .env | cut -d= -f2) && if [ \"$SWAGGER_ENABLED\" = \"true\" ]; then swag init -g main.go; fi && go build -o ./tmp/main ."
# 监控这些文件夹下的文件变化
include_dir = ["internal", "cmd", "config"]
# 忽略这些文件夹下的文件变化
exclude_dir = ["tmp", "vendor", "logs", "tests"]
# 忽略这些文件
exclude_file = []
# 自定义执行结束命令
delay = 1000
# 自定义环境变量
env = []
# 使用格式化工具
[build.lint]
enabled = true
cmd = "golangci-lint run"

[color]
main = "magenta"
watcher = "cyan"
build = "yellow"
runner = "green"

[log]
time = true

[misc]
# 触发构建的文件变化
clean_on_exit = true 